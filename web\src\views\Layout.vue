<template>
  <div class="layout-container">
    <el-container class="full-height">
      <!-- 侧边栏 -->
      <el-aside width="200px" class="sidebar">
        <div class="logo">
          <h3>Auto.js群控</h3>
        </div>

        <el-menu
          :default-active="$route.path"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-menu-item index="/dashboard">
            <i class="el-icon-odometer"></i>
            <span>仪表盘</span>
          </el-menu-item>

          <el-menu-item index="/devices">
            <i class="el-icon-mobile-phone"></i>
            <span>设备管理</span>
          </el-menu-item>

          <el-menu-item index="/scripts">
            <i class="el-icon-document"></i>
            <span>脚本管理</span>
          </el-menu-item>

          <el-menu-item index="/script-config">
            <i class="el-icon-setting"></i>
            <span>脚本配置执行</span>
          </el-menu-item>

          <el-menu-item index="/files">
            <i class="el-icon-folder"></i>
            <span>文件管理</span>
          </el-menu-item>

          <el-menu-item index="/logs">
            <i class="el-icon-tickets"></i>
            <span>执行日志</span>
          </el-menu-item>

          <el-menu-item index="/xiaohongshu">
            <i class="el-icon-star-on"></i>
            <span>小红书自动化</span>
          </el-menu-item>

          <el-menu-item index="/xiaohongshu-logs">
            <i class="el-icon-notebook-1"></i>
            <span>小红书执行日志</span>
          </el-menu-item>

          <el-menu-item index="/xianyu">
            <i class="el-icon-fish"></i>
            <span>闲鱼自动化</span>
          </el-menu-item>

          <el-menu-item index="/xianyu-logs">
            <i class="el-icon-document-copy"></i>
            <span>闲鱼执行日志</span>
          </el-menu-item>

          <!-- 管理员菜单 -->
          <el-submenu v-if="user && user.role === 'admin'" index="admin">
            <template slot="title">
              <i class="el-icon-s-tools"></i>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/admin/activation-codes">
              <i class="el-icon-key"></i>
              <span>卡密管理</span>
            </el-menu-item>
            <el-menu-item index="/admin/users">
              <i class="el-icon-user-solid"></i>
              <span>用户管理</span>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <span class="page-title">{{ $route.meta.title || '控制台' }}</span>
          </div>

          <div class="header-right">
            <!-- 连接状态 -->
            <el-tag
              :type="connected ? 'success' : 'danger'"
              size="small"
              class="connection-status"
            >
              {{ connected ? '已连接' : '未连接' }}
            </el-tag>

            <!-- 用户菜单 -->
            <el-dropdown @command="handleCommand">
              <span class="user-dropdown">
                <i class="el-icon-user"></i>
                {{ user.username }}
                <i class="el-icon-arrow-down"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  computed: {
    user() {
      return this.$store.getters['auth/user']
    },
    connected() {
      return this.$store.getters['socket/connected']
    }
  },
  methods: {
    handleCommand(command) {
      if (command === 'logout') {
        this.handleLogout()
      }
    },

    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.$store.dispatch('auth/logout')
        this.$router.push('/login')
        this.$message.success('已退出登录')

      } catch (error) {
        // 用户取消
      }
    }
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: white;
  margin-bottom: 0;
}

.logo h3 {
  margin: 0;
  font-size: 16px;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left .page-title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.connection-status {
  margin-right: 10px;
}

.user-dropdown {
  cursor: pointer;
  color: #606266;
  font-size: 14px;
}

.user-dropdown:hover {
  color: #409EFF;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
}
</style>
