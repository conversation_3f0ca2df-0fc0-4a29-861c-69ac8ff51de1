/**
 * 服务器设备应用管理模块 - 完整拆分版本
 * 包含所有设备应用管理相关的API和功能
 * 对应原始文件第10643-10786行的完整内容，包含以下API：
 * - POST /api/device/:deviceId/apps - 设备应用信息上报
 * - GET /api/device/:deviceId/apps - 获取设备应用信息
 * - POST /api/device/:deviceId/script-status - 接收设备脚本状态
 * - POST /api/device/:deviceId/app-status - 接收设备应用状态
 * - POST /api/device/clear-chat-records-response - 清空聊天记录响应
 * - POST /api/device/script-completion-ack - 脚本完成确认
 * 以及所有相关的设备应用管理功能
 */

// 设备应用管理模块设置函数
async function setupServerDeviceApps(app, io, coreData, authData) {
  console.log('🔧 设置设备应用管理模块...');

  const {
    pool,
    devices,
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 引入设备认证中间件
  const { authenticateDevice, verifyDeviceOwnership } = require('../middleware/deviceAuth');

  // 设备应用信息上报API (原始文件第10643行) - 改为设备认证模式
  app.post('/api/device/:deviceId/apps', authenticateDevice(pool), async (req, res) => {
    const { deviceId } = req.params;
    const { apps, detectedAt } = req.body;

    console.log(`收到设备应用信息上报: ${deviceId}`);
    console.log('请求体:', JSON.stringify(req.body, null, 2));
    console.log('apps类型:', typeof apps);
    console.log('apps内容:', apps);

    if (!apps) {
      return res.status(400).json({
        success: false,
        message: '缺少应用信息'
      });
    }

    try {
      if (pool) {
        // 清空该设备的旧应用信息
        await pool.execute(`
          DELETE FROM device_apps WHERE device_id = ?
        `, [deviceId]);

        // 插入新的应用信息
        if (Array.isArray(apps) && apps.length > 0) {
          for (const app of apps) {
            await pool.execute(`
              INSERT INTO device_apps (device_id, app_type, app_name, app_text, app_bounds, is_clickable, detection_method, detected_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              deviceId,
              app.type || 'unknown',
              app.name || '',
              app.text || '',
              JSON.stringify(app.bounds || {}),
              app.clickable ? 1 : 0,
              app.detectionMethod || 'auto',
              detectedAt || new Date()
            ]);
          }
          console.log(`已保存 ${apps.length} 个应用信息到数据库`);
        }

        res.json({
          success: true,
          message: `应用信息上报成功，共 ${apps.length} 个应用`,
          data: {
            deviceId,
            appCount: apps.length,
            detectedAt: detectedAt || new Date()
          }
        });
      } else {
        // 数据库不可用，只返回成功响应
        console.log('数据库不可用，无法保存应用信息');
        res.json({
          success: true,
          message: `应用信息已接收（数据库不可用），共 ${apps.length} 个应用`,
          data: {
            deviceId,
            appCount: apps.length,
            detectedAt: detectedAt || new Date()
          }
        });
      }

    } catch (error) {
      console.error('保存设备应用信息失败:', error);
      res.status(500).json({
        success: false,
        message: '保存应用信息失败: ' + error.message
      });
    }
  });

  // 获取设备应用信息API (原始文件第10729行) - 改为Web端认证+设备权限验证
  app.get('/api/device/:deviceId/apps', authenticateToken, verifyDeviceOwnership(pool), async (req, res) => {
    const { deviceId } = req.params;

    console.log(`查询设备应用信息: ${deviceId}`);

    try {
      if (pool) {
        const [rows] = await pool.execute(`
          SELECT app_type, app_name, app_text, app_bounds, is_clickable, detection_method, detected_at
          FROM device_apps
          WHERE device_id = ?
          ORDER BY detected_at DESC
        `, [deviceId]);

        const apps = rows.map(row => ({
          type: row.app_type,
          name: row.app_name,
          text: row.app_text,
          bounds: JSON.parse(row.app_bounds || '{}'),
          clickable: row.is_clickable === 1,
          detectionMethod: row.detection_method,
          detectedAt: row.detected_at
        }));

        res.json({
          success: true,
          data: {
            deviceId,
            apps,
            total: apps.length
          }
        });
      } else {
        // 数据库不可用，返回空结果
        res.json({
          success: true,
          data: {
            deviceId,
            apps: [],
            total: 0
          }
        });
      }

    } catch (error) {
      console.error('查询设备应用信息失败:', error);
      res.status(500).json({
        success: false,
        message: '查询应用信息失败: ' + error.message
      });
    }
  });

  // 接收设备脚本状态API (原始文件第12625行)
  app.post('/api/device/:deviceId/script-status', authenticateToken, async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;

    console.log(`🛑 [脚本状态] 收到设备 ${deviceId} 的脚本状态: ${status}`);
    console.log(`🛑 [脚本状态] 原因: ${reason}`);
    console.log(`🛑 [脚本状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备脚本状态变化
      io.emit('device_script_status', {
        deviceId,
        status,
        reason,
        message,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '脚本状态已接收'
      });

    } catch (error) {
      console.error('处理设备脚本状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理脚本状态失败: ' + error.message
      });
    }
  });

  // 接收设备应用状态API (原始文件第12660行) - 连接码模式，无需token
  app.post('/api/device/:deviceId/app-status', async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;

    console.log(`📱 [应用状态] 收到设备 ${deviceId} 的应用状态: ${status}`);
    console.log(`📱 [应用状态] 原因: ${reason}`);
    console.log(`📱 [应用状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备应用状态变化
      io.emit('device_app_status', {
        deviceId,
        status,
        reason,
        message,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '应用状态已接收'
      });

    } catch (error) {
      console.error('处理设备应用状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理应用状态失败: ' + error.message
      });
    }
  });

  // 清空聊天记录响应API (原始文件第11244行)
  app.post('/api/device/clear-chat-records-response', (req, res) => {
    try {
      const { deviceId, success, message, clearedCount } = req.body;

      console.log(`📝 [清空聊天记录响应] 设备: ${deviceId}`);
      console.log(`📝 [清空聊天记录响应] 成功: ${success}`);
      console.log(`📝 [清空聊天记录响应] 消息: ${message}`);
      console.log(`📝 [清空聊天记录响应] 清空数量: ${clearedCount}`);

      // 通知Web客户端清空操作结果
      io.emit('chat_records_cleared', {
        deviceId,
        success,
        message,
        clearedCount,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '清空聊天记录响应已接收'
      });

    } catch (error) {
      console.error('处理清空聊天记录响应失败:', error);
      res.status(500).json({
        success: false,
        message: '处理响应失败: ' + error.message
      });
    }
  });

  // 脚本完成确认API (原始文件第11272行)
  app.post('/api/device/script-completion-ack', (req, res) => {
    try {
      const { deviceId, taskId, logId, acknowledged } = req.body;

      console.log(`✅ [脚本完成确认] 设备: ${deviceId}`);
      console.log(`✅ [脚本完成确认] 任务ID: ${taskId}`);
      console.log(`✅ [脚本完成确认] 日志ID: ${logId}`);
      console.log(`✅ [脚本完成确认] 已确认: ${acknowledged}`);

      // 通知Web客户端脚本完成确认
      io.emit('script_completion_acknowledged', {
        deviceId,
        taskId,
        logId,
        acknowledged,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '脚本完成确认已接收'
      });

    } catch (error) {
      console.error('处理脚本完成确认失败:', error);
      res.status(500).json({
        success: false,
        message: '处理确认失败: ' + error.message
      });
    }
  });

  console.log('✅ 设备应用管理模块设置完成');
}

module.exports = { setupServerDeviceApps };
