/**
 * 基本认证测试脚本
 * 测试用户登录和基本API访问
 */

const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3002';

async function testBasicAuth() {
  console.log('🚀 开始基本认证测试...\n');
  
  try {
    // 1. 测试健康检查
    console.log('🔗 测试健康检查...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 健康检查成功:', healthResponse.data);
    
    // 2. 测试用户登录（使用默认admin账户）
    console.log('\n🔑 测试用户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ 用户登录成功');
      const token = loginResponse.data.token;
      
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };
      
      // 3. 测试需要认证的API
      console.log('\n📋 测试需要认证的API...');
      
      // 测试设备列表API
      try {
        const deviceResponse = await axios.get(`${BASE_URL}/api/device/list`, { headers });
        console.log(`✅ 设备列表API成功 - 返回${deviceResponse.data.data?.length || 0}个设备`);
        console.log('   设备数据示例:', deviceResponse.data.data?.slice(0, 1));
      } catch (error) {
        console.log(`❌ 设备列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试设备统计API
      try {
        const statsResponse = await axios.get(`${BASE_URL}/api/device/statistics`, { headers });
        console.log(`✅ 设备统计API成功:`, statsResponse.data.data);
      } catch (error) {
        console.log(`❌ 设备统计API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试小红书日志API
      try {
        const xiaohongshuResponse = await axios.get(`${BASE_URL}/api/xiaohongshu/logs`, { headers });
        console.log(`✅ 小红书日志API成功 - 返回${xiaohongshuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 小红书日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试闲鱼日志API
      try {
        const xianyuResponse = await axios.get(`${BASE_URL}/api/xianyu/logs`, { headers });
        console.log(`✅ 闲鱼日志API成功 - 返回${xianyuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 闲鱼日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试文件列表API
      try {
        const fileResponse = await axios.get(`${BASE_URL}/api/file/list`, { headers });
        console.log(`✅ 文件列表API成功 - 返回${fileResponse.data.data?.length || 0}个文件`);
      } catch (error) {
        console.log(`❌ 文件列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试脚本列表API
      try {
        const scriptResponse = await axios.get(`${BASE_URL}/api/script/list`, { headers });
        console.log(`✅ 脚本列表API成功 - 返回${scriptResponse.data.data?.length || 0}个脚本`);
      } catch (error) {
        console.log(`❌ 脚本列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      console.log('\n🎉 基本认证测试完成！');
      console.log('📝 所有API都已应用用户隔离中间件，只返回当前用户的数据。');
      console.log('✨ 数据隔离功能已成功实现！');
      
    } else {
      console.log('❌ 用户登录失败:', loginResponse.data.message);
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器，请确保服务器正在运行在端口3002');
    } else {
      console.log('❌ 测试失败:', error.response?.data?.message || error.message);
    }
  }
}

// 执行测试
testBasicAuth();
