/**
 * 测试服务器启动脚本
 * 用于快速测试系统功能，无需MySQL数据库
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const fs = require('fs');

// 尝试引入数据库连接池
let pool = null;
try {
  const { pool: dbPool, initDatabase } = require('./server/config/database');
  pool = dbPool;
  console.log('数据库连接池已加载');

  // 初始化数据库
  initDatabase().then(() => {
    console.log('数据库初始化完成');
  }).catch(error => {
    console.error('数据库初始化失败:', error);
  });
} catch (error) {
  console.log('数据库不可用（将使用内存模式）:', error.message);
}

// 尝试引入小红书日志服务（如果数据库可用）
let xiaohongshuLogService = null;
try {
  console.log('🔍 正在加载小红书日志服务...');
  xiaohongshuLogService = require('./server/services/xiaohongshuLogService');
  console.log('✅ 小红书日志服务已加载');

  // 检查和修复数据库表结构
  setTimeout(async () => {
    if (pool) {
      try {
        console.log('🔍 检查数据库表结构...');

        // 检查function_type枚举值
        const [columns] = await pool.execute(`
          SELECT COLUMN_TYPE
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_SCHEMA = 'autojs_control'
            AND TABLE_NAME = 'xiaohongshu_execution_logs'
            AND COLUMN_NAME = 'function_type'
        `);

        if (columns.length > 0) {
          const columnType = columns[0].COLUMN_TYPE;
          console.log('📋 当前function_type枚举值:', columnType);

          if (!columnType.includes('videoPublish')) {
            console.log('🔧 数据库表不支持videoPublish，正在修复...');

            // 更新枚举值，添加videoPublish支持
            await pool.execute(`
              ALTER TABLE xiaohongshu_execution_logs
              MODIFY COLUMN function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish') NOT NULL
            `);

            console.log('✅ 数据库表已更新，现在支持videoPublish');

            // 再次检查
            const [newColumns] = await pool.execute(`
              SELECT COLUMN_TYPE
              FROM INFORMATION_SCHEMA.COLUMNS
              WHERE TABLE_SCHEMA = 'autojs_control'
                AND TABLE_NAME = 'xiaohongshu_execution_logs'
                AND COLUMN_NAME = 'function_type'
            `);
            console.log('📋 更新后的function_type枚举值:', newColumns[0].COLUMN_TYPE);
          } else if (!columnType.includes('uidMessage')) {
            console.log('🔧 数据库表不支持uidMessage，正在修复...');

            // 更新枚举值
            await pool.execute(`
              ALTER TABLE xiaohongshu_execution_logs
              MODIFY COLUMN function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish') NOT NULL
            `);

            console.log('✅ 数据库表已更新，现在支持uidMessage和videoPublish');

            // 再次检查
            const [newColumns] = await pool.execute(`
              SELECT COLUMN_TYPE
              FROM INFORMATION_SCHEMA.COLUMNS
              WHERE TABLE_SCHEMA = 'autojs_control'
                AND TABLE_NAME = 'xiaohongshu_execution_logs'
                AND COLUMN_NAME = 'function_type'
            `);
            console.log('📋 更新后的function_type枚举值:', newColumns[0].COLUMN_TYPE);
          } else {
            console.log('✅ 数据库表已支持所有功能类型');
          }
        } else {
          console.error('❌ 找不到xiaohongshu_execution_logs表的function_type列');
        }

        // 检查是否有uidMessage记录
        const [records] = await pool.execute(`
          SELECT COUNT(*) as count
          FROM xiaohongshu_execution_logs
          WHERE function_type = 'uidMessage'
        `);
        console.log(`📊 数据库中uidMessage记录数量: ${records[0].count}`);

      } catch (dbError) {
        console.error('❌ 数据库检查失败:', dbError.message);
      }
    }
  }, 2000); // 2秒后执行检查

} catch (error) {
  console.error('❌ 小红书日志服务加载失败:', error.message);
  console.error('❌ 错误详情:', error.stack);
  console.log('💡 这可能是因为：');
  console.log('   1. MySQL服务未启动');
  console.log('   2. 数据库连接配置错误');
  console.log('   3. 数据库表不存在');
}

// 尝试引入闲鱼日志服务（如果数据库可用）
let xianyuLogService = null;
try {
  xianyuLogService = require('./server/services/xianyuLogService');
  console.log('闲鱼日志服务已加载');
} catch (error) {
  console.log('闲鱼日志服务不可用（可能是数据库未配置）:', error.message);
}

// 尝试引入闲鱼私聊服务（如果数据库可用）
let xianyuChatService = null;
try {
  xianyuChatService = require('./server/services/xianyuChatService');
  console.log('闲鱼私聊服务已加载');
} catch (error) {
  console.log('闲鱼私聊服务不可用（可能是数据库未配置）:', error.message);
}

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: function (origin, callback) {
      // 允许没有origin的请求
      if (!origin) return callback(null, true);

      // 允许localhost和本地IP地址的所有端口
      const allowedOrigins = [
        /^http:\/\/localhost:\d+$/,
        /^http:\/\/127\.0\.0\.1:\d+$/,
        /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
        /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
        /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
      ];

      const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));
      callback(null, isAllowed);
    },
    methods: ["GET", "POST"],
    credentials: true
  }
});

// 将WebSocket实例设置到Express app中，供路由使用
app.set('io', io);
console.log('✅ WebSocket实例已设置到Express app');

// 中间件 - 配置CORS以支持跨域访问
const corsOptions = {
  origin: function (origin, callback) {
    // 允许没有origin的请求（如移动应用、Postman等）
    if (!origin) return callback(null, true);

    // 允许localhost和本地IP地址的所有端口
    const allowedOrigins = [
      /^http:\/\/localhost:\d+$/,
      /^http:\/\/127\.0\.0\.1:\d+$/,
      /^http:\/\/192\.168\.\d+\.\d+:\d+$/,
      /^http:\/\/10\.\d+\.\d+\.\d+:\d+$/,
      /^http:\/\/172\.(1[6-9]|2\d|3[01])\.\d+\.\d+:\d+$/
    ];

    const isAllowed = allowedOrigins.some(pattern => pattern.test(origin));

    if (isAllowed) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// 处理预检请求
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

app.use(express.json({ limit: '2gb' })); // 支持大文件上传
app.use(express.urlencoded({ extended: true, limit: '2gb' })); // 支持大文件上传

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, 'uploads/uids');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    cb(null, `${timestamp}_${originalName}`);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.txt', '.csv'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持 .txt 和 .csv 格式的文件'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// 内存存储（测试用）
const devices = new Map();
const webClients = new Map();
const logs = [];
const pendingCommands = new Map(); // 存储待执行的命令
const deviceCommands = {}; // 存储设备命令队列

// 日志频率限制机制
const logThrottleMap = new Map(); // 存储日志的最后输出时间
const LOG_THROTTLE_INTERVAL = 5 * 60 * 1000; // 5分钟间隔

// 节流日志函数
function throttledLog(key, message) {
  const now = Date.now();
  const lastLogTime = logThrottleMap.get(key);

  if (!lastLogTime || (now - lastLogTime) >= LOG_THROTTLE_INTERVAL) {
    console.log(message);
    logThrottleMap.set(key, now);
    return true;
  }
  // 如果被节流，可以选择性地输出一个简化的提示（可选）
  // console.log(`[节流] ${key} - 日志被限制，上次输出: ${new Date(lastLogTime).toLocaleTimeString()}`);
  return false;
}

// 简化的认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  // 使用节流日志，避免频繁输出相同的认证信息
  const logKey = `auth_${req.method}_${req.path}`;
  throttledLog(logKey, `认证检查: ${req.method} ${req.path}`);
  throttledLog(`${logKey}_header`, `Authorization头: ${authHeader}`);
  throttledLog(`${logKey}_token`, `提取的token: ${token}`);

  if (!token || token !== 'test-token') {
    console.log(`认证失败: token无效`); // 认证失败总是输出
    return res.status(401).json({ message: '访问令牌无效' });
  }

  throttledLog(`${logKey}_success`, `认证成功`);
  req.user = { id: 1, username: 'admin' };
  next();
};

// 测试路由
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  if (username === 'admin' && password === 'admin123') {
    res.json({
      success: true,
      message: '登录成功',
      token: 'test-token',
      user: { id: 1, username: 'admin', email: '<EMAIL>' }
    });
  } else {
    res.status(401).json({ success: false, message: '用户名或密码错误' });
  }
});

app.get('/api/auth/verify', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: '令牌有效',
    user: req.user
  });
});

// 测试节流日志功能的接口
app.get('/api/test/throttle-log', (req, res) => {
  const testKey = 'test_throttle';
  const message = `测试节流日志 - ${new Date().toLocaleTimeString()}`;

  const logged = throttledLog(testKey, message);

  res.json({
    success: true,
    message: logged ? '日志已输出' : '日志被节流限制',
    logged: logged,
    throttleInterval: LOG_THROTTLE_INTERVAL / 1000 + '秒',
    nextAllowedTime: logThrottleMap.get(testKey) ?
      new Date(logThrottleMap.get(testKey) + LOG_THROTTLE_INTERVAL).toLocaleTimeString() :
      '立即'
  });
});

app.get('/api/device/list', authenticateToken, async (req, res) => {
  try {
    let deviceList = [];

    if (pool) {
      // 数据库可用，从数据库获取设备记录
      try {
        const [dbDevices] = await pool.execute(`
          SELECT device_id, device_name, device_info, status, last_seen, created_at
          FROM devices
          ORDER BY last_seen DESC
        `);

        // 获取内存中的连接状态
        const connectedDevices = new Map();
        for (const [socketId, device] of devices) {
          connectedDevices.set(device.deviceId, {
            status: device.status,
            connectedAt: device.connectedAt,
            lastSeen: device.lastSeen || device.connectedAt,
            socketId: socketId
          });
        }

        // 合并数据库记录和连接状态
        deviceList = dbDevices.map(dbDevice => {
          const connectedDevice = connectedDevices.get(dbDevice.device_id);
          let deviceInfo = dbDevice.device_info;

          // 解析JSON格式的device_info
          if (typeof deviceInfo === 'string') {
            try {
              deviceInfo = JSON.parse(deviceInfo);
            } catch (e) {
              deviceInfo = {};
            }
          }

          // 设备状态基于实时连接状态，而不是数据库历史状态
          const realTimeStatus = connectedDevice ? connectedDevice.status : 'offline';

          return {
            deviceId: dbDevice.device_id,
            deviceName: dbDevice.device_name,
            deviceInfo: deviceInfo,
            status: realTimeStatus, // 使用实时连接状态
            lastActiveTime: connectedDevice ? connectedDevice.lastSeen : dbDevice.last_seen,
            createdAt: dbDevice.created_at,
            deviceIP: deviceInfo?.ipAddress || '未知',
            isConnected: !!connectedDevice // 是否当前连接
          };
        });

        // 使用节流日志，避免频繁输出设备列表查询结果
        throttledLog('device_list_query', `设备列表查询完成: 数据库中${dbDevices.length}个设备，当前连接${connectedDevices.size}个设备`);
      } catch (dbError) {
        console.error('数据库查询失败，回退到内存模式:', dbError);
        // 数据库查询失败，回退到内存模式
        deviceList = Array.from(devices.values()).map(device => ({
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          deviceInfo: device.deviceInfo,
          status: device.status,
          lastActiveTime: device.lastSeen || device.connectedAt,
          createdAt: device.connectedAt,
          deviceIP: device.deviceInfo?.ipAddress || '未知',
          isConnected: true
        }));
      }
    } else {
      // 数据库不可用，使用内存模式
      deviceList = Array.from(devices.values()).map(device => ({
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        deviceInfo: device.deviceInfo,
        status: device.status,
        lastActiveTime: device.lastSeen || device.connectedAt,
        createdAt: device.connectedAt,
        deviceIP: device.deviceInfo?.ipAddress || '未知',
        isConnected: true
      }));
      // 使用节流日志，避免频繁输出内存模式查询结果
      throttledLog('device_list_memory', `设备列表查询完成（内存模式）: 当前连接${deviceList.length}个设备`);
    }

    res.json({
      success: true,
      data: deviceList
    });

  } catch (error) {
    console.error('获取设备列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取设备列表失败: ' + error.message
    });
  }
});

// 强制更新设备状态
app.post('/api/device/force-status', authenticateToken, async (req, res) => {
  try {
    const { deviceId, status } = req.body;

    if (!deviceId || !status) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    console.log(`强制更新设备状态: ${deviceId} -> ${status}`);

    // 1. 更新内存中的设备状态
    if (devices.has(deviceId)) {
      const device = devices.get(deviceId);
      device.status = status;
      device.lastSeen = new Date();
      devices.set(deviceId, device);
      console.log(`内存中设备状态已更新: ${deviceId} -> ${status}`);
    } else {
      console.log(`设备 ${deviceId} 在内存中不存在，跳过内存更新`);
    }

    // 2. 更新数据库中的设备状态（如果数据库可用）
    if (pool) {
      try {
        const [result] = await pool.execute(`
          UPDATE devices SET status = ?, last_seen = NOW()
          WHERE device_id = ?
        `, [status, deviceId]);

        if (result.affectedRows > 0) {
          console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
        } else {
          console.log(`设备 ${deviceId} 在数据库中不存在`);
        }
      } catch (dbError) {
        console.error('数据库更新设备状态失败:', dbError);
        // 数据库更新失败不影响响应，因为内存已更新
      }
    }

    // 3. 通过WebSocket广播设备状态更新
    io.emit('device_status_update', {
      deviceId: deviceId,
      status: status,
      lastSeen: new Date()
    });

    console.log(`设备状态强制更新完成: ${deviceId} -> ${status}`);

    res.json({
      success: true,
      message: '设备状态更新成功',
      data: {
        deviceId: deviceId,
        status: status,
        timestamp: new Date()
      }
    });

  } catch (error) {
    console.error('强制更新设备状态失败:', error);
    res.status(500).json({
      success: false,
      message: '强制更新设备状态失败: ' + error.message
    });
  }
});

// 旧的删除设备API已移除，现在使用断开设备API (DELETE /api/device/:id) 和删除设备记录API (DELETE /api/device/:id/delete)

// WebSocket状态变化存储（移除长轮询相关代码）

// 更新设备状态
async function updateDeviceStatus(deviceId, status) {
  console.log(`更新设备状态: ${deviceId} -> ${status}`);

  // 查找设备
  let device = null;
  for (const [socketId, deviceData] of devices) {
    if (deviceData.deviceId === deviceId) {
      device = deviceData;
      // 更新内存中的设备状态
      device.status = status;
      device.lastSeen = new Date();
      break;
    }
  }

  if (!device) {
    console.log(`设备未找到，无法更新状态: ${deviceId}`);
    return;
  }

  // 尝试更新数据库状态（如果可用）
  if (pool) {
    try {
      await pool.execute(`
        UPDATE devices SET status = ?, last_seen = NOW()
        WHERE device_id = ?
      `, [status, deviceId]);

      console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
    } catch (dbError) {
      console.error('数据库状态更新失败:', dbError);
    }
  }

  // 通知所有Web客户端设备状态更新
  const statusChange = {
    type: 'device_status_update',
    deviceId: deviceId,
    deviceName: device.deviceName,
    status: status,
    timestamp: new Date()
  };

  io.emit('device_status_update', {
    deviceId: deviceId,
    status: status,
    lastSeen: device.lastSeen
  });

  // 长轮询相关代码已移除，只使用WebSocket通信

  console.log(`设备状态更新通知已发送: ${deviceId} -> ${status}`);
}

// 长轮询接口已移除，系统现在只使用WebSocket进行实时通信

// 长轮询通知函数已移除，系统现在只使用WebSocket进行实时通信

// 存储最近断开的设备，防止立即重连
const recentlyDisconnectedDevices = new Map();

// 添加设备注册API（HTTP方式）
app.post('/api/device/register', authenticateToken, async (req, res) => {
  const { deviceId, deviceName, deviceInfo, deviceIP } = req.body;

  if (!deviceId || !deviceName) {
    return res.status(400).json({ success: false, message: '设备ID和名称不能为空' });
  }

  // 检查设备是否在最近断开列表中（防止立即重连）
  if (recentlyDisconnectedDevices.has(deviceId)) {
    const disconnectTime = recentlyDisconnectedDevices.get(deviceId);
    const timeSinceDisconnect = Date.now() - disconnectTime;
    const cooldownPeriod = 10000; // 10秒冷却期

    if (timeSinceDisconnect < cooldownPeriod) {
      const remainingTime = Math.ceil((cooldownPeriod - timeSinceDisconnect) / 1000);
      console.log(`设备 ${deviceId} 在冷却期内尝试重连，剩余 ${remainingTime} 秒`);
      return res.status(429).json({
        success: false,
        message: `设备刚刚断开连接，请等待 ${remainingTime} 秒后再重新连接`,
        cooldownRemaining: remainingTime
      });
    } else {
      // 冷却期已过，移除记录
      recentlyDisconnectedDevices.delete(deviceId);
      console.log(`设备 ${deviceId} 冷却期已过，允许重新连接`);
    }
  }

  const currentIP = deviceIP || (deviceInfo && deviceInfo.ipAddress) || '未知';

  // 检查设备是否正在执行脚本
  let deviceStatus = 'online';

  // 检查是否有待执行的命令（说明设备正在执行脚本）
  if (pendingCommands.has(deviceId) && pendingCommands.get(deviceId).length > 0) {
    deviceStatus = 'busy';
    console.log(`设备 ${deviceId} 正在执行脚本，保持忙碌状态`);
  }

  try {
    // 尝试保存到数据库（如果可用）
    if (pool) {
      try {
        await pool.execute(`
          INSERT INTO devices (device_id, device_name, device_info, status, last_seen)
          VALUES (?, ?, ?, ?, NOW())
          ON DUPLICATE KEY UPDATE
          device_name = VALUES(device_name),
          device_info = VALUES(device_info),
          status = CASE
            WHEN devices.status = 'busy' THEN 'busy'
            ELSE VALUES(status)
          END,
          last_seen = NOW()
        `, [deviceId, deviceName, JSON.stringify({
          ...deviceInfo,
          ipAddress: currentIP,
          registrationTime: new Date().toISOString(),
          connectionType: 'http'
        }), deviceStatus]);

        console.log('✅ 设备信息已保存到数据库:', deviceId);

        // 验证数据库状态
        const [rows] = await pool.execute('SELECT status FROM devices WHERE device_id = ?', [deviceId]);
        if (rows.length > 0) {
          console.log('📊 设备注册后数据库状态:', rows[0].status);
        }
      } catch (dbError) {
        console.error('数据库保存失败，继续使用内存模式:', dbError);
      }
    }

  // 检查是否有相同IP的设备已连接，如果有则断开旧连接
  // 注意：只断开群控服务器连接，不影响VSCode调试连接（端口9317）
  if (currentIP && currentIP !== '未知') {
    for (const [socketId, device] of devices) {
      if (device.deviceInfo && device.deviceInfo.ipAddress === currentIP &&
          device.status === 'online' &&
          (socketId.startsWith('http_') || (!socketId.startsWith('vscode_') && !socketId.startsWith('debug_')))) {
        console.log(`检测到同IP设备重连: ${currentIP}, 断开旧连接: ${device.deviceName} (${device.deviceId}), socketId: ${socketId}`);

        // 通知Web客户端旧设备离线
        for (const [clientSocketId] of webClients) {
          const clientSocket = io.sockets.sockets.get(clientSocketId);
          if (clientSocket) {
            clientSocket.emit('device_offline', {
              deviceId: device.deviceId
            });
          }
        }

        // 如果是WebSocket连接，断开socket
        if (!socketId.startsWith('http_')) {
          const oldSocket = io.sockets.sockets.get(socketId);
          if (oldSocket) {
            oldSocket.disconnect(true);
          }
        }

        // 移除旧设备连接
        devices.delete(socketId);
        console.log(`已移除旧设备连接: ${socketId}`);
        break;
      }
    }
  }

  // 检查数据库中是否有正在执行的任务（只检查最近的任务）
  if (pool) {
    try {
      // 同时检查小红书和闲鱼执行日志
      const [xiaohongshuRows] = await pool.execute(`
        SELECT execution_status, started_at FROM xiaohongshu_execution_logs 
        WHERE device_id = ? AND execution_status IN ('running', 'pending')
        ORDER BY started_at DESC LIMIT 1
      `, [deviceId]);
      
      const [xianyuRows] = await pool.execute(`
        SELECT execution_status, started_at FROM xianyu_execution_logs 
        WHERE device_id = ? AND execution_status IN ('running', 'pending')
        ORDER BY started_at DESC LIMIT 1
      `, [deviceId]);
      
      // 合并检查结果
      const allRunningTasks = [...xiaohongshuRows, ...xianyuRows];
      
      if (allRunningTasks.length > 0) {
        // 找到最近的任务
        const latestTask = allRunningTasks.reduce((latest, current) => {
          const currentTime = new Date(current.started_at);
          const latestTime = new Date(latest.started_at);
          return currentTime > latestTime ? current : latest;
        });
        
        const taskStartTime = new Date(latestTask.started_at);
        const now = new Date();
        const timeDiff = now - taskStartTime;
        
        // 只考虑最近10分钟内的任务，避免服务器重启后的历史任务影响
        if (timeDiff < 10 * 60 * 1000) { // 10分钟
          deviceStatus = 'busy';
          console.log(`设备 ${deviceId} 在数据库中有最近10分钟内的正在执行任务，保持忙碌状态`);
          console.log(`任务开始时间: ${taskStartTime.toISOString()}, 时间差: ${Math.round(timeDiff/1000)}秒`);
        } else {
          console.log(`设备 ${deviceId} 的数据库任务已过期，忽略历史任务状态`);
          console.log(`任务开始时间: ${taskStartTime.toISOString()}, 时间差: ${Math.round(timeDiff/1000)}秒`);
          
          // 自动清理过期的任务状态
          try {
            await pool.execute(`
              UPDATE xiaohongshu_execution_logs 
              SET execution_status = 'stopped', 
                  progress_percentage = 0, 
                  status_message = '已过期自动停止'
              WHERE device_id = ? AND execution_status IN ('running', 'pending') 
              AND started_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            `, [deviceId]);
            
            await pool.execute(`
              UPDATE xianyu_execution_logs 
              SET execution_status = 'stopped', 
                  progress_percentage = 0, 
                  status_message = '已过期自动停止'
              WHERE device_id = ? AND execution_status IN ('running', 'pending') 
              AND started_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            `, [deviceId]);
            
            console.log(`🧹 已自动清理设备 ${deviceId} 的过期任务状态`);
          } catch (cleanupError) {
            console.error('自动清理过期任务失败:', cleanupError);
          }
        }
      } else {
        console.log(`设备 ${deviceId} 在数据库中没有正在执行的任务`);
      }
    } catch (dbError) {
      console.error('检查设备执行状态失败:', dbError);
    }
  }

  // 注册设备（如果是同IP重连，使用相同的设备ID）
  const deviceData = {
    socketId: 'http_' + Date.now(),
    deviceId,
    deviceName,
    deviceInfo: {
      ...deviceInfo,
      ipAddress: currentIP
    },
    status: deviceStatus,
    connectedAt: new Date(),
    lastSeen: new Date()
  };

  devices.set(deviceData.socketId, deviceData);

  console.log(`HTTP设备已注册: ${deviceName} (${deviceId}) IP: ${currentIP}`);
  console.log(`设备注册详情: socketId=${deviceData.socketId}, deviceId=${deviceData.deviceId}, deviceName=${deviceData.deviceName}`);

  // 打印详细设备信息
  if (deviceInfo) {
    console.log(`设备详细信息:`);
    console.log(`- 屏幕分辨率: ${deviceInfo.screenWidth || '未知'}x${deviceInfo.screenHeight || '未知'}`);
    console.log(`- 设备品牌: ${deviceInfo.brand || '未知'}`);
    console.log(`- 设备型号: ${deviceInfo.model || '未知'}`);
    console.log(`- Android版本: ${deviceInfo.androidVersion || '未知'}`);
    console.log(`- SDK版本: ${deviceInfo.sdkVersion || '未知'}`);
    console.log(`- 总内存: ${deviceInfo.totalMemory || '未知'}`);
    console.log(`- 可用内存: ${deviceInfo.availableMemory || '未知'}`);
    console.log(`- 电池电量: ${deviceInfo.batteryLevel || '未知'}`);
    console.log(`- 存储信息: ${deviceInfo.storageInfo || '未知'}`);
    console.log(`- Auto.js版本: ${deviceInfo.autoJsVersion || '未知'}`);
    console.log(`- 连接时间: ${deviceInfo.deviceTime || '未知'}`);
  }

  // 通知所有Web客户端有新设备连接
  const statusChange = {
    type: 'device_connected',
    deviceId: deviceId,
    deviceName: deviceName,
    deviceIP: deviceIP,
    deviceInfo: deviceInfo,
    timestamp: new Date()
  };

  io.emit('device_status_changed', statusChange);

  // 长轮询相关代码已移除，只使用WebSocket通信

  res.json({
    success: true,
    message: '设备注册成功',
    data: { deviceId, deviceIP }
  });

  } catch (error) {
    console.error('设备注册失败:', error);
    res.status(500).json({
      success: false,
      message: '设备注册失败: ' + error.message
    });
  }
});

// 删除重复的脚本管理API，使用下面统一的版本

// 删除重复的脚本上传API，使用下面的JSON版本

// 脚本管理API
const scriptsStorage = new Map(); // 存储脚本数据

// 获取脚本列表
app.get('/api/script/list', authenticateToken, (req, res) => {
  const scriptList = Array.from(scriptsStorage.values()).map(script => ({
    id: script.id,
    name: script.name,
    description: script.description,
    created_at: script.created_at,
    updated_at: script.updated_at
  }));

  res.json({
    success: true,
    data: scriptList
  });
});

// 上传脚本
app.post('/api/script/upload', authenticateToken, (req, res) => {
  const { name, content, description } = req.body;

  if (!name || !content) {
    return res.status(400).json({
      success: false,
      message: '脚本名称和内容不能为空'
    });
  }

  const scriptId = Date.now().toString();
  const script = {
    id: scriptId,
    name: name,
    content: content,
    description: description || '',
    created_at: new Date(),
    updated_at: new Date()
  };

  scriptsStorage.set(scriptId, script);

  console.log(`脚本已上传: ${name} (${scriptId})`);

  res.json({
    success: true,
    message: '脚本上传成功',
    data: {
      id: scriptId,
      name: name,
      description: description
    }
  });
});

// 获取单个脚本
app.get('/api/script/:id', authenticateToken, (req, res) => {
  const { id } = req.params;
  const script = scriptsStorage.get(id);

  if (!script) {
    return res.status(404).json({
      success: false,
      message: '脚本不存在'
    });
  }

  res.json({
    success: true,
    data: script
  });
});

// 删除脚本
app.delete('/api/script/:id', authenticateToken, (req, res) => {
  const { id } = req.params;
  const script = scriptsStorage.get(id);

  if (!script) {
    return res.status(404).json({
      success: false,
      message: '脚本不存在'
    });
  }

  scriptsStorage.delete(id);

  console.log(`脚本已删除: ${script.name} (${id})`);

  res.json({
    success: true,
    message: '脚本删除成功'
  });
});

app.post('/api/script/execute', authenticateToken, (req, res) => {
  const { deviceIds, script, scriptId, uiConfig, scriptName } = req.body;

  // 验证输入参数
  if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
    return res.status(400).json({
      success: false,
      message: '设备ID列表不能为空',
      data: {
        successCount: 0,
        failCount: 0,
        totalCount: 0,
        results: [],
        scriptName: scriptName || '未命名脚本',
        timestamp: new Date()
      }
    });
  }

  let scriptContent = script;

  // 如果传入的是scriptId，需要查找脚本内容
  if (scriptId && !script) {
    const foundScript = scriptsStorage.get(scriptId);
    if (!foundScript) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在',
        data: {
          successCount: 0,
          failCount: 0,
          totalCount: deviceIds.length,
          results: [],
          scriptName: scriptName || '未命名脚本',
          timestamp: new Date()
        }
      });
    }
    scriptContent = foundScript.content;
  }

  if (!scriptContent) {
    return res.status(400).json({
      success: false,
      message: '脚本内容不能为空',
      data: {
        successCount: 0,
        failCount: 0,
        totalCount: deviceIds.length,
        results: [],
        scriptName: scriptName || '未命名脚本',
        timestamp: new Date()
      }
    });
  }

  // 注入UI配置到脚本
  if (uiConfig && Object.keys(uiConfig).length > 0) {
    const configInjection = `
// === 配置数据注入 ===
// 由Web端自动注入的配置数据
var __config = ${JSON.stringify(uiConfig, null, 2)};

console.log("=== 配置数据已注入 ===");
console.log("配置内容:", JSON.stringify(__config, null, 2));

// === 原始脚本开始 ===
`;
    scriptContent = configInjection + scriptContent;
  }

  let successCount = 0;
  let failCount = 0;
  const executionResults = [];

  deviceIds.forEach(deviceId => {
    const device = Array.from(devices.values()).find(d => d.deviceId === deviceId);
    if (device) {
      const logId = Date.now() + Math.random(); // 确保唯一性

      try {
        // 对于HTTP设备
        if (device.socketId.startsWith('http_')) {
          // HTTP设备：将命令存储到待执行队列
          if (!pendingCommands.has(deviceId)) {
            pendingCommands.set(deviceId, []);
          }
          pendingCommands.get(deviceId).push({
            logId,
            script: scriptContent,
            timestamp: Date.now()
          });
          console.log(`HTTP设备命令已排队: ${device.deviceName} (${deviceId})`);
          successCount++;
          executionResults.push({
            deviceId,
            deviceName: device.deviceName,
            status: 'queued',
            message: '命令已排队'
          });
        } else {
          // WebSocket设备：直接发送
          const deviceSocket = io.sockets.sockets.get(device.socketId);
          if (deviceSocket) {
            deviceSocket.emit('execute_script', {
              logId,
              script: scriptContent,
              params: {},
              timestamp: Date.now()
            });
            console.log(`WebSocket设备命令已发送: ${device.deviceName} (${deviceId})`);
            successCount++;
            executionResults.push({
              deviceId,
              deviceName: device.deviceName,
              status: 'sent',
              message: '命令已发送'
            });
          } else {
            console.log(`WebSocket设备连接已断开: ${device.deviceName} (${deviceId})`);
            failCount++;
            executionResults.push({
              deviceId,
              deviceName: device.deviceName,
              status: 'failed',
              message: '设备连接已断开'
            });
          }
        }

        // 添加日志记录
        logs.push({
          id: logId,
          device_id: deviceId,
          device_name: device.deviceName,
          command: scriptContent,
          status: 'pending',
          started_at: new Date()
        });

      } catch (error) {
        console.error(`发送脚本到设备失败: ${deviceId}`, error);
        failCount++;
        executionResults.push({
          deviceId,
          deviceName: device.deviceName,
          status: 'error',
          message: error.message
        });
      }
    } else {
      console.log(`设备不存在或已离线: ${deviceId}`);
      failCount++;
      executionResults.push({
        deviceId,
        deviceName: '未知设备',
        status: 'not_found',
        message: '设备不存在或已离线'
      });
    }
  });

  console.log(`脚本执行统计: 成功=${successCount}, 失败=${failCount}, 总计=${deviceIds.length}`);

  res.json({
    success: true,
    message: `脚本执行命令已发送，成功: ${successCount}个，失败: ${failCount}个`,
    data: {
      successCount,
      failCount,
      totalCount: deviceIds.length,
      results: executionResults,
      scriptName: scriptName || '未命名脚本',
      timestamp: new Date()
    }
  });
});

app.get('/api/script/logs/list', authenticateToken, (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const start = (page - 1) * limit;
  const end = start + parseInt(limit);

  const paginatedLogs = logs.slice(start, end);

  res.json({
    success: true,
    data: {
      logs: paginatedLogs,
      total: logs.length,
      page: parseInt(page),
      limit: parseInt(limit)
    }
  });
});

// 文件管理API
app.get('/api/file/list', authenticateToken, (req, res) => {
  // 模拟文件列表
  const files = [
    {
      filename: 'test-script.js',
      size: 1024,
      created: new Date(),
      modified: new Date()
    },
    {
      filename: 'example.apk',
      size: 5242880,
      created: new Date(Date.now() - 86400000),
      modified: new Date(Date.now() - 86400000)
    }
  ];

  res.json({
    success: true,
    data: files
  });
});

app.get('/api/file/transfers', authenticateToken, (req, res) => {
  const { page = 1, limit = 20 } = req.query;

  // 模拟传输记录
  const transfers = [
    {
      id: 1,
      device_name: '测试设备',
      filename: 'test-script.js',
      file_size: 1024,
      transfer_type: 'upload',
      status: 'completed',
      created_at: new Date()
    }
  ];

  res.json({
    success: true,
    data: {
      transfers,
      total: transfers.length,
      page: parseInt(page),
      limit: parseInt(limit)
    }
  });
});

app.post('/api/file/upload', authenticateToken, (req, res) => {
  // 模拟文件上传成功
  res.json({
    success: true,
    message: '文件上传成功（模拟）',
    data: {
      file: {
        filename: 'uploaded-file.js',
        size: 2048
      },
      transfers: []
    }
  });
});

app.delete('/api/file/:filename', authenticateToken, (req, res) => {
  const { filename } = req.params;
  res.json({
    success: true,
    message: `文件 ${filename} 删除成功（模拟）`
  });
});

app.get('/api/file/download/:filename', authenticateToken, (req, res) => {
  const { filename } = req.params;
  res.json({
    success: false,
    message: '测试服务器不支持文件下载，请使用完整版服务器'
  });
});

// HTTP设备获取待执行命令的API
app.get('/api/device/:deviceId/commands', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;

  // 查找设备记录（通过deviceId查找，而不是socketId）
  let device = null;
  let deviceSocketId = null;

  for (const [socketId, deviceData] of devices) {
    if (deviceData.deviceId === deviceId) {
      device = deviceData;
      deviceSocketId = socketId;
      break;
    }
  }

  if (!device) {
    // 检查是否有待执行的命令（包括断开命令和视频传输命令）
    let hasCommands = false;
    let commandToReturn = null;
    let commandSource = '';

    // 优先检查 pendingCommands
    if (pendingCommands.has(deviceId)) {
      const commands = pendingCommands.get(deviceId);
      if (commands.length > 0) {
        commandToReturn = commands.shift();
        commandSource = 'pendingCommands';
        hasCommands = true;

        if (commands.length === 0) {
          pendingCommands.delete(deviceId);
        }
      }
    }

    // 如果没有 pendingCommands，检查 deviceCommands
    if (!hasCommands && deviceCommands[deviceId] && deviceCommands[deviceId].length > 0) {
      const commands = deviceCommands[deviceId];
      commandToReturn = commands.shift();
      commandSource = 'deviceCommands';
      hasCommands = true;

      if (commands.length === 0) {
        delete deviceCommands[deviceId];
      }
    }

    if (hasCommands) {
      console.log(`⚠️ 设备 ${deviceId} 未注册，但有待执行命令(${commandSource})，允许获取命令`);

      // 创建临时设备记录以支持命令获取
      const tempDevice = {
        deviceId: deviceId,
        lastPollTime: Date.now(),
        status: 'online',
        isTemporary: true  // 标记为临时记录
      };

      // 查找合适的socketId（优先使用http_格式）
      let tempSocketId = `temp_${deviceId}_${Date.now()}`;
      devices.set(tempSocketId, tempDevice);

      console.log(`为设备 ${deviceId} 创建临时记录，socketId: ${tempSocketId}`);
      console.log(`返回${commandSource}命令给临时设备 ${deviceId}:`, JSON.stringify(commandToReturn));

      return res.json({
        success: true,
        data: commandToReturn
      });
    }

    console.log(`⚠️ 设备 ${deviceId} 未注册且无待执行命令，拒绝请求`);
    return res.status(404).json({
      success: false,
      message: '设备未注册，请先注册设备'
    });
  }

  // 更新设备的最后轮询时间
  device.lastPollTime = Date.now();

  // 不要在这里自动重置设备状态为在线
  // 设备状态应该由脚本执行完成事件来管理
  // 使用节流日志，避免频繁输出设备命令请求信息
  const deviceLogKey = `device_command_${deviceId}`;
  throttledLog(deviceLogKey, `设备 ${deviceId} 请求获取命令，当前状态: ${device.status}`);
  throttledLog(`${deviceLogKey}_queue`, `当前待执行命令队列状态: pendingCommands=${pendingCommands.has(deviceId)}, deviceCommands=${deviceCommands[deviceId] ? true : false}`);

  // 优先检查 pendingCommands（脚本执行命令）
  if (pendingCommands.has(deviceId)) {
    const commands = pendingCommands.get(deviceId);
    // 使用节流日志，避免频繁输出队列长度信息
    throttledLog(`pending_length_${deviceId}`, `- 设备 ${deviceId} pendingCommands队列长度: ${commands.length}`);

    if (commands.length > 0) {
      // 返回第一个命令并从队列中移除
      const command = commands.shift();

      console.log(`- 返回pendingCommands命令给设备 ${deviceId}:`, JSON.stringify(command));
      console.log(`- 设备 ${deviceId} 剩余pendingCommands数量:`, commands.length);

      // 设备获取到脚本命令时，更新状态为忙碌
      if (command.script && command.script !== 'DISCONNECT_COMMAND') {
        device.status = 'busy';
        console.log(`设备 ${deviceId} 获取到脚本命令，状态更新为忙碌`);

        // 同时更新数据库状态
        await updateDeviceStatus(deviceId, 'busy');

        // 🔥 关键修复：更新执行日志状态为'running'
        console.log(`🔍 [状态修复调试] 检查命令字段:`, {
          hasLogId: !!command.logId,
          logId: command.logId,
          hasXiaohongshuLogService: !!xiaohongshuLogService,
          commandKeys: Object.keys(command)
        });

        if (command.logId && xiaohongshuLogService) {
          try {
            console.log(`🔄 [状态修复] 开始更新执行日志状态为running: ${command.logId}`);
            await xiaohongshuLogService.updateExecutionStatus(
              command.logId,
              'running',
              10,
              '脚本开始执行',
              `设备 ${deviceId} 开始执行脚本`
            );
            console.log(`✅ [状态修复] 执行日志状态已更新为running: ${command.logId}`);
          } catch (error) {
            console.error(`❌ [状态修复] 更新执行日志状态失败: ${command.logId}`, error);
          }
        } else {
          console.warn(`⚠️ [状态修复] 跳过状态更新 - logId: ${command.logId}, service: ${!!xiaohongshuLogService}`);

          // 🔥 强制修复：如果没有logId，尝试从设备状态推断并更新
          if (!command.logId && xiaohongshuLogService) {
            console.log(`🔧 [强制修复] 尝试查找设备的待执行任务并更新状态`);
            try {
              // 查找该设备最近的pending状态记录
              const [pendingTasks] = await pool.execute(`
                SELECT log_id FROM xiaohongshu_execution_logs
                WHERE device_id = ? AND execution_status = 'pending'
                ORDER BY started_at DESC LIMIT 1
              `, [deviceId]);

              if (pendingTasks.length > 0) {
                const logId = pendingTasks[0].log_id;
                console.log(`🔧 [强制修复] 找到待执行任务，更新状态: ${logId}`);

                await xiaohongshuLogService.updateExecutionStatus(
                  logId,
                  'running',
                  10,
                  '脚本开始执行',
                  `设备 ${deviceId} 开始执行脚本`
                );
                console.log(`✅ [强制修复] 执行日志状态已更新为running: ${logId}`);
              } else {
                console.warn(`⚠️ [强制修复] 未找到设备 ${deviceId} 的待执行任务`);
              }
            } catch (error) {
              console.error(`❌ [强制修复] 强制状态更新失败:`, error);
            }
          }
        }
      }

      res.json({
        success: true,
        data: command
      });

      return;
    } else {
      // 使用节流日志，避免频繁输出队列为空信息
      throttledLog(`pending_empty_${deviceId}`, `- 设备 ${deviceId} pendingCommands队列为空`);
    }
  } else {
    // 使用节流日志，避免频繁输出没有队列信息
    throttledLog(`no_pending_${deviceId}`, `- 设备 ${deviceId} 没有pendingCommands队列`);
  }

  // 检查 deviceCommands（视频传输等其他命令）
  if (deviceCommands[deviceId] && deviceCommands[deviceId].length > 0) {
    const commands = deviceCommands[deviceId];
    // 使用节流日志，避免频繁输出队列长度信息
    throttledLog(`device_commands_length_${deviceId}`, `- 设备 ${deviceId} deviceCommands队列长度: ${commands.length}`);

    // 返回第一个命令并从队列中移除
    const command = commands.shift();

    console.log(`- 返回deviceCommands命令给设备 ${deviceId}:`, JSON.stringify(command));
    console.log(`- 设备 ${deviceId} 剩余deviceCommands数量:`, commands.length);

    // 如果队列为空，删除该设备的命令队列
    if (commands.length === 0) {
      delete deviceCommands[deviceId];
      console.log(`- 设备 ${deviceId} deviceCommands队列已清空`);
    }

    res.json({
      success: true,
      data: command
    });

    return;
  } else {
    // 使用节流日志，避免频繁输出没有队列或队列为空信息
    throttledLog(`no_device_commands_${deviceId}`, `- 设备 ${deviceId} 没有deviceCommands队列或队列为空`);
  }

  // 没有待执行命令
  // 使用节流日志，避免频繁输出无命令信息
  throttledLog(`no_command_${deviceId}`, `- 设备 ${deviceId} 无待执行命令`);
  res.json({
    success: true,
    data: null
  });
});

// HTTP设备上报脚本执行结果
app.post('/api/device/:deviceId/result', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;
  const { logId, result, status } = req.body;

  // 更新日志
  const log = logs.find(l => l.id === logId);
  if (log) {
    log.result = result;
    log.status = status;
    log.completed_at = new Date();
  }

  // 检查是否是小红书任务的执行结果
  if (xiaohongshuLogService && logId && logId.includes('xiaohongshu_')) {
    try {
      console.log(`[状态更新] 收到设备结果: logId=${logId}, status=${status}, result=${result}`);

      // 处理停止命令的logId（去掉stop_前缀）
      let actualLogId = logId;
      if (logId.startsWith('stop_')) {
        actualLogId = logId.substring(5); // 去掉"stop_"前缀
        console.log(`[状态更新] 检测到停止命令，原始logId: ${actualLogId}`);

        // 检查是否有重复的设备ID，如果有则去掉重复部分
        // 例如：xiaohongshu_profile_1751366484142_device_192_168_1_71_device_192_168_1_71
        // 应该变为：xiaohongshu_profile_1751366484142_device_192_168_1_71
        const parts = actualLogId.split('_');
        if (parts.length >= 6) {
          // 检查最后两个部分是否重复（device_xxx_device_xxx）
          const lastPart = parts[parts.length - 1];
          const secondLastPart = parts[parts.length - 2];
          const thirdLastPart = parts[parts.length - 3];
          const fourthLastPart = parts[parts.length - 4];

          if (fourthLastPart === 'device' && secondLastPart === 'device' && thirdLastPart === lastPart) {
            // 去掉重复的设备ID部分
            actualLogId = parts.slice(0, -2).join('_');
            console.log(`[状态更新] 检测到重复设备ID，修正后的logId: ${actualLogId}`);
          }
        }
      }

      // 更新小红书执行日志
      // 判断执行状态：停止、成功、失败
      let finalStatus, progress;

      if (status === 'stopped') {
        // 脚本被停止
        finalStatus = 'stopped';
        progress = 0;
        console.log(`[状态更新] 检测到停止状态: ${actualLogId}`);
      } else {
        // 判断执行是否成功：status为'success'或者result中包含成功信息
        const isSuccess = status === 'success' ||
                         (result && (
                           result.includes('执行完成') ||
                           result.includes('成功') ||
                           result.includes('完成')
                         )) ||
                         status === 'completed';

        finalStatus = isSuccess ? 'completed' : 'failed';
        progress = isSuccess ? 100 : 0;
        console.log(`[状态更新] 检测到${isSuccess ? '成功' : '失败'}状态: ${actualLogId}`);
      }

      // 根据最终状态设置状态消息
      let statusMessage, executionLogs;
      if (finalStatus === 'stopped') {
        statusMessage = '已停止';
        executionLogs = `脚本已被停止: ${result || '用户手动停止'}`;
      } else if (finalStatus === 'completed') {
        statusMessage = '执行完成';
        executionLogs = `脚本执行成功: ${result || '无详细信息'}`;
      } else {
        statusMessage = '执行失败';
        executionLogs = `脚本执行失败: ${result || '无详细信息'}`;
      }

      console.log(`[状态更新] 准备更新数据库: actualLogId=${actualLogId}, finalStatus=${finalStatus}, progress=${progress}, statusMessage=${statusMessage}`);

      await xiaohongshuLogService.updateExecutionStatus(
        actualLogId,
        finalStatus,
        progress,
        statusMessage,
        executionLogs
      );

      console.log(`[状态更新] 数据库更新完成: ${actualLogId} -> ${finalStatus} (${progress}%)`);
      console.log(`[状态更新] 状态消息: ${statusMessage}`);

      if (result) {
        await xiaohongshuLogService.updateExecutionResult(actualLogId, {
          status: status,
          result: result,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`小红书执行状态已更新: ${actualLogId} -> ${finalStatus}`);
      console.log(`小红书执行日志已更新: ${actualLogId} -> ${finalStatus} (${progress}%)`);

      // 恢复设备状态为在线
      try {
        await updateDeviceStatus(deviceId, 'online');
        console.log(`HTTP设备脚本执行完成，设备状态已恢复为在线: ${deviceId}`);
      } catch (error) {
        console.error('恢复HTTP设备状态失败:', error);
      }

      // 通知前端重置脚本执行状态
      const completionEvent = {
        deviceId: deviceId,
        taskId: actualLogId,
        status: finalStatus === 'completed' ? 'success' :
                finalStatus === 'stopped' ? 'stopped' : 'failed',
        message: result,
        timestamp: new Date().toISOString()
      };

      console.log(`📡 发送脚本执行完成事件: ${JSON.stringify(completionEvent)}`);
      io.emit('xiaohongshu_execution_completed', completionEvent);

      // 也发送给所有Web客户端
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket && clientSocket.connected) {
          clientSocket.emit('xiaohongshu_execution_completed', completionEvent);
          console.log(`📡 已发送脚本完成事件到客户端: ${clientSocketId}`);
        }
      }

      // 通知手机端关闭小红书应用
      console.log(`通知设备 ${deviceId} 关闭小红书应用`);
      io.to(deviceId).emit('script_command', {
        type: 'close_xiaohongshu_app',
        deviceId: deviceId,
        reason: finalStatus === 'completed' ? '脚本执行完成' :
                finalStatus === 'stopped' ? '脚本被停止' : '脚本执行失败',
        timestamp: new Date().toISOString()
      });
    } catch (logError) {
      console.error('更新小红书执行日志失败:', logError);
    }
  }

  console.log(`HTTP设备执行结果: ${deviceId}, 状态: ${status}`);

  res.json({
    success: true,
    message: '结果已记录'
  });
});

// 断开设备连接API (PC端断开设备，不删除设备记录)
app.delete('/api/device/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  console.log(`PC端请求断开设备: ${id}`);
  console.log(`请求头信息:`, req.headers);
  console.log(`认证用户:`, req.user);

  // 查找设备
  let deviceToDisconnect = null;
  let socketIdToDisconnect = null;

  for (const [socketId, device] of devices) {
    if (device.deviceId === id) {
      deviceToDisconnect = device;
      socketIdToDisconnect = socketId;
      break;
    }
  }

  if (!deviceToDisconnect) {
    console.log(`设备不在内存中: ${id}`);
    console.log(`当前连接的设备:`, Array.from(devices.keys()));

    // 检查数据库中是否存在该设备
    try {
      const [rows] = await pool.execute('SELECT * FROM devices WHERE device_id = ?', [id]);
      if (rows.length === 0) {
        return res.status(404).json({ success: false, message: '设备不存在' });
      }

      // 设备存在于数据库但不在内存中，说明已经离线
      // 直接更新数据库状态为离线
      await pool.execute(`
        UPDATE devices SET status = 'offline', last_seen = NOW()
        WHERE device_id = ?
      `, [id]);

      console.log(`设备已离线，数据库状态已更新: ${id}`);

      // 通知所有Web客户端设备状态变化
      const statusChange = {
        type: 'device_disconnected',
        deviceId: id,
        deviceName: rows[0].device_name,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);
      // 长轮询相关代码已移除，只使用WebSocket通信

      return res.json({
        success: true,
        message: '设备已离线，状态已更新'
      });

    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      return res.status(500).json({ success: false, message: '数据库查询失败' });
    }
  }

  try {
    // 更新数据库中设备状态为离线（不删除记录）
    if (pool) {
      try {
        const [result] = await pool.execute(`
          UPDATE devices SET status = 'offline', last_seen = NOW()
          WHERE device_id = ?
        `, [id]);

        if (result.affectedRows > 0) {
          console.log('设备状态已更新为离线:', id);
        }
      } catch (dbError) {
        console.error('数据库状态更新失败:', dbError);
      }
    }

    // 清理该设备相关的小红书任务
    await cleanupXiaohongshuTasksForDevice(id);

    // 清理该设备相关的闲鱼任务
    await cleanupXianyuTasksForDevice(id);

    // 对于HTTP设备，添加断开命令到队列，让手机端知道PC端要求断开
    if (deviceToDisconnect.socketId && deviceToDisconnect.socketId.startsWith('http_')) {
      if (!pendingCommands.has(id)) {
        pendingCommands.set(id, []);
      }
      pendingCommands.get(id).push({
        logId: Date.now(),
        script: 'DISCONNECT_COMMAND', // 特殊的断开命令
        timestamp: Date.now(),
        type: 'disconnect'
      });
      console.log(`PC端断开命令已排队: ${deviceToDisconnect.deviceName} (${id})`);
      console.log(`断开命令详情:`, JSON.stringify({
        logId: Date.now(),
        script: 'DISCONNECT_COMMAND',
        timestamp: Date.now(),
        type: 'disconnect'
      }));

      // 立即更新内存中的设备状态为离线，确保前端显示正确状态
      deviceToDisconnect.status = 'offline';
      deviceToDisconnect.lastSeen = new Date();
      console.log(`HTTP设备 ${id} 内存状态已更新为离线`);
      console.log(`当前队列状态: ${pendingCommands.get(id).length} 个命令`);

      // 从内存中移除设备，确保设备列表API返回正确的连接数
      devices.delete(socketIdToDisconnect);
      console.log(`HTTP设备 ${id} 已从内存中移除，当前连接设备数: ${devices.size}`);
    } else {
      // 对于WebSocket设备，可以立即断开
      console.log(`WebSocket设备 ${id}，立即断开连接`);

      // 断开WebSocket连接
      const socket = io.sockets.sockets.get(socketIdToDisconnect);
      if (socket) {
        socket.disconnect(true);
      }

      // 从内存中移除设备，确保设备列表API返回正确的连接数
      devices.delete(socketIdToDisconnect);
      console.log(`WebSocket设备 ${id} 已从内存中移除，当前连接设备数: ${devices.size}`);
    }

    // 清理待执行命令（保留断开命令）
    if (pendingCommands.has(id)) {
      const commands = pendingCommands.get(id);
      const disconnectCommands = commands.filter(cmd => cmd.type === 'disconnect');
      if (disconnectCommands.length > 0) {
        pendingCommands.set(id, disconnectCommands);
      } else {
        pendingCommands.delete(id);
      }
    }

    console.log(`PC端断开设备成功: ${deviceToDisconnect.deviceName} (${id})`);

    // 将设备添加到最近断开列表，防止立即重连
    recentlyDisconnectedDevices.set(id, Date.now());
    console.log(`设备 ${id} 已添加到断开冷却列表`);

    // 通知所有Web客户端设备状态变化
    console.log('发送WebSocket通知: PC端断开设备');
    const statusChange = {
      type: 'device_disconnected',
      deviceId: id,
      deviceName: deviceToDisconnect.deviceName,
      timestamp: new Date()
    };

    io.emit('device_status_changed', statusChange);

    // 长轮询相关代码已移除，只使用WebSocket通信

    res.json({
      success: true,
      message: '设备连接已断开，设备记录已保留为离线状态'
    });

  } catch (error) {
    console.error('断开设备连接失败:', error);
    res.status(500).json({
      success: false,
      message: '断开设备连接失败: ' + error.message
    });
  }
});

// 删除设备记录API (真正删除设备记录)
app.delete('/api/device/:id/delete', authenticateToken, async (req, res) => {
  const { id } = req.params;
  console.log(`PC端请求删除设备记录: ${id}`);

  try {
    // 查找设备
    let deviceToDelete = null;
    let socketIdToDelete = null;

    for (const [socketId, device] of devices) {
      if (device.deviceId === id) {
        deviceToDelete = device;
        socketIdToDelete = socketId;
        break;
      }
    }

    // 从数据库中删除设备记录
    if (pool) {
      try {
        const [result] = await pool.execute(`
          DELETE FROM devices WHERE device_id = ?
        `, [id]);

        if (result.affectedRows > 0) {
          console.log('设备记录已从数据库删除:', id);
        } else {
          console.log('数据库中未找到设备记录:', id);
        }
      } catch (dbError) {
        console.error('数据库删除失败:', dbError);
        return res.status(500).json({
          success: false,
          message: '删除设备记录失败: ' + dbError.message
        });
      }
    }

    // 如果设备当前在线，断开连接
    if (deviceToDelete && socketIdToDelete) {
      console.log('断开并删除在线设备:', id);

      // 清理该设备相关的小红书任务
      await cleanupXiaohongshuTasksForDevice(id);

      // 清理该设备相关的闲鱼任务
      await cleanupXianyuTasksForDevice(id);

      // 断开WebSocket连接
      const socket = io.sockets.sockets.get(socketIdToDelete);
      if (socket) {
        socket.disconnect(true);
      }

      // 从内存中移除
      devices.delete(socketIdToDelete);

      // 清理待执行命令
      if (pendingCommands.has(id)) {
        pendingCommands.delete(id);
      }
    }

    console.log(`设备记录删除成功: ${id}`);

    // 通知所有Web客户端设备已删除
    const statusChange = {
      type: 'device_deleted',
      deviceId: id,
      deviceName: deviceToDelete ? deviceToDelete.deviceName : id,
      timestamp: new Date()
    };

    io.emit('device_status_changed', statusChange);

    // 长轮询相关代码已移除，只使用WebSocket通信

    res.json({
      success: true,
      message: '设备记录已删除'
    });

  } catch (error) {
    console.error('删除设备记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除设备记录失败: ' + error.message
    });
  }
});

// 小红书自动化API路由
// 小红书自动化任务存储
let xiaohongshuActiveTasks = new Map();
let xiaohongshuTaskHistory = [];

// 闲鱼自动化API路由
// 闲鱼自动化任务存储
let xianyuActiveTasks = new Map();
let xianyuTaskHistory = [];

// 测试路由
app.get('/api/xiaohongshu/test', (req, res) => {
  res.json({
    success: true,
    message: '小红书自动化API正常工作',
    timestamp: new Date().toISOString(),
    routes: [
      'GET /api/xiaohongshu/test',
      'POST /api/xiaohongshu/execute',
      'POST /api/xiaohongshu/stop',
      'GET /api/xiaohongshu/tasks'
    ]
  });
});

// 执行小红书自动化任务
app.post('/api/xiaohongshu/execute', async (req, res) => {
  try {
    const { functionType, config, deviceConfigs, schedule, deviceIds, taskId } = req.body;

    const requestTime = new Date().toLocaleTimeString();
    console.log(`🔍 [${requestTime}] 收到小红书自动化执行请求:`);
    console.log('- 功能类型:', functionType);
    console.log('- 设备数量:', deviceIds ? deviceIds.length : 0);
    console.log('- 调度模式:', schedule ? schedule.mode : 'unknown');
    console.log('- 请求来源IP:', req.ip || req.connection.remoteAddress);
    console.log('- User-Agent:', req.get('User-Agent') ? req.get('User-Agent').substring(0, 50) + '...' : 'unknown');
    console.log('- 原始配置 config:', JSON.stringify(config, null, 2));
    console.log('- 设备独立配置 deviceConfigs:', JSON.stringify(deviceConfigs, null, 2));
    console.log('- 完整请求体:', JSON.stringify(req.body, null, 2));

    // 特别检查groupChat功能的关键词
    if (functionType === 'groupChat') {
      console.log('🔍 群聊功能特别检查:');
      console.log('- 用户输入的搜索关键词:', config.searchKeyword);
      console.log('- 目标加入次数:', config.targetJoinCount);
      console.log('- 操作间隔:', config.operationInterval);
      console.log('- config对象的所有属性:', Object.keys(config));
      console.log('- config对象是否为空:', Object.keys(config).length === 0);

      // 如果config为空或缺少关键字段，记录警告
      if (!config.searchKeyword) {
        console.log('⚠️ 警告: searchKeyword字段缺失或为空!');
        console.log('- config.searchKeyword值:', config.searchKeyword);
        console.log('- config.searchKeyword类型:', typeof config.searchKeyword);
      }
    }

    // 验证参数
    if (!functionType || !deviceIds || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 验证设备独立配置
    if (!deviceConfigs || typeof deviceConfigs !== 'object') {
      console.log('⚠️ 没有设备独立配置，使用通用配置作为后备');
    } else {
      console.log('✅ 检测到设备独立配置，设备数量:', Object.keys(deviceConfigs).length);
      for (const [deviceId, deviceConfig] of Object.entries(deviceConfigs)) {
        console.log(`- 设备 ${deviceId} 配置:`, JSON.stringify(deviceConfig, null, 2));
      }
    }

    // 根据功能类型选择对应的脚本文件
    const scriptMap = {
      'profile': './jb/无ui界面6.30.js',
      'searchGroupChat': './jb/无ui界面 群聊.js',
      'groupChat': './jb/群聊.js',
      'groupMessage': './jb/每小时群发消息-非UI版本.js',
      'articleComment': './jb/search_comment_no_ui.js',
      'uidMessage': './jb/小红书UID私信脚本-无UI版.js',
      'uidFileMessage': './jb/小红书UID私信脚本-无UI版.js',
      // 添加发布视频功能 - 支持两种命名方式（使用测试版脚本）
      'publishVideo': './jb/小红书发布视频脚本-测试版.js',
      'videoPublish': './jb/小红书发布视频脚本-测试版.js',
      // 添加简化测试脚本
      'videoTest': './jb/测试脚本-简化版.js',
      // 添加视频传输测试脚本
      'videoTransfer': './jb/视频传输测试脚本.js'
    };

    console.log('🔍 脚本映射检查:');
    console.log('- 请求的功能类型:', functionType);
    console.log('- 可用的功能类型:', Object.keys(scriptMap));
    console.log('- 映射的脚本路径:', scriptMap[functionType]);

    if (!scriptMap[functionType]) {
      return res.status(400).json({
        success: false,
        message: `不支持的功能类型: ${functionType}。支持的功能类型: ${Object.keys(scriptMap).join(', ')}`
      });
    }

    const scriptPath = path.resolve(scriptMap[functionType]);

    console.log('尝试读取脚本文件:', scriptPath);
    console.log('文件是否存在:', fs.existsSync(scriptPath));

    if (!fs.existsSync(scriptPath)) {
      return res.status(404).json({
        success: false,
        message: `${functionType}功能脚本不存在: ${scriptPath}`
      });
    }

    const originalScriptContent = fs.readFileSync(scriptPath, 'utf8');

    // 创建任务ID - 优先使用前端传递的taskId
    const finalTaskId = taskId || ('xiaohongshu_' + functionType + '_' + Date.now());
    console.log('🎯 使用任务ID:', finalTaskId, taskId ? '(前端传递)' : '(服务器生成)');

    // 构建通用执行参数（作为后备）
    const executeParams = buildXiaohongshuFunctionConfig(functionType, config);

    // 添加功能类型到参数中
    executeParams.function = functionType;

    console.log('原始配置:', config);
    console.log('通用任务配置:', executeParams);

    // 执行任务 - 传递原始脚本内容和设备独立配置，让executeXiaohongshuTask为每个设备单独转换脚本
    await executeXiaohongshuTask(finalTaskId, originalScriptContent, executeParams, deviceIds, schedule, deviceConfigs, functionType);

    // 记录任务
    const task = {
      id: finalTaskId,
      function: functionType,
      config: config,
      deviceConfigs: deviceConfigs, // 保存设备独立配置
      devices: deviceIds,
      schedule: schedule,
      status: 'running',
      createdAt: new Date(),
      startedAt: new Date()
    };

    xiaohongshuActiveTasks.set(finalTaskId, task);
    xiaohongshuTaskHistory.unshift(task);

    // 限制历史记录数量
    if (xiaohongshuTaskHistory.length > 100) {
      xiaohongshuTaskHistory = xiaohongshuTaskHistory.slice(0, 100);
    }

    res.json({
      success: true,
      message: '小红书自动化任务已启动',
      taskId: finalTaskId,
      scriptPath: scriptPath,
      scriptSize: originalScriptContent.length
    });

  } catch (error) {
    console.error('执行小红书自动化任务失败:', error);
    res.status(500).json({
      success: false,
      message: '执行失败: ' + error.message
    });
  }
});

// 构建功能特定的配置参数
function buildXiaohongshuFunctionConfig(functionType, config) {
  switch (functionType) {
    case 'profile':
      return {
        nickname: config.nickname || '',
        profile: config.profile || '',
        onlyNickname: config.modifyOptions?.includes('onlyNickname') || false,
        onlyProfile: config.modifyOptions?.includes('onlyProfile') || false,
        autoSave: config.modifyOptions?.includes('autoSave') || false,
        operationDelay: config.operationDelay || 2,
        safetyOptions: config.safetyOptions || [],
        // 安全设置的具体选项
        backupOriginal: config.safetyOptions?.includes('backupOriginal') || false,
        confirmBeforeChange: config.safetyOptions?.includes('confirmBeforeChange') || false,
        validateInput: config.safetyOptions?.includes('validateInput') || false,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'searchGroupChat':
      return {
        searchKeyword: config.searchKeyword || '私域',
        targetJoinCount: config.targetJoinCount || 5,
        maxScrollAttempts: config.maxScrollAttempts || 10,
        enableDetailedLog: config.enableDetailedLog || false,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'groupChat':
      return {
        searchKeyword: config.searchKeyword || '私域',
        targetJoinCount: config.targetJoinCount || 5,
        operationInterval: config.operationInterval || 10,
        joinMessage: config.joinMessage || '',
        sendMessage: config.joinSettings?.includes('sendMessage') || false,
        autoScroll: config.joinSettings?.includes('autoScroll') || true
      };

    case 'groupMessage':
      return {
        messageContent: config.messageContent || '',
        sendInterval: config.sendInterval || 10,
        executionMode: config.executionMode || 'once',
        loopInterval: config.loopInterval || 60,
        randomDelay: config.sendSettings?.includes('randomDelay') || true,
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'articleComment':
      return {
        searchKeyword: config.searchKeyword || '美食推荐',
        commentCount: config.commentCount || 3,
        operationDelay: config.operationDelay || 5,
        commentType: config.commentType || 'template',
        customComments: config.customComments || '',
        commentTemplates: config.commentTemplates || ['praise'],
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'uidMessage':
      return {
        inputMode: config.inputMode || 'manual',
        uidList: config.uidList || [],
        uidStrategy: config.uidStrategy || 'sequential',
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog !== undefined ? config.enableDetailLog : true,
        skipUsedUids: config.skipUsedUids !== undefined ? config.skipUsedUids : true,
        autoMarkUsed: config.autoMarkUsed !== undefined ? config.autoMarkUsed : true,
        taskId: config.taskId || '',
        deviceId: config.deviceId || '',
        deviceName: config.deviceName || '',
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'uidFileMessage':
      return {
        inputMode: 'file', // 文件上传模式固定为file
        uidList: config.uidList || [],
        selectedFileId: config.selectedFileId || null,
        uidsPerDevice: config.uidsPerDevice || 5,
        totalUidCount: config.totalUidCount || 10,
        uidStrategy: config.uidStrategy || 'sequential',
        message: config.message || '',
        delay: config.delay || 5,
        maxCount: config.maxCount || 10,
        enableDetailLog: config.enableDetailLog !== undefined ? config.enableDetailLog : true,
        skipUsedUids: config.skipUsedUids !== undefined ? config.skipUsedUids : true,
        autoMarkUsed: config.autoMarkUsed !== undefined ? config.autoMarkUsed : true,
        taskId: config.taskId || '',
        deviceId: config.deviceId || '',
        deviceName: config.deviceName || '',
        // 小红书应用选择
        selectedApp: config.selectedApp || ''
      };

    case 'videoPublish':
      return {
        selectedApp: config.selectedApp || '',
        titleTemplate: config.titleTemplate || '{filename}',
        videoDescription: config.videoDescription || '',
        hashtags: config.hashtags || '',
        publishOptions: config.publishOptions || ['allowComment', 'allowShare'],
        operationDelay: config.operationDelay || 5,
        retryCount: config.retryCount || 2,
        selectedVideoIds: config.selectedVideoIds || [],
        selectedVideos: config.selectedVideos || [],
        taskId: config.taskId || '',
        deviceId: config.deviceId || '',
        deviceName: config.deviceName || ''
      };

    default:
      return config;
  }
}

// 将UI界面脚本转换为无UI界面脚本
function convertUIScriptToNonUI(originalScript, functionType, params, taskId) {
  console.log('开始转换脚本:', functionType);
  console.log('传入参数:', JSON.stringify(params, null, 2));
  console.log('任务ID:', taskId);

  // 文章评论功能现在直接使用无UI脚本，不需要转换
  if (functionType === 'articleComment') {
    console.log('文章评论功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }

  // 循环群发功能现在直接使用无UI脚本，不需要转换
  if (functionType === 'groupMessage') {
    console.log('循环群发功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }

  // 视频发布功能直接使用无UI脚本，不需要转换
  if (functionType === 'videoPublish') {
    console.log('视频发布功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }






  // 文章评论功能现在直接使用无UI脚本，不需要转换
  if (functionType === 'articleComment') {
    console.log('文章评论功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }



  // 对于其他功能，使用原有的转换逻辑
  console.log('使用原有的脚本转换逻辑');

  // 对于searchGroupChat功能，使用手动优化的模板
  if (functionType === 'searchGroupChat') {
    console.log('使用searchGroupChat专用的模板转换逻辑');

    // 读取手动优化的模板
    const fs = require('fs');
    const templatePath = './searchgroupchat-template.js';
    let templateScript = '';

    try {
      templateScript = fs.readFileSync(templatePath, 'utf8');
      console.log('成功读取搜索加群模板文件，长度:', templateScript.length);
    } catch (error) {
      console.log('读取模板文件失败:', error.message);
      throw new Error('无法读取搜索加群模板文件');
    }

    // 使用模板替换参数
    const finalScript = templateScript
      .replace(/\{\{SEARCH_KEYWORD\}\}/g, params.searchKeyword || '私域')
      .replace(/\{\{TARGET_JOIN_COUNT\}\}/g, params.targetJoinCount || 5)
      .replace(/\{\{OPERATION_INTERVAL\}\}/g, params.operationInterval || 10)
      .replace(/\{\{AUTO_SAVE\}\}/g, params.autoSave || true)
      .replace(/\{\{SEND_MESSAGE\}\}/g, params.sendMessage || false)
      .replace(/\{\{JOIN_MESSAGE\}\}/g, params.joinMessage || '')
      .replace(/\{\{AUTO_SCROLL\}\}/g, params.autoScroll || true)
      .replace(/\{\{SKIP_JOINED\}\}/g, params.skipJoined || true)
      .replace(/\{\{RANDOM_DELAY\}\}/g, params.randomDelay || true);

    console.log('searchGroupChat模板转换完成，最终脚本长度:', finalScript.length);

    // 保存调试版本
    try {
      fs.writeFileSync('generated-searchgroupchat-debug.js', finalScript);
      console.log('搜索加群调试脚本已保存');
    } catch (error) {
      console.log('保存调试脚本失败:', error.message);
    }

    return finalScript;
  }

// 清理群聊函数代码
function cleanGroupChatFunctions(functionsCode) {
  console.log('开始清理群聊函数代码...');

  let cleanedCode = functionsCode;

  // 清理UI相关调用
  cleanedCode = cleanedCode.replace(/ui\.run\s*\([^)]*\)\s*;/g, '// UI操作已移除');
  cleanedCode = cleanedCode.replace(/ui\.\w+\.setText\([^)]*\)/g, '// UI设置已移除');
  cleanedCode = cleanedCode.replace(/ui\.\w+\.setEnabled\([^)]*\)/g, '// UI设置已移除');
  cleanedCode = cleanedCode.replace(/ui\.\w+\.text\(\)\.trim\(\)/g, '""');
  cleanedCode = cleanedCode.replace(/ui\.\w+\.text\(\)/g, '""');
  cleanedCode = cleanedCode.replace(/ui\.\w+\.checked/g, 'false');

  // 清理存储操作
  cleanedCode = cleanedCode.replace(/storage\.put\([^)]*\);/g, '// 存储操作已移除');
  cleanedCode = cleanedCode.replace(/storage\.get\([^)]*\)/g, '"私域"');

  // 清理对话框操作
  cleanedCode = cleanedCode.replace(/dialogs\.alert\([^;]*\);/g, 'addLog("对话框已移除");');
  cleanedCode = cleanedCode.replace(/dialogs\.select\([^)]*\)/g, '0');
  cleanedCode = cleanedCode.replace(/dialogs\.rawInput\([^)]*\)/g, '""');

  // 转换toast为addLog
  cleanedCode = cleanedCode.replace(/\btoast\s*\(([^)]*)\)\s*;/g, 'addLog("Toast: " + $1);');

  // 转换console.log为addLog
  cleanedCode = cleanedCode.replace(/console\.log\s*\(([^)]*)\)\s*;/g, 'addLog($1);');

  console.log('群聊函数代码清理完成');
  return cleanedCode;
}

// 专门用于提取修改资料脚本的核心功能函数
function extractProfileCoreFunctions(script) {
  console.log('开始提取修改资料脚本的核心功能函数');

  // 定义需要保留的核心函数
  const coreFunctionNames = [
    'addLog',
    'executeScript',
    'launchXiaohongshu',
    'ensureInHomePage',
    'backToHome',
    'goToMyProfile',
    'checkProfilePageOpened',
    'changeNickname',
    'changeProfile',
    'clickEditProfileButton',
    'clickSaveButton',
    'clickNicknameField',
    'clickProfileField',
    'endButton',
    'endSecondButton',
    'checkShouldStop',
    'updateButtonStates',
    'updateServiceStatus',
    'updateNameCharCount',
    'resetScriptState'
  ];

  let extractedFunctions = '';

  // 提取每个核心函数
  for (const functionName of coreFunctionNames) {
    const functionCode = extractFunction(script, functionName);
    if (functionCode && functionCode.trim().length > 0) {
      // 验证提取的函数代码是否有效
      if (functionCode.includes(`function ${functionName}`) || functionCode.includes(`${functionName} =`)) {
        extractedFunctions += functionCode + '\n\n';
        console.log(`✓ 提取函数: ${functionName}`);
      } else {
        console.log(`✗ 提取的函数代码无效: ${functionName}`);
      }
    } else {
      console.log(`✗ 未找到函数: ${functionName}`);
    }
  }

  console.log('修改资料核心功能函数提取完成');
  return extractedFunctions;
}

// 专门用于转换修改资料脚本为无UI版本的函数
function convertProfileScriptToNonUI(originalScript, params) {
  console.log('开始转换修改资料脚本为无UI版本');
  console.log('参数:', JSON.stringify(params, null, 2));

  // 移除UI相关的代码
  let cleanedScript = originalScript;

  // 1. 移除UI声明和布局
  cleanedScript = cleanedScript.replace(/"ui";\s*\n/, '');
  cleanedScript = cleanedScript.replace(/ui\.layout\([\s\S]*?\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.statusBarColor\([^)]*\);\s*\n/g, '');

  // 2. 移除存储器相关代码（在无UI模式下不需要）
  cleanedScript = cleanedScript.replace(/const storage = storages\.create\([^)]*\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/const saved\w+ = storage\.get\([^)]*\);\s*\n/g, '');

  // 3. 移除所有UI相关的事件监听器（更精确的匹配）
  cleanedScript = cleanedScript.replace(/ui\.\w+\.setOnCheckedChangeListener\(function\([^)]*\)\s*\{[\s\S]*?\}\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.\w+\.addTextChangedListener\(\{[\s\S]*?\}\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.\w+\.click\(\(\)\s*=>\s*\{[\s\S]*?\}\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.emitter\.on\([^,]+,\s*\([^)]*\)\s*=>\s*\{[\s\S]*?\}\);\s*\n/g, '');

  // 4. 移除UI更新函数的实现，但保留函数声明（避免调用错误）
  // 注意：这些函数会在extractProfileCoreFunctions中被重新提取，所以这里不需要替换

  // 5. 移除UI相关的变量声明
  cleanedScript = cleanedScript.replace(/let (shouldStop|isRunning|currentThread|nameLength) = [^;]*;\s*\n/g, '');

  // 6. 简化checkShouldStop函数，移除UI相关检查
  cleanedScript = cleanedScript.replace(/function checkShouldStop\([^)]*\)\s*\{[\s\S]*?\n\}/g, `
function checkShouldStop() {
    // 在无UI模式下简化检查逻辑
    if (typeof shouldStop !== 'undefined' && shouldStop) {
        addLog("检测到终止信号，停止执行");
        throw new Error("脚本被用户终止");
    }
}`);

  // 7. 移除UI相关的调用
  cleanedScript = cleanedScript.replace(/ui\.run\(\(\)\s*=>\s*\{[\s\S]*?\}\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/updateButtonStates\([^)]*\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/updateServiceStatus\([^)]*\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/updateNameCharCount\([^)]*\);\s*\n/g, '');

  // 8. 移除初始化相关的UI代码
  cleanedScript = cleanedScript.replace(/\/\/ 初始化状态[\s\S]*?\/\/ 初始化按钮状态[\s\S]*?\n/g, '');

  // 9. 提取核心功能函数（保留executeScript和所有核心功能函数）
  const coreFunctions = extractProfileCoreFunctions(cleanedScript);

  // 10. 添加Web端参数注入代码
  const paramInjectionCode = `
// Web端传递的配置参数
const webConfig = ${JSON.stringify(params, null, 2)};

console.log('=== 脚本开始执行 ===');
console.log('收到Web端配置参数:', JSON.stringify(webConfig, null, 2));

// 提取具体参数
const nickname = webConfig.nickname || '';
const profile = webConfig.profile || '';
const onlyNickname = webConfig.onlyNickname || false;
const onlyProfile = webConfig.onlyProfile || false;
const autoSave = webConfig.autoSave || false;
const operationDelay = webConfig.operationDelay || 2;

// 初始化脚本状态变量
let shouldStop = false;
let isRunning = false;
let currentThread = null;
let nameLength = 0;
let logMessages = []; // 添加日志消息数组
`;

  // 11. 添加自动执行代码
  const autoExecuteCode = `
// 自动执行修改资料功能
console.log('开始自动执行修改资料功能');

// 检查无障碍服务
if (!auto.service) {
    addLog('无障碍服务未开启，请先开启无障碍服务');
    throw new Error('无障碍服务未开启');
}

// 验证参数
if (!onlyProfile && (!nickname || nickname.length < 2 || nickname.length > 24)) {
    addLog('昵称长度必须在2-24个字符之间');
    throw new Error('昵称长度无效');
}

if (!onlyNickname && !profile) {
    addLog('请输入简介内容');
    throw new Error('简介内容为空');
}

// 执行脚本主逻辑
try {
    isRunning = true;
    executeScript(nickname, profile, onlyNickname, onlyProfile);
    addLog('修改资料功能执行完成');
} catch (error) {
    addLog('修改资料功能执行失败: ' + error.message);
    throw error;
} finally {
    isRunning = false;
}
`;

  // 12. 组合最终脚本
  const finalScript = paramInjectionCode + '\n' + coreFunctions + '\n' + autoExecuteCode;

  console.log('修改资料脚本转换完成');
  return finalScript;
}

  // 对于profile功能，直接使用无UI界面脚本
  if (functionType === 'profile') {
    console.log('=== 修改资料功能：直接使用无UI界面脚本 ===');
    console.log('参数:', JSON.stringify(params, null, 2));

    // 移除原始的main()调用
    let modifiedScript = originalScript.replace(/main\(\);?\s*$/m, '');

    // 在脚本开头注入参数并直接调用executeScript
    const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(params, null, 2)};

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + serverParams.onlyNickname);
console.log("只修改简介: " + serverParams.onlyProfile);

try {
    executeScript(serverParams.nickname, serverParams.profile, serverParams.onlyNickname, serverParams.onlyProfile, serverParams);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
}

`;

    const finalScript = modifiedScript + '\n' + paramInjection;

    console.log('修改资料脚本参数注入完成，最终长度:', finalScript.length);

    // 保存调试版本
    try {
      const fs = require('fs');
      fs.writeFileSync('generated-profile-converted.js', finalScript);
      console.log('修改资料转换脚本已保存到: generated-profile-converted.js');
    } catch (debugError) {
      console.log('调试保存失败:', debugError.message);
    }

    return finalScript;
  }

  // 对于其他功能，使用原有的转换逻辑
  console.log('使用原有的脚本转换逻辑');

  // 提取核心功能函数
  const coreFunctions = extractCoreFunctions(originalScript);

  // 修复UI相关的代码引用
  const fixedCoreFunctions = fixUIReferences(coreFunctions, functionType, params);

  // 添加必要的代码
  const logFunctionCode = `
// 完整的日志函数
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const fullMessage = '[' + timestamp + '] ' + message;
    console.log(fullMessage);
}

// 安全的toast函数（无UI模式下使用console.log）
function toast(message) {
    addLog('Toast: ' + message);
}

// 禁用可能导致UI线程冲突的函数
function updateButtonStates() {
    // 无UI模式下不执行任何操作
}

function updateSentCount() {
    // 无UI模式下不执行任何操作
}

function updateProcessedCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        processedControlCount: processedControlCount,
        message: '已处理控件数: ' + processedControlCount
    });
}

function updateExecutionCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        executionCount: executionCount,
        message: '执行次数: ' + executionCount
    });
}

function updateLoopCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        loopCount: loopCount,
        message: '循环次数: ' + loopCount
    });
}

function updateServiceStatus() {
    // 无UI模式下不执行任何操作
}

function updateCurrentStatus(status) {
    addLog('状态更新: ' + status);
}

function updateNameCharCount() {
    // 无UI模式下不执行任何操作
}

// 检查脚本是否应该继续运行（无UI模式版本）
function checkPause() {
    // 在无UI模式下，我们简化检查逻辑
    // 只检查基本的运行状态，不检查UI相关的暂停状态
    if (typeof shouldStop !== 'undefined' && shouldStop) {
        addLog("脚本已被终止，停止执行");
        throw new Error("脚本被用户终止");
    }

    // 检查isRunning状态（如果定义了的话）
    if (typeof isRunning !== 'undefined' && !isRunning) {
        // 在无UI模式下，如果isRunning为false但没有明确的终止信号，
        // 我们认为这可能是初始化问题，不立即终止
        addLog("检测到isRunning为false，但在无UI模式下继续执行");
    }

    // 无UI模式下不需要检查暂停状态
}
`;

  const paramInjectionCode = generateParameterInjectionCode(functionType, params);
  const autoExecuteCode = generateAutoExecuteCode(functionType, params);

  // 组合最终脚本
  const finalScript = `${logFunctionCode}${paramInjectionCode}

${fixedCoreFunctions}

${autoExecuteCode}`;

  console.log('脚本转换完成');
  return finalScript;
}

// 修复UI相关的代码引用
function fixUIReferences(script, functionType, params) {
  let fixedScript = script;



  // 修复群聊功能中的UI引用
  if (functionType === 'groupChat') {
    // 可能的UI引用修复
    fixedScript = fixedScript.replace(
      /ui\.\w+\.text\(\)\.trim\(\)/g,
      '""'
    );
  }

  // 修复文章评论功能中的UI引用
  if (functionType === 'articleComment') {
    // 可能的UI引用修复
    fixedScript = fixedScript.replace(
      /ui\.\w+\.text\(\)\.trim\(\)/g,
      '""'
    );
  }

  // 通用UI引用修复
  fixedScript = fixedScript.replace(
    /ui\.\w+\.checked/g,
    'false'
  );

  fixedScript = fixedScript.replace(
    /ui\.\w+\.text\(\)/g,
    '""'
  );

  fixedScript = fixedScript.replace(
    /ui\.run\s*\(\s*\(\s*\)\s*=>\s*\{[\s\S]*?\}\s*\);?/g,
    '// UI操作已移除'
  );

  // 移除存储器相关的UI引用
  fixedScript = fixedScript.replace(
    /storage\.get\s*\([^)]*\)/g,
    '10'
  );

  fixedScript = fixedScript.replace(
    /storage\.put\s*\([^)]*\);?/g,
    '// 存储操作已移除'
  );

  return fixedScript;
}

// 提取核心功能函数
function extractCoreFunctions(script) {
  // 定义需要保留的核心函数
  const coreFunctionNames = [
    // 通用核心函数
    'executeScript',
    'launchXiaohongshu',
    'ensureInHomePage',
    'backToHome',
    'checkPause',
    'checkPermissions',

    // 修改资料功能函数
    'goToMyProfile',
    'checkProfilePageOpened',
    'changeNickname',
    'changeProfile',
    'clickEditProfileButton',
    'clickSaveButton',
    'clickNicknameField',
    'clickProfileField',
    'endButton',
    'endSecondButton',
    'checkShouldStop',
    'resetScriptState',

    // 群发消息功能函数
    'executeSingleRun',
    'executeLoopMode',
    'openMessagePage',
    'checkMessagePageOpened',
    'scrollToTop',
    'analyzeAndClickTargetControls',
    'checkJoinGroupChatSuccess',
    'executeThreeStepClicks',
    'checkAndHandleGroupChatSuccess',
    'checkAndHandleGroupChatDialog',
    'exitXiaohongshu',

    // 群聊功能函数
    'openDiscoverGroupChat',
    'clickTopRightCorner',
    'searchGroupChat',
    'analyzeAndClickTarget',
    'analyzeCurrentPageControls',
    'checkJoinControlsExist',
    'scrollPageAndFindNewJoinButtons',
    'clickAllJoinButtons',
    'clickJoinButton',
    'checkJoinSuccess',
    'handleJoinDialog',
    'checkAndHandleGroupVerification',
    'checkAndHandleGroupChatSuccess',
    'showChangeKeywordDialog',
    'showKeywordInputDialog',
    'updateSuccessCount',
    'isButtonClicked',
    'recordClickedButton',
    'getButtonId',
    'arrayContains',
    'checkButtonTextChanged',
    'clickImmediateJoinButton',
    'clickAlternativeButton',

    // 文章评论功能函数
    'executeSearchComment',
    'clickSearchButton',
    'inputSearchKeyword',
    'clickSearchResults',
    'clickArticleElement'
  ];

  let result = '';

  // 注意：全局变量现在在参数注入代码中声明，这里不再重复声明

  // 提取每个核心函数
  for (const functionName of coreFunctionNames) {
    const functionCode = extractFunction(script, functionName);
    if (functionCode) {
      result += functionCode + '\n\n';
    }
  }

  return result;
}

// 提取单个函数
function extractFunction(script, functionName) {
  // 匹配函数定义
  const functionRegex = new RegExp(`function\\s+${functionName}\\s*\\([^)]*\\)\\s*\\{`, 'g');
  const match = functionRegex.exec(script);

  if (!match) {
    return null;
  }

  const startPos = match.index;
  let pos = startPos + match[0].length;
  let braceCount = 1;

  // 找到函数结束位置
  while (pos < script.length && braceCount > 0) {
    if (script[pos] === '{') {
      braceCount++;
    } else if (script[pos] === '}') {
      braceCount--;
    }
    pos++;
  }

  if (braceCount === 0) {
    let functionCode = script.substring(startPos, pos);

    // 清理函数内的UI相关代码
    functionCode = cleanFunctionCode(functionCode);

    return functionCode;
  }

  return null;
}

// 更完善的函数提取方法，专门用于群聊脚本
function extractCompleteFunction(script, functionName) {
  console.log(`正在提取函数: ${functionName}`);

  // 使用现有的extractFunction方法
  const extractedFunc = extractFunction(script, functionName);

  if (extractedFunc) {
    console.log(`成功提取函数 ${functionName}，长度: ${extractedFunc.length}`);
    return extractedFunc;
  } else {
    console.log(`未找到函数: ${functionName}`);
    return null;
  }
}

// 清理函数内的UI相关代码
function cleanFunctionCode(code) {
  let result = code;

  // 正确处理UI引用，不要完全删除，而是替换为合适的值
  // 特殊处理：先处理带有.trim()的情况
  result = result.replace(/(\w+\s*=\s*)ui\.searchKeywordInput\.text\(\)\.trim\(\)/g, '$1""');
  result = result.replace(/(\w+\s*=\s*)ui\.\w+\.text\(\)\.trim\(\)/g, '$1""');
  // 处理其他位置的UI引用
  result = result.replace(/ui\.searchKeywordInput\.text\(\)\.trim\(\)/g, '""');
  result = result.replace(/ui\.\w+\.text\(\)\.trim\(\)/g, '""');
  // 然后处理普通的.text()情况
  result = result.replace(/ui\.\w+\.text\(\)/g, '""');
  result = result.replace(/ui\.\w+\.checked/g, 'false');

  // 移除UI设置相关的函数调用
  result = result.replace(/ui\.\w+\.setText\([^)]*\)\s*;?\s*/g, '// UI设置已移除');
  result = result.replace(/ui\.\w+\.setEnabled\([^)]*\)\s*;?\s*/g, '// UI设置已移除');
  result = result.replace(/ui\.\w+\.setVisibility\([^)]*\)\s*;?\s*/g, '// UI设置已移除');

  result = result.replace(/updateButtonStates\s*\([^)]*\)\s*;?\s*/g, '');
  result = result.replace(/updateServiceStatus\s*\([^)]*\)\s*;?\s*/g, '');
  // 移除特定的update函数调用，但保留updateCurrentStatus
  result = result.replace(/updateSentCount\s*\([^)]*\)\s*;?\s*/g, '');
  result = result.replace(/updateProcessedCount\s*\([^)]*\)\s*;?\s*/g, '');
  result = result.replace(/updateExecutionCount\s*\([^)]*\)\s*;?\s*/g, '');
  result = result.replace(/updateLoopCount\s*\([^)]*\)\s*;?\s*/g, '');
  result = result.replace(/updateNameCharCount\s*\([^)]*\)\s*;?\s*/g, '');

  // 保留console.log输出，不要移除调试信息
  // 保留toast输出，转换为addLog（使用更精确的正则表达式）
  result = result.replace(/\btoast\s*\(([^)]*)\)\s*;/g, 'addLog("Toast: " + $1);');

  // 移除存储器相关代码
  result = result.replace(/storage\.put\([^)]*\)\s*;?\s*/g, '// 存储操作已移除');
  result = result.replace(/storage\.get\([^)]*\)/g, '"私域"');

  // 移除线程相关代码
  result = result.replace(/threads\.start[\s\S]*?\)\s*;?\s*/g, '');
  result = result.replace(/currentThread[\s\S]*?;/g, '');

  // 简化可能导致Auto.js崩溃的复杂函数实现
  // 将analyzeCurrentPageControls函数简化为空函数
  result = result.replace(/function analyzeCurrentPageControls\(\) \{[\s\S]*?\n\}/g,
    'function analyzeCurrentPageControls() {\n    // 函数已简化 - 防止Auto.js崩溃\n    console.log("analyzeCurrentPageControls已简化");\n}');

  // 移除复杂的dumpWindowHierarchy调用
  result = result.replace(/auto\.dumpWindowHierarchy\s*\([^)]*\)\s*;?\s*/g, '// auto.dumpWindowHierarchy调用已移除 - 防止Auto.js崩溃\n        ');

  // 移除可能导致崩溃的控件遍历调试代码
  result = result.replace(/let allTexts = className\("android\.widget\.TextView"\)\.find\(\);[\s\S]*?for \(let i = 0; i < Math\.min\(allTexts\.length, \d+\); i\+\+\) \{[\s\S]*?\n\s*\}/g, '// 控件遍历调试代码已移除 - 防止Auto.js崩溃\n        ');
  result = result.replace(/let allElements = className\("\*"\)\.find\(\);[\s\S]*?for \(let i = 0; i < Math\.min\(allElements\.length, \d+\); i\+\+\) \{[\s\S]*?\n\s*\}/g, '// 控件遍历调试代码已移除 - 防止Auto.js崩溃\n        ');

  // 更精确的匹配模式，处理具体的控件遍历代码
  result = result.replace(/console\.log\("进群验证问题页面控件信息:"\);[\s\S]*?let allTexts = className\("android\.widget\.TextView"\)\.find\(\);[\s\S]*?for \(let i = 0; i < Math\.min\(allTexts\.length, \d+\); i\+\+\) \{[\s\S]*?\n\s*\}/g, '// 进群验证问题页面控件遍历代码已移除 - 防止Auto.js崩溃\n        ');
  result = result.replace(/console\.log\("当前页面所有文本控件:"\);[\s\S]*?let allTexts = className\("android\.widget\.TextView"\)\.find\(\);[\s\S]*?for \(let i = 0; i < Math\.min\(allTexts\.length, \d+\); i\+\+\) \{[\s\S]*?\n\s*\}/g, '// 当前页面文本控件遍历代码已移除 - 防止Auto.js崩溃\n        ');

  // 移除其他可能导致崩溃的大量控件操作
  result = result.replace(/className\("android\.widget\.TextView"\)\.find\(\)/g, '[]  // TextView查找已简化 - 防止Auto.js崩溃');
  result = result.replace(/className\("\*"\)\.find\(\)/g, '[]  // 全控件查找已简化 - 防止Auto.js崩溃');

  return result;
}

// 生成参数注入代码
function generateParameterInjectionCode(functionType, params) {
  const paramsJson = JSON.stringify(params, null, 2);

  return `
// Web端传递的配置参数
const webConfig = ${paramsJson};

addLog('收到Web端配置参数: ' + JSON.stringify(webConfig, null, 2));

// 提取具体参数
${generateSpecificParameterExtraction(functionType, params)}
`;
}

// 生成特定功能的参数提取代码
function generateSpecificParameterExtraction(functionType, params) {
  switch (functionType) {
    case 'profile':
      return `
const nickname = webConfig.nickname || '';
const profile = webConfig.profile || '';
const onlyNickname = webConfig.onlyNickname || false;
const onlyProfile = webConfig.onlyProfile || false;
const autoSave = webConfig.autoSave || false;
const operationDelay = webConfig.operationDelay || 2;

addLog('修改资料参数 - 昵称: ' + nickname + ', 简介: ' + profile);
addLog('修改选项 - 仅昵称: ' + onlyNickname + ', 仅简介: ' + onlyProfile);
`;

    case 'searchGroupChat':
      return `
const searchKeyword = webConfig.searchKeyword || '私域';
const targetJoinCount = webConfig.targetJoinCount || 5;
const operationInterval = webConfig.operationInterval || 10;
const joinMessage = webConfig.joinMessage || '';
const sendMessage = webConfig.sendMessage || false;
const autoScroll = webConfig.autoScroll || true;
const autoSave = webConfig.autoSave || true;
const skipJoined = webConfig.skipJoined || true;
const randomDelay = webConfig.randomDelay || true;

addLog('搜索加群参数 - 搜索关键词: ' + searchKeyword + ', 目标次数: ' + targetJoinCount);
addLog('操作间隔: ' + operationInterval + '秒, 发送消息: ' + sendMessage);
addLog('自动滚动: ' + autoScroll + ', 跳过已加入: ' + skipJoined + ', 随机延迟: ' + randomDelay);

// 设置全局变量（基于群聊.js脚本的变量）
let successfulJoinCount = 0;
let isStopped = false;
let isPaused = false;
let hasExecuted = true;
let clickedButtons = [];
let shouldStop = false;
let isRunning = true;
let logMessages = [];
let currentThread = null;
`;

    case 'groupChat':
      return `
const searchKeyword = webConfig.searchKeyword || '私域';
const targetJoinCount = webConfig.targetJoinCount || 5;
const operationInterval = webConfig.operationInterval || 10;
const joinMessage = webConfig.joinMessage || '';
const sendMessage = webConfig.sendMessage || false;
const autoScroll = webConfig.autoScroll || true;

addLog('群聊参数 - 搜索关键词: ' + searchKeyword + ', 目标次数: ' + targetJoinCount);
addLog('操作间隔: ' + operationInterval + '秒, 发送消息: ' + sendMessage);

// 设置全局变量（避免重复声明）
let successfulJoinCount = 0;
let isStopped = false;
let isPaused = false;
let hasExecuted = true;
let clickedButtons = [];
let shouldStop = false;  // 脚本终止标志
let isRunning = true;    // 脚本运行状态
let nameLength = 0;
`;



    case 'articleComment':
      return `
const searchKeyword = webConfig.searchKeyword || '美食推荐';
const commentCount = webConfig.commentCount || 3;
const operationDelay = webConfig.operationDelay || 5;
const commentType = webConfig.commentType || 'template';
const customComments = webConfig.customComments || '';
const commentTemplates = webConfig.commentTemplates || ['praise'];

addLog('评论参数 - 搜索关键词: ' + searchKeyword + ', 评论数量: ' + commentCount);
addLog('评论类型: ' + commentType + ', 操作延迟: ' + operationDelay + '秒');
`;

    case 'uidMessage':
      return `
// UID私信功能参数处理
const uidList = webConfig.uidList || [];
const message = webConfig.message || '';
const delay = webConfig.delay || 5;
const maxCount = webConfig.maxCount || 10;
const enableDetailLog = webConfig.enableDetailLog !== undefined ? webConfig.enableDetailLog : true;
const skipUsedUids = webConfig.skipUsedUids !== undefined ? webConfig.skipUsedUids : true;
const autoMarkUsed = webConfig.autoMarkUsed !== undefined ? webConfig.autoMarkUsed : true;
const taskId = webConfig.taskId || '';
const deviceId = webConfig.deviceId || '';
const deviceName = webConfig.deviceName || '';

// 设置全局配置对象
const globalConfig = {
  uidList: uidList,
  message: message,
  delay: delay,
  maxCount: maxCount,
  enableDetailLog: enableDetailLog,
  skipUsedUids: skipUsedUids,
  autoMarkUsed: autoMarkUsed,
  taskId: taskId,
  deviceId: deviceId,
  deviceName: deviceName
};

addLog('UID私信参数 - UID数量: ' + uidList.length + ', 私信内容: ' + message);
addLog('操作间隔: ' + delay + '秒, 最大数量: ' + maxCount);
addLog('任务ID: ' + taskId + ', 设备ID: ' + deviceId);
`;

    default:
      return '// 未知功能类型，使用默认参数';
  }
}

// 生成自动执行代码
function generateAutoExecuteCode(functionType, params) {
  switch (functionType) {
    case 'profile':
      return `
// 自动执行修改资料功能
addLog('开始自动执行修改资料功能');

// 检查无障碍服务
if (!auto.service) {
    addLog('无障碍服务未开启，请先开启无障碍服务');
    throw new Error('无障碍服务未开启');
}

// 验证参数
if (!onlyProfile && (!nickname || nickname.length < 2 || nickname.length > 24)) {
    addLog('昵称长度必须在2-24个字符之间');
    throw new Error('昵称长度无效');
}

if (!onlyNickname && !profile) {
    addLog('请输入简介内容');
    throw new Error('简介内容为空');
}

// 执行脚本主逻辑
try {
    executeScript(nickname, profile, onlyNickname, onlyProfile);
    addLog('修改资料功能执行完成');

    // 发送执行完成状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'success', 100, '修改资料功能执行完成');
    }
} catch (error) {
    addLog('修改资料功能执行失败: ' + error.message);

    // 发送执行失败状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'error', 0, '修改资料功能执行失败: ' + error.message);
    }
    throw error;
}
`;

    case 'searchGroupChat':
      return `
// 搜索加群功能直接使用无UI界面 群聊.js脚本，不需要额外的自动执行代码
// 状态上报已在无UI界面 群聊.js脚本中处理
`;

    case 'groupChat':
      return `
// 自动执行群聊加入功能
addLog('开始自动执行群聊加入功能');

// 检查无障碍服务
if (!auto.service) {
    addLog('无障碍服务未开启，请先开启无障碍服务');
    throw new Error('无障碍服务未开启');
}

// 验证参数
if (!searchKeyword) {
    addLog('请输入搜索关键词');
    throw new Error('搜索关键词为空');
}

if (!targetJoinCount || targetJoinCount <= 0) {
    addLog('请输入有效的目标加入次数');
    throw new Error('目标加入次数无效');
}

// 执行脚本主逻辑
try {
    executeScript(searchKeyword);
    addLog('群聊加入功能执行完成');

    // 发送执行完成状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'success', 100, '群聊加入功能执行完成');
    }
} catch (error) {
    addLog('群聊加入功能执行失败: ' + error.message);

    // 发送执行失败状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'error', 0, '群聊加入功能执行失败: ' + error.message);
    }
    throw error;
}
`;



    case 'articleComment':
      return `
// 自动执行文章评论功能
addLog('开始自动执行文章评论功能');

// 检查无障碍服务
if (!auto.service) {
    addLog('无障碍服务未开启，请先开启无障碍服务');
    throw new Error('无障碍服务未开启');
}

// 验证参数
if (!searchKeyword) {
    addLog('请输入搜索关键词');
    throw new Error('搜索关键词为空');
}

if (!commentCount || commentCount <= 0) {
    addLog('请输入有效的评论数量');
    throw new Error('评论数量无效');
}

// 执行脚本主逻辑
try {
    executeSearchComment(searchKeyword, commentCount, operationDelay);
    addLog('文章评论功能执行完成');

    // 发送执行完成状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'success', 100, '文章评论功能执行完成');
    }
} catch (error) {
    addLog('文章评论功能执行失败: ' + error.message);

    // 发送执行失败状态
    if (typeof sendStatusUpdate === 'function') {
        sendStatusUpdate('completed', 'error', 0, '文章评论功能执行失败: ' + error.message);
    }
    throw error;
}
`;

    default:
      return `
// 未知功能类型，跳过自动执行
addLog('未知功能类型: ${functionType}');
`;
  }
}

// 执行小红书任务
async function executeXiaohongshuTask(taskId, originalScriptContent, params, deviceIds, schedule, deviceConfigs, functionType) {
  console.log('执行小红书任务:', taskId);
  console.log('目标设备列表:', deviceIds);
  console.log('当前连接的设备数量:', devices.size);
  console.log('设备独立配置:', deviceConfigs ? Object.keys(deviceConfigs).length : 0, '个设备有独立配置');
  console.log('功能类型:', functionType);

  // 检查是否有重复的设备ID
  const uniqueDeviceIds = [...new Set(deviceIds)];
  if (uniqueDeviceIds.length !== deviceIds.length) {
    console.warn(`⚠️ 发现重复的设备ID！原始数量: ${deviceIds.length}, 去重后数量: ${uniqueDeviceIds.length}`);
    console.warn('原始设备列表:', deviceIds);
    console.warn('去重后设备列表:', uniqueDeviceIds);
  }

  // 打印所有连接的设备
  console.log('当前连接的设备:');
  for (const [socketId, deviceData] of devices) {
    console.log(`  - Socket: ${socketId}, DeviceID: ${deviceData.deviceId}, Name: ${deviceData.deviceName}`);
  }

  for (const deviceId of deviceIds) {
    try {
      // 查找设备
      let deviceSocket = null;
      let device = null;

      console.log(`查找设备: ${deviceId}`);

      // 立即更新设备状态为忙碌（在查找设备之前）
      console.log(`[设备状态] 立即更新设备状态为忙碌: ${deviceId}`);
      await updateDeviceStatus(deviceId, 'busy');
      console.log(`[设备状态] 设备状态已更新为忙碌: ${deviceId}`);

      // 查找WebSocket连接的设备
      for (const [socketId, deviceData] of devices) {
        console.log(`  检查设备: ${deviceData.deviceId} === ${deviceId} ?`);
        if (deviceData.deviceId === deviceId) {
          device = deviceData;
          console.log(`  找到设备: ${deviceData.deviceName}, Socket: ${socketId}`);

          // 尝试获取WebSocket连接
          deviceSocket = io.sockets.sockets.get(socketId);
          console.log(`  WebSocket连接状态: ${deviceSocket ? '已连接' : '未连接'}`);

          if (!deviceSocket) {
            // 如果WebSocket连接不存在，检查是否是HTTP连接的设备
            console.log(`  设备 ${deviceId} 通过HTTP连接，尝试使用HTTP方式发送任务`);

            // 注意：日志创建需要在deviceSpecificParams定义之后进行

            // HTTP设备状态已在前面更新为忙碌
            console.log(`[设备状态] HTTP设备状态已更新为忙碌: ${deviceId}`);

            // 对于HTTP连接的设备，我们将任务存储到待执行队列
            if (!pendingCommands.has(deviceId)) {
              pendingCommands.set(deviceId, []);
            }

            // 获取该设备的独立配置
            let deviceSpecificParams = params;
            if (deviceConfigs && deviceConfigs[deviceId]) {
              console.log(`HTTP设备 ${deviceId} 使用独立配置:`, JSON.stringify(deviceConfigs[deviceId], null, 2));
              // 构建设备特定的配置参数
              const deviceConfig = deviceConfigs[deviceId];
              deviceSpecificParams = buildXiaohongshuFunctionConfig(functionType, deviceConfig);
              deviceSpecificParams.function = functionType;
            } else {
              console.log(`HTTP设备 ${deviceId} 使用通用配置:`, JSON.stringify(params, null, 2));
            }

            // 创建执行日志记录（在deviceSpecificParams定义之后）
            console.log(`[日志调试] 准备创建执行日志: taskId=${taskId}, functionType=${functionType}, deviceId=${deviceId}`);
            console.log(`[日志调试] xiaohongshuLogService是否可用:`, !!xiaohongshuLogService);

            if (xiaohongshuLogService) {
              try {
                console.log(`[日志调试] 开始创建执行日志: ${taskId + '_' + deviceId}`);
                console.log(`[日志调试] 参数详情:`, {
                  logId: taskId + '_' + deviceId,
                  functionType: functionType,
                  deviceId: deviceId,
                  deviceName: deviceData.deviceName,
                  configParams: JSON.stringify(deviceSpecificParams).substring(0, 200) + '...',
                  schedule: schedule
                });

                await xiaohongshuLogService.createExecutionLog(
                  taskId + '_' + deviceId,
                  functionType,
                  deviceId,
                  deviceData.deviceName,
                  deviceSpecificParams,
                  schedule
                );
                console.log(`[日志调试] ✅ 执行日志创建成功: ${taskId + '_' + deviceId}`);

                await xiaohongshuLogService.updateExecutionStatus(
                  taskId + '_' + deviceId,
                  'pending',
                  0,
                  '任务排队',
                  `任务已排队，等待设备 ${deviceData.deviceName} 获取`
                );
                console.log(`[日志调试] ✅ 执行状态更新成功: ${taskId + '_' + deviceId}`);
              } catch (logError) {
                console.error('[日志调试] ❌ 创建执行日志失败:', logError.message);
                console.error('[日志调试] ❌ 错误类型:', logError.constructor.name);
                console.error('[日志调试] ❌ 错误代码:', logError.code);
                console.error('[日志调试] ❌ SQL状态:', logError.sqlState);
                console.error('[日志调试] ❌ 完整错误:', logError);

                // 如果是枚举值错误，提供具体的修复建议
                if (logError.message && logError.message.includes('enum')) {
                  console.error('[日志调试] 💡 这是枚举值错误，请执行以下SQL修复:');
                  console.error('[日志调试] 💡 ALTER TABLE xiaohongshu_execution_logs MODIFY COLUMN function_type ENUM(\'profile\', \'groupChat\', \'searchGroupChat\', \'groupMessage\', \'articleComment\', \'uidMessage\', \'uidFileMessage\', \'videoPublish\') NOT NULL;');
                }
              }
            } else {
              console.warn('[日志调试] ⚠️ xiaohongshuLogService不可用，跳过日志创建');
            }

            // 为该设备单独转换脚本
            let deviceSpecificScript;
            if (functionType === 'searchGroupChat') {
              console.log(`=== HTTP设备 ${deviceId} 搜索群聊功能：直接使用无UI脚本 ===`);
              // 为无UI脚本注入设备特定参数
              const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

`;
              deviceSpecificScript = paramInjection + originalScriptContent;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} 参数注入完成，脚本长度:`, deviceSpecificScript.length);
            } else if (functionType === 'uidMessage' || functionType === 'uidFileMessage') {
              console.log(`=== HTTP设备 ${deviceId} ${functionType === 'uidFileMessage' ? '文件上传' : '手动输入'}UID私信功能：直接使用无UI脚本 ===`);

              // 处理UID获取逻辑
              if (deviceSpecificParams.inputMode === 'file' || functionType === 'uidFileMessage') {
                console.log('文件模式：从数据库获取UID列表');

                try {
                  // 使用数据库获取UID
                  if (pool && deviceSpecificParams.selectedFileId) {
                    const connection = await pool.getConnection();

                    try {
                      // 获取可用的UID（包含ID用于后续标记）
                      const maxCount = deviceSpecificParams.totalUidCount || 10;
                      const [uids] = await connection.execute(
                        'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 LIMIT ?',
                        [deviceSpecificParams.selectedFileId, maxCount]
                      );

                      if (uids.length > 0) {
                        // 只分配UID给设备，不立即标记为已使用
                        // 标记为已使用的操作将在私信成功后由手机端上报时执行
                        const uidIds = uids.map(row => row.id);
                        const placeholders = uidIds.map(() => '?').join(',');

                        console.log(`分配UID给设备（不立即标记为已使用）:`);
                        console.log(`  文件ID: ${deviceSpecificParams.selectedFileId}`);
                        console.log(`  设备ID: ${deviceId}`);
                        console.log(`  设备名称: ${deviceSpecificParams.deviceName || deviceId}`);
                        console.log(`  任务ID: ${taskId}`);
                        console.log(`  UID IDs: ${uidIds.join(', ')}`);

                        // 只设置任务ID，不设置设备信息（设备信息将在私信成功时设置）
                        const updateResult = await connection.execute(
                          `UPDATE uid_data SET task_id = ? WHERE id IN (${placeholders})`,
                          [taskId, ...uidIds]
                        );

                        console.log(`UID任务分配结果: 影响行数 ${updateResult[0].affectedRows}`);

                        const availableUids = uids.map(row => row.uid);
                        deviceSpecificParams.uidList = availableUids;

                        console.log(`从数据库文件ID ${deviceSpecificParams.selectedFileId} 分配了 ${availableUids.length} 个UID给任务`);
                        console.log('分配的UID:', availableUids);
                        console.log('分配的UID ID:', uidIds);

                        // 验证分配是否成功
                        const [verifyResult] = await connection.execute(
                          `SELECT COUNT(*) as allocated_count FROM uid_data WHERE id IN (${placeholders}) AND task_id = ?`,
                          [...uidIds, taskId]
                        );
                        console.log(`验证任务分配结果: ${verifyResult[0].allocated_count} 个UID已成功分配给任务`);
                      } else {
                        console.warn(`文件ID ${deviceSpecificParams.selectedFileId} 没有可用的UID`);
                        deviceSpecificParams.uidList = [];
                      }

                    } finally {
                      connection.release();
                    }
                  } else {
                    console.warn('数据库连接不可用或未指定文件ID，使用空UID列表');
                    deviceSpecificParams.uidList = [];
                  }
                } catch (error) {
                  console.error('从数据库获取UID失败:', error);
                  deviceSpecificParams.uidList = [];
                }
              }

              // 添加任务相关信息
              deviceSpecificParams.taskId = taskId + '_' + deviceId;
              deviceSpecificParams.deviceId = deviceId;
              deviceSpecificParams.deviceName = device.deviceName;

              // 添加服务器地址信息，让手机端能够正确上报结果
              // 获取服务器的实际IP地址
              const os = require('os');
              const networkInterfaces = os.networkInterfaces();
              let serverIP = 'localhost';

              // 查找局域网IP地址
              for (const interfaceName in networkInterfaces) {
                const interfaces = networkInterfaces[interfaceName];
                for (const iface of interfaces) {
                  if (iface.family === 'IPv4' && !iface.internal && iface.address.startsWith('192.168.')) {
                    serverIP = iface.address;
                    break;
                  }
                }
                if (serverIP !== 'localhost') break;
              }

              deviceSpecificParams.serverHost = serverIP + ':3002';
              console.log(`为设备 ${deviceId} 设置服务器地址: ${deviceSpecificParams.serverHost}`);

              // UID私信功能是无UI版本，直接注入参数
              const paramInjection = `
// 服务器传递的UID私信参数
const globalConfig = ${JSON.stringify(deviceSpecificParams, null, 2)};

`;
              deviceSpecificScript = paramInjection + originalScriptContent;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} UID私信参数注入完成，脚本长度:`, deviceSpecificScript.length);
            } else if (functionType === 'profile') {
              console.log(`=== HTTP设备 ${deviceId} 修改资料功能：直接使用无UI脚本 ===`);

              // 移除原始的main()调用
              let modifiedScript = originalScriptContent.replace(/main\(\);?\s*$/m, '');

              // 在脚本开头注入参数并直接调用executeScript
              const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + serverParams.onlyNickname);
console.log("只修改简介: " + serverParams.onlyProfile);

try {
    executeScript(serverParams.nickname, serverParams.profile, serverParams.onlyNickname, serverParams.onlyProfile, serverParams);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
}

`;

              deviceSpecificScript = modifiedScript + '\n' + paramInjection;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} 修改资料参数注入完成，脚本长度:`, deviceSpecificScript.length);
            } else if (functionType === 'groupMessage') {
              console.log(`=== HTTP设备 ${deviceId} 循环群发功能：直接使用无UI脚本 ===`);

              // 替换脚本中的CONFIG对象
              let modifiedScript = originalScriptContent.replace(
                /const CONFIG = \{[\s\S]*?\};/,
                `const CONFIG = {
    sendInterval: ${deviceSpecificParams.sendInterval || 10},
    loopMode: ${deviceSpecificParams.executionMode === 'loop'},
    autoSave: true
};`
              );

              // 在脚本末尾添加执行调用
              const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本
console.log("=== 小红书循环群发脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("发送间隔: " + CONFIG.sendInterval + "秒");
console.log("循环模式: " + (CONFIG.loopMode ? "开启" : "关闭"));

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeScript(CONFIG.sendInterval);
    console.log("循环群发脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "循环群发脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("循环群发脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "循环群发脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

              deviceSpecificScript = modifiedScript + executeCall;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} 循环群发参数注入完成，脚本长度:`, deviceSpecificScript.length);
            } else if (functionType === 'articleComment') {
              console.log(`=== HTTP设备 ${deviceId} 文章评论功能：直接使用无UI脚本 ===`);

              // 替换脚本中的CONFIG对象
              let modifiedScript = originalScriptContent.replace(
                /const CONFIG = \{[\s\S]*?\};/,
                `const CONFIG = {
    keyword: "${deviceSpecificParams.searchKeyword || '美食推荐'}",
    commentCount: ${deviceSpecificParams.commentCount || 3},
    delay: ${deviceSpecificParams.operationDelay || 5}
};`
              );

              // 在脚本末尾添加执行调用
              const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本
console.log("=== 小红书文章评论脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("搜索关键词: " + CONFIG.keyword);
console.log("评论文章数量: " + CONFIG.commentCount);
console.log("操作间隔: " + CONFIG.delay + "秒");

// 设置运行状态
isRunning = true;

try {
    executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
} finally {
    isRunning = false;
    console.log("=== 脚本执行结束 ===");
}
`;

              deviceSpecificScript = modifiedScript + executeCall;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} 文章评论参数注入完成，脚本长度:`, deviceSpecificScript.length);
            } else if (functionType === 'videoPublish') {
              console.log('🎬🎬🎬 [HTTP视频发布] ===== HTTP设备视频发布功能开始 ===== 🎬🎬🎬');
              console.log(`📱 [HTTP视频发布] 设备ID: ${deviceId}`);
              console.log(`🆔 [HTTP视频发布] 任务ID: ${taskId}`);
              console.log(`🔧 [HTTP视频发布] 功能类型: ${functionType}`);
              console.log(`🔧 [HTTP视频发布] 设备特定参数:`, JSON.stringify(deviceSpecificParams, null, 2));
              console.log(`📹 [HTTP视频发布] 选中的视频:`, deviceSpecificParams.selectedVideos);
              console.log(`📹 [HTTP视频发布] 视频数量:`, deviceSpecificParams.selectedVideos ? deviceSpecificParams.selectedVideos.length : 0);

              // 为视频发布脚本注入参数，包含选择的视频信息
              console.log(`🔧 [DEBUG] 视频发布脚本：注入参数包含视频信息`);
              console.log(`🔧 [DEBUG] 原始脚本长度:`, originalScriptContent.length);
              console.log(`🔧 [DEBUG] 选择的视频数量:`, deviceSpecificParams.selectedVideos ? deviceSpecificParams.selectedVideos.length : 0);

              // 从前端参数中提取发布配置
              const videoTitle = deviceSpecificParams.titleTemplate || '精彩视频分享';
              const videoDescription = deviceSpecificParams.videoDescription || '分享一个有趣的视频';

              // 处理hashtags字符串，转换为数组
              let videoTags = ['生活', '分享', '有趣']; // 默认标签
              if (deviceSpecificParams.hashtags) {
                // 解析hashtags字符串，例如 "#私域，#创业" 或 "#私域,#创业"
                const hashtagsStr = deviceSpecificParams.hashtags;
                console.log(`🔧 [DEBUG] 原始hashtags:`, hashtagsStr);

                // 分割并清理标签
                videoTags = hashtagsStr
                  .split(/[,，]/) // 支持中英文逗号分割
                  .map(tag => tag.trim().replace(/^#/, '')) // 移除开头的#号和空格
                  .filter(tag => tag.length > 0); // 过滤空标签

                console.log(`🔧 [DEBUG] 处理后的标签:`, videoTags);
              }

              console.log(`🔧 [DEBUG] 视频标题:`, videoTitle);
              console.log(`🔧 [DEBUG] 视频描述:`, videoDescription);
              console.log(`🔧 [DEBUG] 视频标签:`, videoTags);

              // 安全的参数注入 - 在脚本开头直接初始化
              const paramInjection = `
// 服务器传递的参数
var serverParams = {
    deviceId: "${deviceId.replace(/"/g, '\\"')}",
    taskId: "${taskId.replace(/"/g, '\\"')}",
    serverHost: "************:3002",
    selectedVideos: ${JSON.stringify(deviceSpecificParams.selectedVideos || [])},
    videoDescription: "${videoDescription.replace(/"/g, '\\"')}",
    videoTitle: "${videoTitle.replace(/"/g, '\\"')}",
    videoTags: ${JSON.stringify(videoTags)}
};

// 立即初始化配置（在脚本开头）
console.log('准备初始化配置...');

`;

              deviceSpecificScript = paramInjection + originalScriptContent;

              // 替换脚本中的占位符
              deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
              deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

              console.log(`HTTP设备 ${deviceId} 视频发布参数注入完成，脚本长度:`, deviceSpecificScript.length);
              console.log(`🔧 [DEBUG] 最终脚本前200字符:`, deviceSpecificScript.substring(0, 200));

              // 记录视频传输到数据库（在脚本执行前记录，状态为pending）
              console.log('🚀🚀🚀 [脚本执行] 准备记录视频传输到数据库 🚀🚀🚀');
              console.log('📹 [脚本执行] 选中的视频:', deviceSpecificParams.selectedVideos);
              console.log('📱 [脚本执行] 设备ID:', deviceId);
              console.log('🆔 [脚本执行] 任务ID:', taskId);
              await recordVideoTransfersForScript(deviceSpecificParams.selectedVideos, deviceId, taskId, 'pending');
              console.log('✅ [脚本执行] 视频传输记录完成（状态：pending）');
            } else {
              // 其他功能使用原有的脚本转换逻辑
              deviceSpecificScript = convertUIScriptToNonUI(originalScriptContent, functionType, deviceSpecificParams, taskId);
              console.log(`HTTP设备 ${deviceId} 脚本转换完成，脚本长度:`, deviceSpecificScript.length);
            }

            // 检查是否已经有相同的任务在队列中
            const existingCommands = pendingCommands.get(deviceId);
            const duplicateCommand = existingCommands.find(cmd => cmd.logId === taskId + '_' + deviceId);

            if (duplicateCommand) {
              console.warn(`⚠️ 设备 ${deviceId} 已有相同任务在队列中，跳过重复添加: ${taskId + '_' + deviceId}`);
              continue;
            }

            // 为了兼容手机端期望的格式，直接存储script和logId
            pendingCommands.get(deviceId).push({
              logId: taskId + '_' + deviceId,
              script: deviceSpecificScript,
              params: deviceSpecificParams,
              timestamp: new Date()
            });

            console.log(`✅ 任务已添加到设备 ${deviceId} 的待执行队列，当前队列长度: ${pendingCommands.get(deviceId).length}`);

            // 立即更新设备状态为忙碌（特别是视频发布功能）
            if (functionType === 'videoPublish') {
              const deviceData = connectedDevices[deviceId];
              if (deviceData) {
                deviceData.status = 'busy';
                console.log(`🔄 [设备状态] 设备 ${deviceId} 状态已更新为忙碌（视频发布任务）`);

                // 同时更新数据库状态
                await updateDeviceStatus(deviceId, 'busy');
                // updateDeviceStatus函数已经会广播设备状态更新，不需要重复发送
              }
            }

            // 通知Web客户端任务状态
            io.emit('xiaohongshu_task_update', {
              taskId: taskId,
              deviceId: deviceId,
              status: 'queued',
              message: `任务已排队，等待设备 ${deviceData.deviceName} 获取`
            });

            // 发送任务开始事件，包含logId和taskId信息（HTTP设备）
            io.emit('xiaohongshu-task-started', {
              functionType: functionType,
              deviceId: deviceId,
              taskId: taskId,
              logId: taskId + '_' + deviceId,
              message: `开始执行${getXiaohongshuFunctionName(functionType || 'unknown')}任务`
            });
            console.log(`已发送HTTP设备任务开始事件: ${deviceId}, logId: ${taskId + '_' + deviceId}`);

            console.log('已向HTTP设备发送小红书任务:', deviceId, params.function || 'unknown');
            continue;
          }
          break;
        }
      }

      if (!device) {
        console.log('设备未找到:', deviceId);
        console.log('可能的原因:');
        console.log('1. 设备未注册到服务器');
        console.log('2. 设备ID不匹配');

        // 通知Web客户端设备未连接
        io.emit('xiaohongshu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'failed',
          message: `设备 ${deviceId} 未找到`
        });
        continue;
      }

      if (!deviceSocket) {
        // 这种情况已经在上面处理了（HTTP设备）
        continue;
      }

      // WebSocket设备状态已在前面更新为忙碌
      console.log(`[设备状态] WebSocket设备状态已更新为忙碌: ${deviceId}`);

      // 获取该设备的独立配置
      let deviceSpecificParams = params;
      if (deviceConfigs && deviceConfigs[deviceId]) {
        console.log(`WebSocket设备 ${deviceId} 使用独立配置:`, JSON.stringify(deviceConfigs[deviceId], null, 2));
        // 构建设备特定的配置参数
        const deviceConfig = deviceConfigs[deviceId];
        deviceSpecificParams = buildXiaohongshuFunctionConfig(functionType, deviceConfig);
        deviceSpecificParams.function = functionType;
      } else {
        console.log(`WebSocket设备 ${deviceId} 使用通用配置:`, JSON.stringify(params, null, 2));
      }

      // 创建执行日志记录（WebSocket设备，在deviceSpecificParams定义之后）
      if (xiaohongshuLogService) {
        try {
          console.log(`[日志调试] WebSocket设备创建执行日志: ${taskId + '_' + deviceId}`);
          await xiaohongshuLogService.createExecutionLog(
            taskId + '_' + deviceId,
            functionType,
            deviceId,
            device.deviceName,
            deviceSpecificParams,
            schedule
          );
          await xiaohongshuLogService.updateExecutionStatus(
            taskId + '_' + deviceId,
            'running',
            10,
            '开始执行',
            `开始执行${getXiaohongshuFunctionName(functionType || 'unknown')}任务`
          );
          console.log(`[日志调试] ✅ WebSocket设备执行日志创建成功: ${taskId + '_' + deviceId}`);
        } catch (logError) {
          console.error('[日志调试] ❌ WebSocket设备创建执行日志失败:', logError.message);
          console.error('[日志调试] ❌ 完整错误:', logError);
        }
      }

      // 为该设备单独转换脚本
      let deviceSpecificScript;
      if (functionType === 'searchGroupChat') {
        console.log(`=== WebSocket设备 ${deviceId} 搜索群聊功能：直接使用无UI脚本 ===`);
        // 为无UI脚本注入设备特定参数
        const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

`;
        deviceSpecificScript = paramInjection + originalScriptContent;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} 参数注入完成，脚本长度:`, deviceSpecificScript.length);
      } else if (functionType === 'uidMessage' || functionType === 'uidFileMessage') {
        console.log(`=== WebSocket设备 ${deviceId} ${functionType === 'uidFileMessage' ? '文件上传' : '手动输入'}UID私信功能：直接使用无UI脚本 ===`);

        // 处理UID获取逻辑
        if (deviceSpecificParams.inputMode === 'file' || functionType === 'uidFileMessage') {
          console.log('文件模式：从数据库获取UID列表');

          try {
            // 使用数据库获取UID
            if (pool && deviceSpecificParams.selectedFileId) {
              const connection = await pool.getConnection();

              try {
                // 获取可用的UID（包含ID用于后续标记）
                const maxCount = deviceSpecificParams.totalUidCount || 10;
                const [uids] = await connection.execute(
                  'SELECT id, uid FROM uid_data WHERE file_id = ? AND is_used = 0 LIMIT ?',
                  [deviceSpecificParams.selectedFileId, maxCount]
                );

                if (uids.length > 0) {
                  // 只分配UID给设备，不立即标记为已使用
                  // 标记为已使用的操作将在私信成功后由手机端上报时执行
                  const uidIds = uids.map(row => row.id);
                  const placeholders = uidIds.map(() => '?').join(',');

                  console.log(`[WebSocket] 分配UID给设备（不立即标记为已使用）:`);
                  console.log(`  文件ID: ${deviceSpecificParams.selectedFileId}`);
                  console.log(`  设备ID: ${deviceId}`);
                  console.log(`  设备名称: ${deviceSpecificParams.deviceName || deviceId}`);
                  console.log(`  任务ID: ${taskId}`);
                  console.log(`  UID IDs: ${uidIds.join(', ')}`);

                  // 只设置任务ID，不设置设备信息（设备信息将在私信成功时设置）
                  const updateResult = await connection.execute(
                    `UPDATE uid_data SET task_id = ? WHERE id IN (${placeholders})`,
                    [taskId, ...uidIds]
                  );

                  console.log(`[WebSocket] UID任务分配结果: 影响行数 ${updateResult[0].affectedRows}`);

                  const availableUids = uids.map(row => row.uid);
                  deviceSpecificParams.uidList = availableUids;

                  console.log(`[WebSocket] 从数据库文件ID ${deviceSpecificParams.selectedFileId} 分配了 ${availableUids.length} 个UID给任务`);
                  console.log('[WebSocket] 分配的UID:', availableUids);
                  console.log('[WebSocket] 分配的UID ID:', uidIds);

                  // 验证分配是否成功
                  const [verifyResult] = await connection.execute(
                    `SELECT COUNT(*) as allocated_count FROM uid_data WHERE id IN (${placeholders}) AND task_id = ?`,
                    [...uidIds, taskId]
                  );
                  console.log(`[WebSocket] 验证任务分配结果: ${verifyResult[0].allocated_count} 个UID已成功分配给任务`);
                } else {
                  console.warn(`文件ID ${deviceSpecificParams.selectedFileId} 没有可用的UID`);
                  deviceSpecificParams.uidList = [];
                }

              } finally {
                connection.release();
              }
            } else {
              console.warn('数据库连接不可用或未指定文件ID，使用空UID列表');
              deviceSpecificParams.uidList = [];
            }
          } catch (error) {
            console.error('从数据库获取UID失败:', error);
            deviceSpecificParams.uidList = [];
          }
        }

        // 添加任务相关信息
        deviceSpecificParams.taskId = taskId + '_' + deviceId;
        deviceSpecificParams.deviceId = deviceId;
        deviceSpecificParams.deviceName = device.deviceName;

        // 添加服务器地址信息，让手机端能够正确上报结果
        // 获取服务器的实际IP地址
        const os = require('os');
        const networkInterfaces = os.networkInterfaces();
        let serverIP = 'localhost';

        // 查找局域网IP地址
        for (const interfaceName in networkInterfaces) {
          const interfaces = networkInterfaces[interfaceName];
          for (const iface of interfaces) {
            if (iface.family === 'IPv4' && !iface.internal && iface.address.startsWith('192.168.')) {
              serverIP = iface.address;
              break;
            }
          }
          if (serverIP !== 'localhost') break;
        }

        deviceSpecificParams.serverHost = serverIP + ':3002';
        console.log(`[WebSocket] 为设备 ${deviceId} 设置服务器地址: ${deviceSpecificParams.serverHost}`);

        // UID私信功能是无UI版本，直接注入参数
        const paramInjection = `
// 服务器传递的UID私信参数
const globalConfig = ${JSON.stringify(deviceSpecificParams, null, 2)};

`;
        deviceSpecificScript = paramInjection + originalScriptContent;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} UID私信参数注入完成，脚本长度:`, deviceSpecificScript.length);
      } else if (functionType === 'profile') {
        console.log(`=== WebSocket设备 ${deviceId} 修改资料功能：直接使用无UI脚本 ===`);

        // 移除原始的main()调用
        let modifiedScript = originalScriptContent.replace(/main\(\);?\s*$/m, '');

        // 在脚本开头注入参数并直接调用executeScript
        const paramInjection = `
// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本，跳过UI输入
console.log("=== 小红书资料修改脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("昵称: " + serverParams.nickname);
console.log("简介: " + serverParams.profile);
console.log("只修改昵称: " + serverParams.onlyNickname);
console.log("只修改简介: " + serverParams.onlyProfile);

try {
    executeScript(serverParams.nickname, serverParams.profile, serverParams.onlyNickname, serverParams.onlyProfile, serverParams);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
}

`;

        deviceSpecificScript = modifiedScript + '\n' + paramInjection;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} 修改资料参数注入完成，脚本长度:`, deviceSpecificScript.length);
      } else if (functionType === 'groupMessage') {
        console.log(`=== WebSocket设备 ${deviceId} 循环群发功能：直接使用无UI脚本 ===`);

        // 替换脚本中的CONFIG对象
        let modifiedScript = originalScriptContent.replace(
          /const CONFIG = \{[\s\S]*?\};/,
          `const CONFIG = {
    sendInterval: ${deviceSpecificParams.sendInterval || 10},
    loopMode: ${deviceSpecificParams.executionMode === 'loop'},
    autoSave: true
};`
        );

        // 在脚本末尾添加执行调用
        const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本
console.log("=== 小红书循环群发脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("发送间隔: " + CONFIG.sendInterval + "秒");
console.log("循环模式: " + (CONFIG.loopMode ? "开启" : "关闭"));

// 设置运行状态
isRunning = true;

// 执行主函数
try {
    executeScript(CONFIG.sendInterval);
    console.log("循环群发脚本执行完成");

    // 发送执行完成状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "success",
                progress: 100,
                message: "循环群发脚本执行完成",
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行完成状态上报成功");
        } catch (e) {
            console.log("执行完成状态上报失败: " + e.message);
        }
    });

} catch (error) {
    console.log("循环群发脚本执行失败: " + error.message);

    // 发送执行失败状态到服务器
    threads.start(function() {
        try {
            http.postJson("http://************:3002/api/xiaohongshu/status", {
                taskId: "TASK_ID_PLACEHOLDER",
                deviceId: "DEVICE_ID_PLACEHOLDER",
                stage: "completed",
                status: "error",
                progress: 0,
                message: "循环群发脚本执行失败: " + error.message,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    "Content-Type": "application/json"
                },
                timeout: 5000
            });
            console.log("执行失败状态上报成功");
        } catch (e) {
            console.log("执行失败状态上报失败: " + e.message);
        }
    });

    throw error;
}
`;

        deviceSpecificScript = modifiedScript + executeCall;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} 循环群发参数注入完成，脚本长度:`, deviceSpecificScript.length);
      } else if (functionType === 'articleComment') {
        console.log(`=== WebSocket设备 ${deviceId} 文章评论功能：直接使用无UI脚本 ===`);

        // 替换脚本中的CONFIG对象
        let modifiedScript = originalScriptContent.replace(
          /const CONFIG = \{[\s\S]*?\};/,
          `const CONFIG = {
    keyword: "${deviceSpecificParams.searchKeyword || '美食推荐'}",
    commentCount: ${deviceSpecificParams.commentCount || 3},
    delay: ${deviceSpecificParams.operationDelay || 5}
};`
        );

        // 在脚本末尾添加执行调用
        const executeCall = `

// 服务器传递的参数
const serverParams = ${JSON.stringify(deviceSpecificParams, null, 2)};

// 直接执行脚本
console.log("=== 小红书文章评论脚本启动（无UI模式）===");

// 检查无障碍服务
if (!checkAccessibilityService()) {
    console.log("请开启无障碍服务后重新运行脚本");
    throw new Error("无障碍服务未开启");
}

console.log("开始执行脚本...");
console.log("搜索关键词: " + CONFIG.keyword);
console.log("评论文章数量: " + CONFIG.commentCount);
console.log("操作间隔: " + CONFIG.delay + "秒");

// 设置运行状态
isRunning = true;

try {
    executeSearchComment(CONFIG.keyword, CONFIG.commentCount, CONFIG.delay);
    console.log("脚本执行完成");
} catch (e) {
    console.error("脚本执行出错: " + e.message);
    throw e;
} finally {
    isRunning = false;
    console.log("=== 脚本执行结束 ===");
}
`;

        deviceSpecificScript = modifiedScript + executeCall;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} 文章评论参数注入完成，脚本长度:`, deviceSpecificScript.length);
      } else if (functionType === 'videoPublish') {
        console.log('🎬🎬🎬 [WebSocket视频发布] ===== WebSocket设备视频发布功能开始 ===== 🎬🎬🎬');
        console.log(`📱 [WebSocket视频发布] 设备ID: ${deviceId}`);
        console.log(`🆔 [WebSocket视频发布] 任务ID: ${taskId}`);
        console.log(`🔧 [WebSocket视频发布] 功能类型: ${functionType}`);
        console.log(`🔧 [WebSocket视频发布] 设备特定参数:`, JSON.stringify(deviceSpecificParams, null, 2));
        console.log(`📹 [WebSocket视频发布] 选中的视频:`, deviceSpecificParams.selectedVideos);
        console.log(`📹 [WebSocket视频发布] 视频数量:`, deviceSpecificParams.selectedVideos ? deviceSpecificParams.selectedVideos.length : 0);

        // 为WebSocket视频发布脚本注入参数，包含选择的视频信息
        console.log(`🔧 [DEBUG] WebSocket视频发布脚本：注入参数包含视频信息`);
        console.log(`🔧 [DEBUG] WebSocket原始脚本长度:`, originalScriptContent.length);
        console.log(`🔧 [DEBUG] WebSocket选择的视频数量:`, deviceSpecificParams.selectedVideos ? deviceSpecificParams.selectedVideos.length : 0);

        // 从前端参数中提取发布配置
        const videoTitle = deviceSpecificParams.titleTemplate || '精彩视频分享';
        const videoDescription = deviceSpecificParams.videoDescription || '分享一个有趣的视频';

        // 处理hashtags字符串，转换为数组
        let videoTags = ['生活', '分享', '有趣']; // 默认标签
        if (deviceSpecificParams.hashtags) {
          // 解析hashtags字符串，例如 "#私域，#创业" 或 "#私域,#创业"
          const hashtagsStr = deviceSpecificParams.hashtags;
          console.log(`🔧 [DEBUG] WebSocket原始hashtags:`, hashtagsStr);

          // 分割并清理标签
          videoTags = hashtagsStr
            .split(/[,，]/) // 支持中英文逗号分割
            .map(tag => tag.trim().replace(/^#/, '')) // 移除开头的#号和空格
            .filter(tag => tag.length > 0); // 过滤空标签

          console.log(`🔧 [DEBUG] WebSocket处理后的标签:`, videoTags);
        }

        console.log(`🔧 [DEBUG] WebSocket视频标题:`, videoTitle);
        console.log(`🔧 [DEBUG] WebSocket视频描述:`, videoDescription);
        console.log(`🔧 [DEBUG] WebSocket视频标签:`, videoTags);

        // 安全的参数注入 - 在脚本开头直接初始化
        const paramInjection = `
// 服务器传递的参数
var serverParams = {
    deviceId: "${deviceId.replace(/"/g, '\\"')}",
    taskId: "${taskId.replace(/"/g, '\\"')}",
    serverHost: "************:3002",
    selectedVideos: ${JSON.stringify(deviceSpecificParams.selectedVideos || [])},
    videoDescription: "${videoDescription.replace(/"/g, '\\"')}",
    videoTitle: "${videoTitle.replace(/"/g, '\\"')}",
    videoTags: ${JSON.stringify(videoTags)}
};

// 立即初始化配置（在脚本开头）
console.log('WebSocket准备初始化配置...');

`;

        deviceSpecificScript = paramInjection + originalScriptContent;

        // 替换脚本中的占位符
        deviceSpecificScript = deviceSpecificScript.replace(/DEVICE_ID_PLACEHOLDER/g, deviceId);
        deviceSpecificScript = deviceSpecificScript.replace(/TASK_ID_PLACEHOLDER/g, taskId);

        console.log(`WebSocket设备 ${deviceId} 视频发布参数注入完成，脚本长度:`, deviceSpecificScript.length);
        console.log(`🔧 [DEBUG] WebSocket最终脚本前200字符:`, deviceSpecificScript.substring(0, 200));

        // 记录视频传输到数据库（在脚本执行前记录，状态为pending）
        console.log('🚀🚀🚀 [WebSocket脚本执行] 准备记录视频传输到数据库 🚀🚀🚀');
        console.log('📹 [WebSocket脚本执行] 选中的视频:', deviceSpecificParams.selectedVideos);
        console.log('📱 [WebSocket脚本执行] 设备ID:', deviceId);
        console.log('🆔 [WebSocket脚本执行] 任务ID:', taskId);
        await recordVideoTransfersForScript(deviceSpecificParams.selectedVideos, deviceId, taskId, 'pending');
        console.log('✅ [WebSocket脚本执行] 视频传输记录完成（状态：pending）');
      } else {
        // 其他功能使用原有的脚本转换逻辑
        deviceSpecificScript = convertUIScriptToNonUI(originalScriptContent, functionType, deviceSpecificParams, taskId);
        console.log(`WebSocket设备 ${deviceId} 脚本转换完成，脚本长度:`, deviceSpecificScript.length);
      }

      // 发送脚本执行命令
      const executeData = {
        logId: taskId + '_' + deviceId,
        script: deviceSpecificScript,
        params: deviceSpecificParams
      };

      deviceSocket.emit('execute_script', executeData);

      // 立即更新设备状态为忙碌（特别是视频发布功能）
      if (functionType === 'videoPublish') {
        device.status = 'busy';
        console.log(`🔄 [设备状态] WebSocket设备 ${deviceId} 状态已更新为忙碌（视频发布任务）`);

        // 同时更新数据库状态
        await updateDeviceStatus(deviceId, 'busy');
        // updateDeviceStatus函数已经会广播设备状态更新，不需要重复发送
      }

      // 通知Web客户端任务状态
      io.emit('xiaohongshu_task_update', {
        taskId: taskId,
        deviceId: deviceId,
        status: 'executing',
        message: `正在执行${getXiaohongshuFunctionName(functionType || 'unknown')}任务`
      });

      // 发送任务开始事件，包含logId和taskId信息
      io.emit('xiaohongshu-task-started', {
        functionType: functionType,
        deviceId: deviceId,
        taskId: taskId,
        logId: taskId + '_' + deviceId,
        message: `开始执行${getXiaohongshuFunctionName(functionType || 'unknown')}任务`
      });
      console.log(`已发送任务开始事件: ${deviceId}, logId: ${taskId + '_' + deviceId}`);

      console.log('已向设备发送小红书任务:', deviceId, params.function || 'unknown');

    } catch (error) {
      console.error('向设备发送任务失败:', deviceId, error);

      io.emit('xiaohongshu_task_update', {
        taskId: taskId,
        deviceId: deviceId,
        status: 'failed',
        message: '发送任务失败: ' + error.message
      });
    }
  }
}

// 获取功能名称
function getXiaohongshuFunctionName(functionType) {
  const nameMap = {
    'profile': '修改资料',
    'groupChat': '搜索加群',
    'searchGroupChat': '搜索加群',  // 添加搜索加群功能映射
    'groupMessage': '循环群发',
    'articleComment': '文章评论',
    'uidMessage': 'UID私信'
  };
  return nameMap[functionType] || functionType;
}

// 设备停止信号存储
let deviceStopSignals = new Map(); // deviceId -> { shouldStop: boolean, timestamp: Date }

// 清理特定设备的小红书任务
async function cleanupXiaohongshuTasksForDevice(deviceId) {
  console.log(`清理设备 ${deviceId} 相关的小红书任务`);

  let cleanedTasksCount = 0;

  // 首先更新数据库中该设备正在执行的任务记录
  if (pool) {
    try {
      console.log(`更新设备 ${deviceId} 的执行日志为出错状态`);

      // 查询该设备正在执行的小红书任务
      const [runningLogs] = await pool.execute(`
        SELECT task_id, function_type, started_at
        FROM xiaohongshu_execution_logs
        WHERE device_id = ? AND execution_status = 'running'
      `, [deviceId]);

      if (runningLogs.length > 0) {
        console.log(`发现设备 ${deviceId} 有 ${runningLogs.length} 个正在执行的任务`);

        // 更新所有正在执行的任务为出错状态
        await pool.execute(`
          UPDATE xiaohongshu_execution_logs
          SET execution_status = 'error',
              progress_percentage = 0,
              error_message = '设备断开连接',
              execution_result = '执行失败',
              completed_at = NOW(),
              execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
          WHERE device_id = ? AND execution_status = 'running'
        `, [deviceId]);

        console.log(`已将设备 ${deviceId} 的 ${runningLogs.length} 个执行任务标记为出错状态`);

        // 为每个任务发送状态更新通知
        for (const log of runningLogs) {
          const logId = `${log.task_id}_${deviceId}`;
          console.log(`发送任务出错通知: ${logId}`);

          // 通知Web客户端任务出错
          io.emit('xiaohongshu_task_update', {
            taskId: log.task_id,
            logId: logId,
            deviceId: deviceId,
            status: 'error',
            progress: 0,
            message: '设备断开连接，脚本执行失败',
            errorMessage: '设备断开连接',
            timestamp: new Date()
          });
        }
      } else {
        console.log(`设备 ${deviceId} 没有正在执行的小红书任务`);
      }

    } catch (dbError) {
      console.error(`更新设备 ${deviceId} 执行日志失败:`, dbError);
    }
  }

  // 遍历所有活跃任务，找到包含该设备的任务
  for (const [taskId, task] of xiaohongshuActiveTasks) {
    if (task.devices && task.devices.includes(deviceId)) {
      console.log(`发现任务 ${taskId} 包含设备 ${deviceId}`);

      // 从任务的设备列表中移除该设备
      task.devices = task.devices.filter(id => id !== deviceId);

      // 如果任务没有剩余设备，则停止整个任务
      if (task.devices.length === 0) {
        console.log(`任务 ${taskId} 没有剩余设备，停止任务`);
        task.status = 'stopped';
        task.stoppedAt = new Date();
        task.stopReason = `设备 ${deviceId} 断开连接，无剩余设备`;

        // 通知Web客户端任务已停止
        io.emit('xiaohongshu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'stopped',
          message: `设备断开连接，任务已停止`
        });

        // 从活跃任务中移除
        xiaohongshuActiveTasks.delete(taskId);
        cleanedTasksCount++;
      } else {
        console.log(`任务 ${taskId} 还有 ${task.devices.length} 个设备，继续执行`);

        // 通知Web客户端该设备的任务状态
        io.emit('xiaohongshu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'device_disconnected',
          message: `设备已断开连接`
        });
      }
    }
  }

  // 清理该设备的待执行命令
  if (pendingCommands.has(deviceId)) {
    pendingCommands.delete(deviceId);
    console.log(`已清理设备 ${deviceId} 的待执行命令`);
  }

  // 清理该设备的停止信号
  if (deviceStopSignals.has(deviceId)) {
    deviceStopSignals.delete(deviceId);
    console.log(`已清理设备 ${deviceId} 的停止信号`);
  }

  if (cleanedTasksCount > 0) {
    console.log(`已清理 ${cleanedTasksCount} 个任务，设备: ${deviceId}`);

    // 通知Web客户端任务状态变化
    io.emit('xiaohongshu_all_tasks_stopped');
  }
}

// 清理特定设备的闲鱼任务
async function cleanupXianyuTasksForDevice(deviceId) {
  console.log(`清理设备 ${deviceId} 相关的闲鱼任务`);

  let cleanedTasksCount = 0;

  // 首先更新数据库中该设备正在执行的任务记录
  if (pool && xianyuLogService) {
    try {
      console.log(`更新设备 ${deviceId} 的闲鱼执行日志为出错状态`);

      // 添加延迟以避免竞争条件
      await new Promise(resolve => setTimeout(resolve, 100));

      // 查询该设备正在执行的闲鱼任务
      console.log(`查询设备 ${deviceId} 的正在执行的闲鱼任务...`);
      console.log('调用参数:', {
        page: 1,
        limit: 50,
        filters: {
          deviceId: deviceId,
          executionStatus: 'running'
        }
      });

      let runningLogs;
      try {
        runningLogs = await xianyuLogService.getExecutionLogs(1, 50, {
          deviceId: deviceId,
          executionStatus: 'running'
        });
        console.log('✅ 闲鱼任务查询成功');
      } catch (queryError) {
        console.error('❌ 闲鱼任务查询失败:', queryError.message);
        console.error('查询错误详情:', {
          code: queryError.code,
          errno: queryError.errno,
          sql: queryError.sql,
          sqlState: queryError.sqlState,
          sqlMessage: queryError.sqlMessage
        });
        throw queryError; // 重新抛出错误
      }

      console.log(`闲鱼任务查询结果:`, {
        success: !!runningLogs,
        total: runningLogs?.total || 0,
        logsCount: runningLogs?.logs?.length || 0
      });

      if (runningLogs && runningLogs.logs && runningLogs.logs.length > 0) {
        console.log(`发现设备 ${deviceId} 有 ${runningLogs.logs.length} 个正在执行的闲鱼任务`);

        // 更新所有正在执行的任务为出错状态
        for (const log of runningLogs.logs) {
          try {
            console.log(`更新闲鱼任务状态: ${log.id} -> failed (进度: ${log.progress}% -> 0%)`);

            await xianyuLogService.updateExecutionStatus(
              log.id,
              'failed',  // 执行状态改为失败（数据库ENUM类型只允许failed，不允许error）
              0,        // 进度重置为0
              '设备断开连接',  // 阶段描述
              null,     // debugLogs
              '设备断开连接'  // 错误消息
            );

            console.log(`✅ 闲鱼任务 ${log.id} 已标记为出错状态`);
          } catch (updateError) {
            console.error(`更新闲鱼任务 ${log.id} 状态失败:`, updateError.message);
          }

          // 发送状态更新通知
          io.emit('xianyu_status_update', {
            taskId: log.id,
            deviceId: deviceId,
            stage: 'error',
            status: '设备断开连接',
            progress: 0,
            message: '设备断开连接，脚本执行失败',
            errorMessage: '设备断开连接',
            timestamp: new Date()
          });
        }

        console.log(`已将设备 ${deviceId} 的 ${runningLogs.logs.length} 个闲鱼执行任务标记为出错状态`);
      } else {
        console.log(`设备 ${deviceId} 没有正在执行的闲鱼任务`);
      }

    } catch (dbError) {
      console.error(`更新设备 ${deviceId} 闲鱼执行日志失败:`, dbError);
      console.error('错误详情:', {
        message: dbError.message,
        code: dbError.code,
        errno: dbError.errno,
        sql: dbError.sql,
        sqlMessage: dbError.sqlMessage
      });
    }
  }

  // 遍历所有活跃任务，找到包含该设备的任务
  for (const [taskId, task] of xianyuActiveTasks) {
    if (task.devices && task.devices.includes(deviceId)) {
      console.log(`发现闲鱼任务 ${taskId} 包含设备 ${deviceId}`);

      // 从任务的设备列表中移除该设备
      task.devices = task.devices.filter(id => id !== deviceId);

      // 如果任务没有剩余设备，则停止整个任务
      if (task.devices.length === 0) {
        console.log(`闲鱼任务 ${taskId} 没有剩余设备，停止任务`);
        task.status = 'stopped';
        task.stoppedAt = new Date();
        task.stopReason = `设备 ${deviceId} 断开连接，无剩余设备`;

        // 通知Web客户端任务已停止
        io.emit('xianyu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'stopped',
          message: `设备断开连接，任务已停止`
        });

        // 从活跃任务中移除
        xianyuActiveTasks.delete(taskId);
        cleanedTasksCount++;
      } else {
        console.log(`闲鱼任务 ${taskId} 还有 ${task.devices.length} 个设备，继续执行`);

        // 通知Web客户端该设备的任务状态
        io.emit('xianyu_task_update', {
          taskId: taskId,
          deviceId: deviceId,
          status: 'device_disconnected',
          message: `设备已断开连接`
        });
      }
    }
  }

  // 清理该设备的待执行命令
  if (pendingCommands.has(deviceId)) {
    pendingCommands.delete(deviceId);
    console.log(`已清理设备 ${deviceId} 的待执行命令`);
  }

  if (cleanedTasksCount > 0) {
    console.log(`已清理 ${cleanedTasksCount} 个闲鱼任务，设备: ${deviceId}`);

    // 通知Web客户端任务状态变化
    io.emit('xianyu_all_tasks_stopped');
  }
}

// 设备检查停止信号API
app.get('/api/device/check-stop', (req, res) => {
  try {
    const { deviceId } = req.query;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID'
      });
    }

    const stopSignal = deviceStopSignals.get(deviceId);

    res.json({
      success: true,
      shouldStop: stopSignal ? stopSignal.shouldStop : false,
      timestamp: stopSignal ? stopSignal.timestamp : null
    });

  } catch (error) {
    console.error('检查停止信号失败:', error);
    res.status(500).json({
      success: false,
      message: '检查失败: ' + error.message
    });
  }
});

// 设备通知脚本已停止API
app.post('/api/device/script-stopped', (req, res) => {
  try {
    const { deviceId, timestamp } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID'
      });
    }

    console.log(`设备 ${deviceId} 报告脚本已停止`);

    // 清除停止信号
    deviceStopSignals.delete(deviceId);

    // 通知Web客户端设备脚本已停止
    io.emit('device_script_stopped', {
      deviceId: deviceId,
      timestamp: timestamp || new Date()
    });

    res.json({
      success: true,
      message: '已确认脚本停止'
    });

  } catch (error) {
    console.error('处理脚本停止通知失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});

// 接收脚本执行状态更新API
app.post('/api/xiaohongshu/status', async (req, res) => {
  try {
    const { deviceId, status, progress, message, stage, taskId, debugInfo } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID参数'
      });
    }

    // 构造状态更新数据
    const statusData = {
      deviceId: deviceId,
      status: status,
      progress: progress,
      message: message,
      stage: stage,
      taskId: taskId,
      debugInfo: debugInfo,
      timestamp: new Date().toISOString()
    };

    // 更新小红书执行日志
    if (xiaohongshuLogService && taskId) {
      try {
        // 首先检查任务是否已经被手动停止
        const currentLog = await xiaohongshuLogService.getExecutionLogDetail(taskId);

        if (currentLog && currentLog.executionStatus === 'stopped') {
          console.log(`⚠️ 任务 ${taskId} 已被手动停止，忽略状态更新: ${stage} - ${status}`);

          // 如果手机端发送的是停止状态，允许更新
          if (stage === 'stopped' || status === 'stopped') {
            console.log(`✅ 手机端确认停止状态，允许更新: ${taskId}`);
          } else {
            // 忽略其他状态更新，保持停止状态
            console.log(`🚫 忽略状态更新，保持停止状态: ${taskId}`);

            res.json({
              success: true,
              message: '任务已停止，忽略状态更新'
            });
            return;
          }
        }

        // 根据stage确定执行状态
        let executionStatus = 'running';
        if (stage === 'completed') {
          // 判断是否成功：status为'success'或者message中包含成功信息
          const isSuccess = status === 'success' ||
                           (message && (
                             message.includes('成功') ||
                             message.includes('完成') ||
                             message.includes('执行完成')
                           ));
          executionStatus = isSuccess ? 'completed' : 'failed';
        } else if (stage === 'error') {
          executionStatus = 'failed';
        } else if (stage === 'stopped' || status === 'stopped') {
          executionStatus = 'stopped';
        }

        await xiaohongshuLogService.updateExecutionStatus(
          taskId,
          executionStatus,
          progress || 0,
          stage || '执行中',
          message
        );

        console.log(`小红书执行日志已更新: ${taskId} -> ${executionStatus} (${progress}%)`);
      } catch (logError) {
        console.error('更新小红书执行日志失败:', logError);
      }
    }

    // 通过WebSocket发送状态更新到前端
    io.emit('xiaohongshu_status_update', statusData);

    console.log(`脚本状态更新 [${deviceId}]: ${stage} - ${status} (${progress}%) - ${message}`);

    // 如果脚本执行完成、出错或停止，立即处理设备状态和前端通知
    if (stage === 'completed' || stage === 'error' || stage === 'stopped') {
      console.log(`脚本执行${stage === 'completed' ? '完成' : stage === 'error' ? '出错' : '停止'}，恢复设备状态: ${deviceId}`);

      // 立即恢复设备状态为在线，不再延迟
      try {
        // 直接使用updateDeviceStatus函数更新设备状态
        await updateDeviceStatus(deviceId, 'online');
        console.log(`设备状态已立即恢复为在线: ${deviceId}`);

        // 发送设备状态更新事件到前端
        io.emit('device_status_update', {
          deviceId: deviceId,
          status: 'online',
          lastSeen: new Date()
        });

        // 🔥 新增：通知前端重置脚本执行状态
        if (stage === 'stopped') {
          console.log(`通知前端重置脚本状态: ${deviceId}`);

          // 从taskId中提取functionType，或者从数据库获取
          let extractedFunctionType = 'unknown';
          try {
            if (taskId) {
              // 从taskId中提取functionType (格式: xiaohongshu_functionType_timestamp_deviceId)
              const taskIdParts = taskId.split('_');
              if (taskIdParts.length >= 2) {
                extractedFunctionType = taskIdParts[1];
              } else {
                // 如果taskId格式不对，尝试从数据库获取
                const currentLog = await xiaohongshuLogService.getExecutionLogDetail(taskId);
                if (currentLog && currentLog.functionType) {
                  extractedFunctionType = currentLog.functionType;
                }
              }
            }
          } catch (error) {
            console.warn(`⚠️ 提取functionType失败，使用默认值: ${error.message}`);
          }

          io.emit('xiaohongshu_script_reset', {
            deviceId: deviceId,
            functionType: extractedFunctionType,
            taskId: taskId,
            reason: '脚本已停止，重置执行状态',
            timestamp: new Date().toISOString()
          });

          // 🔥 新增：通知前端更新Vuex状态
          io.emit('xiaohongshu_vuex_state_update', {
            action: 'stopTask',
            functionType: extractedFunctionType,
            deviceId: deviceId,
            taskId: taskId,
            reason: '脚本已停止',
            timestamp: new Date().toISOString()
          });

          console.log(`✅ 已通知前端重置设备 ${deviceId} 的脚本状态和Vuex状态 (功能: ${extractedFunctionType})`);
        }

        // 通知设备端脚本已完成，停止上报忙碌状态
        const deviceSocket = findDeviceSocket(deviceId);
        if (deviceSocket) {
          deviceSocket.emit('script_execution_completed', {
            taskId: taskId,
            status: stage,
            message: '脚本执行已完成，请停止上报忙碌状态',
            timestamp: new Date().toISOString()
          });
          console.log(`已通知设备端脚本完成: ${deviceId}`);
        }

        // 对于HTTP设备，添加脚本完成通知到命令队列
        if (pendingCommands.has(deviceId)) {
          pendingCommands.get(deviceId).push({
            type: 'script_execution_completed',
            taskId: taskId,
            status: stage,
            message: '脚本执行已完成，请停止上报忙碌状态',
            timestamp: new Date().toISOString()
          });
          console.log(`已添加脚本完成通知到HTTP设备命令队列: ${deviceId}`);
        }
      } catch (error) {
        console.error('恢复设备状态失败:', error);
      }

      // 通知前端重置脚本执行状态
      // 从taskId中提取functionType
      let functionType = 'unknown';
      if (taskId && taskId.includes('_')) {
        const parts = taskId.split('_');
        if (parts.length >= 2) {
          functionType = parts[1]; // xiaohongshu_uidMessage_xxx -> uidMessage
        }
      }

      // 发送脚本完成事件（使用连字符格式，与前端监听一致）
      let eventStatus = 'failed';
      if (stage === 'completed') {
        eventStatus = 'success';
      } else if (stage === 'stopped') {
        eventStatus = 'stopped';
      }

      // 发送两种格式的事件，确保兼容性
      io.emit('xiaohongshu-script-completed', {
        deviceId: deviceId,
        taskId: taskId,
        functionType: functionType,
        status: eventStatus,
        message: message,
        timestamp: new Date().toISOString()
      });

      // 添加下划线格式的事件，与前端监听一致
      io.emit('xiaohongshu_execution_completed', {
        deviceId: deviceId,
        taskId: taskId,
        functionType: functionType,
        status: eventStatus,
        message: message,
        timestamp: new Date().toISOString()
      });

      console.log(`已发送脚本完成事件: deviceId=${deviceId}, functionType=${functionType}, status=${eventStatus}`);

      // 延迟清理任务状态
      console.log(`脚本执行完成，将在60秒后清理任务状态: ${taskId}`);
      setTimeout(() => {
        // 清理活跃任务
        if (taskId && xiaohongshuActiveTasks.has(taskId)) {
          const task = xiaohongshuActiveTasks.get(taskId);
          task.status = 'completed';
          task.completedAt = new Date();

          // 移动到历史记录
          xiaohongshuTaskHistory.unshift({
            ...task,
            completedAt: new Date()
          });

          // 从活跃任务中移除
          xiaohongshuActiveTasks.delete(taskId);

          console.log(`任务 ${taskId} 已清理并移动到历史记录`);
        }
      }, 60000); // 60秒后清理
    }

    res.json({
      success: true,
      message: '状态更新已接收'
    });

  } catch (error) {
    console.error('处理脚本状态更新失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});

// 接收闲鱼脚本执行状态更新API
app.post('/api/xianyu/status', async (req, res) => {
  try {
    const { deviceId, status, progress, message, stage, taskId, debugInfo } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID参数'
      });
    }

    // 构造状态更新数据
    const statusData = {
      deviceId: deviceId,
      status: status,
      progress: progress,
      message: message,
      stage: stage,
      taskId: taskId,
      debugInfo: debugInfo,
      timestamp: new Date().toISOString()
    };

    // 更新闲鱼执行日志
    if (xianyuLogService && taskId) {
      try {
        // 首先检查任务是否已经被手动停止
        const currentLog = await xianyuLogService.getExecutionLogDetail(taskId);

        if (currentLog && currentLog.executionStatus === 'stopped') {
          console.log(`⚠️ 任务 ${taskId} 已被手动停止，忽略状态更新: ${stage} - ${status}`);

          // 如果手机端发送的是停止状态，允许更新
          if (stage === 'stopped' || status === 'stopped') {
            console.log(`✅ 手机端确认停止状态，允许更新: ${taskId}`);
          } else {
            // 忽略其他状态更新，保持停止状态
            console.log(`🚫 忽略状态更新，保持停止状态: ${taskId}`);

            res.json({
              success: true,
              message: '任务已停止，忽略状态更新'
            });
            return;
          }
        }

        // 根据stage确定执行状态
        let executionStatus = 'running';
        if (stage === 'completed') {
          // 判断是否成功完成
          const isSuccess = status === 'success' ||
                           status === '完成' ||
                           status === 'completed' ||
                           (message && (
                             message.includes('执行完成') ||
                             message.includes('成功') ||
                             message.includes('完成') ||
                             message.includes('脚本执行完成')
                           ));
          executionStatus = isSuccess ? 'completed' : 'failed';
          console.log(`[闲鱼状态更新] stage=completed, status=${status}, message=${message}, isSuccess=${isSuccess}, executionStatus=${executionStatus}`);
        } else if (stage === 'error') {
          executionStatus = 'failed';
        } else if (stage === 'stopped' || status === 'stopped') {
          executionStatus = 'stopped';
        }

        await xianyuLogService.updateExecutionStatus(
          taskId,
          executionStatus,
          progress || 0,
          stage || '执行中',
          message
        );

        console.log(`闲鱼执行日志已更新: ${taskId} -> ${executionStatus} (${progress}%)`);
      } catch (logError) {
        console.error('更新闲鱼执行日志失败:', logError);
      }
    }

    // 通过WebSocket发送状态更新到前端
    io.emit('xianyu_status_update', statusData);

    console.log(`闲鱼脚本状态更新 [${deviceId}]: ${stage} - ${status} (${progress}%) - ${message}`);

    // 如果脚本执行完成，立即处理设备状态和前端通知
    if (stage === 'completed' || stage === 'error') {
      console.log(`闲鱼脚本执行${stage === 'completed' ? '完成' : '出错'}，恢复设备状态: ${deviceId}`);

      // 立即恢复设备状态为在线
      try {
        await updateDeviceStatus(deviceId, 'online');
        console.log(`设备状态已恢复为在线: ${deviceId}`);
      } catch (error) {
        console.error('恢复设备状态失败:', error);
      }

      // 通知前端脚本执行完成
      if (stage === 'completed') {
        io.emit('xianyu_task_completed', {
          deviceId: deviceId,
          taskId: taskId,
          functionType: 'keywordMessage', // 默认功能类型
          status: 'success',
          message: message,
          timestamp: new Date().toISOString()
        });
      } else {
        io.emit('xianyu_task_stopped', {
          deviceId: deviceId,
          taskId: taskId,
          functionType: 'keywordMessage', // 默认功能类型
          status: 'failed',
          message: message,
          timestamp: new Date().toISOString()
        });
      }

      // 通知前端重置脚本执行状态
      io.emit('xianyu_execution_completed', {
        deviceId: deviceId,
        taskId: taskId,
        status: stage === 'completed' ? 'success' : 'failed',
        message: message,
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      message: '闲鱼状态更新已接收'
    });

  } catch (error) {
    console.error('处理闲鱼脚本状态更新失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});

// 接收脚本调试日志API
app.post('/api/xiaohongshu/debug-log', (req, res) => {
  try {
    const { deviceId, taskId, message, level, timestamp } = req.body;

    if (!deviceId || !message) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 构造调试日志数据
    const logData = {
      deviceId: deviceId,
      taskId: taskId,
      message: message,
      level: level || 'info',
      timestamp: timestamp || new Date().toISOString()
    };

    // 通过WebSocket发送调试日志到前端
    io.emit('xiaohongshu_debug_log', logData);

    // 只在debug模式下输出调试日志到控制台
    if (process.env.DEBUG_LOGS === 'true') {
      console.log(`调试日志 [${deviceId}]: ${message}`);
    }

    res.json({
      success: true,
      message: '调试日志已接收'
    });

  } catch (error) {
    console.error('处理调试日志失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});



// UID文件上传
app.post('/api/xiaohongshu/upload-uid-file', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: '没有上传文件' });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;
    // 修复文件名编码问题
    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');
    const fileSize = req.file.size;
    const uploadedBy = req.user ? req.user.username : 'admin';

    // 检查文件格式
    const fileExt = path.extname(originalName).toLowerCase();
    if (fileExt !== '.txt' && fileExt !== '.csv') {
      fs.unlinkSync(filePath); // 删除不支持的文件
      return res.status(400).json({ success: false, message: '只支持 .txt 和 .csv 格式的文件' });
    }

    // 读取文件内容并解析UID
    let fileContent;
    try {
      fileContent = fs.readFileSync(filePath, 'utf8');
    } catch (readError) {
      // 如果UTF-8读取失败，尝试其他编码
      try {
        fileContent = fs.readFileSync(filePath, 'gbk');
      } catch (gbkError) {
        try {
          fileContent = fs.readFileSync(filePath, 'latin1');
        } catch (latinError) {
          fs.unlinkSync(filePath);
          return res.status(400).json({ success: false, message: '文件编码不支持，请使用UTF-8编码的文本文件' });
        }
      }
    }

    // 检查文件内容是否为二进制数据（Excel文件等）
    const firstBytes = fileContent.substring(0, 200);

    // 检测Excel文件的多种特征
    const isExcelFile =
      // XLSX文件特征
      (firstBytes.includes('PK') && (firstBytes.includes('docProps') || firstBytes.includes('xl/'))) ||
      // XLS文件特征
      firstBytes.includes('\xD0\xCF\x11\xE0') ||
      // 其他Office文档特征
      firstBytes.includes('Microsoft') ||
      // ZIP文件特征（XLSX本质是ZIP）
      (firstBytes.startsWith('PK\x03\x04') && firstBytes.includes('xl/')) ||
      // 检查是否包含大量非文本字符
      (fileContent.length > 0 && (fileContent.match(/[\x00-\x08\x0E-\x1F\x7F-\xFF]/g) || []).length / fileContent.length > 0.3);

    if (isExcelFile) {
      fs.unlinkSync(filePath);
      return res.status(400).json({
        success: false,
        message: '检测到Excel文件格式。请将Excel文件另存为CSV格式，或直接使用文本文件。'
      });
    }

    const lines = fileContent.split('\n').filter(line => line.trim());
    const uids = lines.map(line => line.trim()).filter(uid => uid.length > 0);

    if (uids.length === 0) {
      fs.unlinkSync(filePath); // 删除空文件
      return res.status(400).json({ success: false, message: '文件中没有有效的UID' });
    }

    // 检查UID长度
    const longUids = uids.filter(uid => uid.length > 100);
    if (longUids.length > 0) {
      fs.unlinkSync(filePath);
      return res.status(400).json({ 
        success: false, 
        message: `发现 ${longUids.length} 个超长UID（超过100字符）。请检查文件格式是否正确，或清理超长UID。` 
      });
    }

    // 如果数据库可用，保存到数据库
    if (pool) {
      try {
        // 检查是否已存在相同文件名的文件
        const [existingFiles] = await pool.execute(
          'SELECT id, original_name FROM uid_files WHERE original_name = ? AND status = "active"',
          [originalName]
        );

        if (existingFiles.length > 0) {
          fs.unlinkSync(filePath); // 删除重复文件
          return res.status(400).json({
            success: false,
            message: `文件 "${originalName}" 已存在，请勿重复上传。如需更新，请先删除原文件。`
          });
        }

        // 插入文件记录
        const [fileResult] = await pool.execute(
          'INSERT INTO uid_files (file_name, original_name, file_path, file_size, total_uid_count, uploaded_by) VALUES (?, ?, ?, ?, ?, ?)',
          [fileName, originalName, filePath, fileSize, uids.length, uploadedBy]
        );

        const fileId = fileResult.insertId;

        // 批量插入UID数据
        const uidValues = uids.map(uid => [fileId, uid]);
        await pool.query(
          'INSERT INTO uid_data (file_id, uid) VALUES ?',
          [uidValues]
        );

        res.json({
          success: true,
          message: 'UID文件上传成功',
          data: {
            id: fileId,
            original_name: originalName,
            total_uid_count: uids.length,
            upload_time: new Date().toISOString()
          }
        });
      } catch (dbError) {
        fs.unlinkSync(filePath); // 删除文件
        throw dbError;
      }
    } else {
      // 数据库不可用时，只保存文件
      res.json({
        success: true,
        message: 'UID文件上传成功（数据库不可用，仅保存文件）',
        data: {
          id: Date.now(),
          original_name: originalName,
          total_uid_count: uids.length,
          upload_time: new Date().toISOString()
        }
      });
    }

  } catch (error) {
    console.error('上传UID文件失败:', error);
    res.status(500).json({ success: false, message: '上传失败: ' + error.message });
  }
});

// 获取UID文件列表
app.get('/api/xiaohongshu/uid-files', authenticateToken, async (req, res) => {
  try {
    if (pool) {
      const [files] = await pool.execute(`
        SELECT 
          f.id,
          f.file_name,
          f.original_name,
          f.file_size,
          f.total_uid_count,
          f.uploaded_by,
          f.upload_time,
          f.status,
          COUNT(CASE WHEN d.is_used = 1 THEN 1 END) as used_count,
          COUNT(CASE WHEN d.is_used = 0 THEN 1 END) as unused_count
        FROM uid_files f
        LEFT JOIN uid_data d ON f.id = d.file_id
        WHERE f.status = 'active'
        GROUP BY f.id
        ORDER BY f.upload_time DESC
      `);

      res.json({
        success: true,
        data: files
      });
    } else {
      // 数据库不可用时返回空列表
      res.json({
        success: true,
        data: []
      });
    }
  } catch (error) {
    console.error('获取UID文件列表失败:', error);
    res.status(500).json({ success: false, message: '获取文件列表失败' });
  }
});

// 获取指定文件的UID列表
app.get('/api/xiaohongshu/uid-files/:fileId/uids', authenticateToken, async (req, res) => {
  try {
    const { fileId } = req.params;
    const { page = 1, limit = 50, status } = req.query;

    console.log(`🔍 获取UID列表请求: fileId=${fileId}, page=${page}, limit=${limit}, status=${status}`);

    if (pool) {
      let whereClause = 'WHERE file_id = ?';
      let params = [fileId];

      if (status === 'used') {
        whereClause += ' AND is_used = 1';
      } else if (status === 'unused') {
        whereClause += ' AND is_used = 0';
      }

      const offset = (page - 1) * limit;
      
      console.log(`📊 执行查询: WHERE ${whereClause}, LIMIT ${limit}, OFFSET ${offset}`);
      console.log(`📊 查询参数:`, [...params, parseInt(limit), offset]);

      const [uids] = await pool.execute(`
        SELECT
          id, uid, is_used, used_time, used_device_id, used_device_name, task_id, created_at
        FROM uid_data
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), offset]);

      console.log(`📊 查询结果: 返回 ${uids.length} 条记录`);

      // 显示前5条记录的状态
      uids.slice(0, 5).forEach((uid, index) => {
        const status = uid.is_used === 1 ? '已使用' : '未使用';
        console.log(`  ${index + 1}. ${uid.uid}: ${status}, 时间: ${uid.used_time || '无'}`);
      });

      // 获取总数
      const [countResult] = await pool.execute(`
        SELECT COUNT(*) as total
        FROM uid_data 
        ${whereClause}
      `, params);

      res.json({
        success: true,
        data: {
          uids: uids,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            totalPages: Math.ceil(countResult[0].total / limit)
          }
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          uids: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            totalPages: 0
          }
        }
      });
    }
  } catch (error) {
    console.error('获取UID列表失败:', error);
    res.status(500).json({ success: false, message: '获取UID列表失败' });
  }
});

// 删除UID文件
app.delete('/api/xiaohongshu/uid-files/:fileId', authenticateToken, async (req, res) => {
  try {
    const { fileId } = req.params;

    if (pool) {
      // 获取文件信息
      const [files] = await pool.execute(
        'SELECT file_path FROM uid_files WHERE id = ? AND status = "active"',
        [fileId]
      );

      if (files.length === 0) {
        return res.status(404).json({ success: false, message: '文件不存在' });
      }

      const filePath = files[0].file_path;

      // 软删除文件记录
      await pool.execute(
        'UPDATE uid_files SET status = "deleted" WHERE id = ?',
        [fileId]
      );

      // 删除物理文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }

    res.json({
      success: true,
      message: '文件删除成功'
    });

  } catch (error) {
    console.error('删除UID文件失败:', error);
    res.status(500).json({ success: false, message: '删除失败' });
  }
});

// 重置UID状态
app.post('/api/xiaohongshu/uid-files/:fileId/reset-status', authenticateToken, async (req, res) => {
  try {
    const { fileId } = req.params;
    const { uidIds } = req.body;

    if (pool) {
      if (uidIds && Array.isArray(uidIds) && uidIds.length > 0) {
        // 重置指定UID的状态
        await pool.execute(
          'UPDATE uid_data SET is_used = 0, used_time = NULL, used_device_id = NULL, used_device_name = NULL, task_id = NULL WHERE file_id = ? AND id IN (?)',
          [fileId, uidIds]
        );
      } else {
        // 重置文件所有UID的状态
        await pool.execute(
          'UPDATE uid_data SET is_used = 0, used_time = NULL, used_device_id = NULL, used_device_name = NULL, task_id = NULL WHERE file_id = ?',
          [fileId]
        );
      }
    }

    res.json({
      success: true,
      message: 'UID状态重置成功'
    });

  } catch (error) {
    console.error('重置UID状态失败:', error);
    res.status(500).json({ success: false, message: '重置失败' });
  }
});

// 获取小红书任务状态
app.get('/api/xiaohongshu/tasks', (req, res) => {
  try {
    const activeTasks_array = Array.from(xiaohongshuActiveTasks.values());

    res.json({
      success: true,
      data: {
        activeTasks: activeTasks_array,
        taskHistory: xiaohongshuTaskHistory.slice(0, 50) // 返回最近50个任务
      }
    });

  } catch (error) {
    console.error('获取小红书任务状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 停止小红书脚本执行
app.post('/api/xiaohongshu/stop', authenticateToken, async (req, res) => {
  try {
    const { taskId, deviceId, logId } = req.body;

    if (!taskId || !deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    console.log(`收到停止请求: taskId=${taskId}, deviceId=${deviceId}`);

    // 如果没有提供logId，使用taskId作为logId（小红书系统中logId等于taskId）
    const actualLogId = logId || taskId;
    console.log(`使用logId: ${actualLogId}`);

    // 查找设备
    let targetDevice = null;
    let targetSocket = null;

    // 查找WebSocket设备
    for (const [socketId, deviceData] of devices) {
      if (deviceData.deviceId === deviceId) {
        targetDevice = deviceData;
        targetSocket = io.sockets.sockets.get(socketId);
        break;
      }
    }

    if (targetDevice && targetSocket) {
      // WebSocket设备 - 直接发送停止命令
      console.log(`向WebSocket设备发送停止命令: ${deviceId}`);
      targetSocket.emit('stop_script', {
        taskId: taskId,
        logId: actualLogId,
        reason: '用户手动停止'
      });

      // 更新执行状态为停止中
      if (xiaohongshuLogService && actualLogId) {
        try {
          await xiaohongshuLogService.updateExecutionStatus(
            actualLogId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行'
          );

          // 延迟3秒后更新为最终停止状态
          setTimeout(async () => {
            try {
              await xiaohongshuLogService.updateExecutionStatus(
                actualLogId,
                'stopped',
                0,
                '已停止',
                '用户手动停止脚本执行'
              );
              console.log(`小红书执行日志已更新为最终停止状态: ${actualLogId}`);

              // 恢复设备状态为在线
              await updateDeviceStatus(deviceId, 'online');
              console.log(`设备状态已恢复为在线: ${deviceId}`);

              // 通知前端重置脚本执行状态
              io.emit('xiaohongshu_execution_completed', {
                deviceId: deviceId,
                taskId: taskId,
                status: 'stopped',
                message: '用户手动停止',
                timestamp: new Date().toISOString()
              });
              console.log(`已发送脚本完成通知: ${deviceId}`);

            } catch (error) {
              console.error('更新最终停止状态失败:', error);
            }
          }, 3000);

          // 立即通知手机端关闭小红书应用（手机端自己控制休眠20秒）
          console.log(`通知设备 ${deviceId} 关闭小红书应用`);
          io.to(deviceId).emit('script_command', {
            type: 'close_xiaohongshu_app',
            deviceId: deviceId,
            reason: '脚本停止',
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          console.error('更新停止状态失败:', error);
        }
      }

      console.log(`WebSocket停止命令已发送: ${deviceId}`);

      res.json({
        success: true,
        message: '停止命令已发送到WebSocket设备'
      });
    } else {
      // HTTP设备 - 添加停止命令到队列
      console.log(`向HTTP设备发送停止命令: ${deviceId}`);

      if (!pendingCommands.has(deviceId)) {
        pendingCommands.set(deviceId, []);
      }

      // 添加停止命令
      pendingCommands.get(deviceId).push({
        type: 'stop_script',
        taskId: taskId,
        logId: actualLogId,
        reason: '用户手动停止',
        timestamp: new Date()
      });

      // 更新执行状态为停止中
      if (xiaohongshuLogService && actualLogId) {
        try {
          await xiaohongshuLogService.updateExecutionStatus(
            actualLogId,
            'stopping',
            50,
            '正在停止...',
            '用户请求停止脚本执行'
          );

          // 延迟3秒后更新为最终停止状态
          setTimeout(async () => {
            try {
              await xiaohongshuLogService.updateExecutionStatus(
                actualLogId,
                'stopped',
                0,
                '已停止',
                '用户手动停止脚本执行'
              );
              console.log(`小红书执行日志已更新为最终停止状态: ${actualLogId}`);

              // 恢复设备状态为在线
              await updateDeviceStatus(deviceId, 'online');
              console.log(`设备状态已恢复为在线: ${deviceId}`);

              // 通知前端重置脚本执行状态
              // 从taskId中提取functionType
              let functionType = 'unknown';
              if (taskId && taskId.includes('_')) {
                const parts = taskId.split('_');
                if (parts.length >= 2) {
                  functionType = parts[1]; // xiaohongshu_uidMessage_xxx -> uidMessage
                }
              }

              // 发送脚本停止事件（使用连字符格式，与前端监听一致）
              io.emit('xiaohongshu-script-completed', {
                deviceId: deviceId,
                taskId: taskId,
                functionType: functionType,
                status: 'stopped',
                message: '用户手动停止',
                timestamp: new Date().toISOString()
              });
              console.log(`已发送脚本停止事件: deviceId=${deviceId}, functionType=${functionType}`);

              // 通知手机端关闭小红书应用
              console.log(`通知设备 ${deviceId} 关闭小红书应用`);
              if (pendingCommands.has(deviceId)) {
                pendingCommands.get(deviceId).push({
                  type: 'close_xiaohongshu_app',
                  deviceId: deviceId,
                  reason: '脚本被停止',
                  timestamp: new Date().toISOString()
                });
              }

            } catch (error) {
              console.error('更新最终停止状态失败:', error);
            }
          }, 3000);

        } catch (error) {
          console.error('更新停止状态失败:', error);
        }
      }

      console.log(`HTTP停止命令已添加到队列: ${deviceId}`);

      res.json({
        success: true,
        message: '停止命令已添加到HTTP设备队列'
      });
    }

    console.log(`停止命令处理完成: ${deviceId}`);
  } catch (error) {
    console.error('停止脚本执行失败:', error);
    res.status(500).json({
      success: false,
      message: '停止失败: ' + error.message
    });
  }
});

// 停止特定设备的小红书脚本执行
app.post('/api/xiaohongshu/stop-device', authenticateToken, async (req, res) => {
  try {
    const { deviceId, functionType } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID参数'
      });
    }

    console.log(`收到停止设备请求: deviceId=${deviceId}, functionType=${functionType}`);

    let stoppedCount = 0;
    let updatedCount = 0;

    // 1. 更新数据库中该设备正在执行的任务
    if (pool) {
      try {
        let updateQuery = `
          UPDATE xiaohongshu_execution_logs
          SET execution_status = 'stopped',
              progress_percentage = 0,
              error_message = '用户手动停止',
              execution_result = '已停止',
              completed_at = NOW(),
              execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
          WHERE device_id = ? AND execution_status = 'running'
        `;
        let queryParams = [deviceId];

        if (functionType) {
          updateQuery += ' AND function_type = ?';
          queryParams.push(functionType);
        }

        const [result] = await pool.execute(updateQuery, queryParams);
        updatedCount = result.affectedRows;
        console.log(`已更新设备 ${deviceId} 的 ${updatedCount} 个执行记录为停止状态`);
      } catch (dbError) {
        console.error('更新数据库记录失败:', dbError);
      }
    }

    // 2. 向设备发送停止命令
    for (const [socketId, device] of devices) {
      if (device.deviceId === deviceId) {
        const deviceSocket = io.sockets.sockets.get(socketId);
        if (deviceSocket) {
          deviceSocket.emit('script_command', {
            type: 'stop_script',
            functionType: functionType,
            reason: '用户手动停止'
          });
          stoppedCount++;
          console.log(`已向设备 ${device.deviceName} (${deviceId}) 发送停止命令`);
        }
        break;
      }
    }

    // 3. 设置停止信号
    deviceStopSignals.set(deviceId, {
      shouldStop: true,
      timestamp: new Date(),
      functionType: functionType
    });

    // 4. 通知前端任务已停止
    io.emit('xiaohongshu_task_update', {
      deviceId: deviceId,
      functionType: functionType,
      status: 'stopped',
      message: '用户手动停止',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `已停止设备 ${deviceId} 的${functionType ? functionType : '所有'}任务`,
      data: {
        stoppedDevices: stoppedCount,
        updatedTasks: updatedCount
      }
    });

  } catch (error) {
    console.error('停止设备脚本失败:', error);
    res.status(500).json({
      success: false,
      message: '停止设备脚本失败: ' + error.message
    });
  }
});

// 停止所有小红书脚本执行
app.post('/api/xiaohongshu/stop-all', authenticateToken, async (req, res) => {
  try {
    const { functionType } = req.body;
    console.log(`收到停止所有任务请求: functionType=${functionType}`);

    let stoppedCount = 0;
    let updatedCount = 0;

    // 1. 更新数据库中所有正在执行的任务
    if (pool) {
      try {
        let updateQuery = `
          UPDATE xiaohongshu_execution_logs
          SET execution_status = 'stopped',
              progress_percentage = 0,
              error_message = '用户手动停止',
              execution_result = '已停止',
              completed_at = NOW(),
              execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
          WHERE execution_status = 'running'
        `;
        let queryParams = [];

        if (functionType) {
          updateQuery += ' AND function_type = ?';
          queryParams.push(functionType);
        }

        const [result] = await pool.execute(updateQuery, queryParams);
        updatedCount = result.affectedRows;
        console.log(`已更新 ${updatedCount} 个执行记录为停止状态`);
      } catch (dbError) {
        console.error('更新数据库记录失败:', dbError);
      }
    }

    // 2. 向所有设备发送停止命令
    for (const [socketId, device] of devices) {
      const deviceSocket = io.sockets.sockets.get(socketId);
      if (deviceSocket) {
        deviceSocket.emit('script_command', {
          type: 'stop_script',
          functionType: functionType,
          reason: '用户手动停止所有任务'
        });
        stoppedCount++;
        console.log(`已向设备 ${device.deviceName} (${device.deviceId}) 发送停止命令`);
      }
    }

    // 3. 清理所有停止信号并重新设置
    deviceStopSignals.clear();
    for (const [socketId, device] of devices) {
      deviceStopSignals.set(device.deviceId, {
        shouldStop: true,
        timestamp: new Date(),
        functionType: functionType
      });
    }

    // 4. 通知前端所有任务已停止
    io.emit('xiaohongshu_all_tasks_stopped', {
      functionType: functionType,
      stoppedDevices: stoppedCount,
      updatedTasks: updatedCount,
      timestamp: new Date().toISOString()
    });

    // 5. 立即通知手机端关闭小红书应用（手机端自己控制休眠20秒）
    console.log(`立即通知所有设备关闭小红书应用 (停止所有${functionType || ''}任务)`);
    for (const [socketId, device] of devices) {
      const deviceSocket = io.sockets.sockets.get(socketId);
      if (deviceSocket) {
        // WebSocket设备
        deviceSocket.emit('script_command', {
          type: 'close_xiaohongshu_app',
          deviceId: device.deviceId,
          functionType: functionType,
          reason: `停止所有${functionType || ''}任务`,
          timestamp: new Date().toISOString()
        });
        console.log(`已通知WebSocket设备 ${device.deviceName} (${device.deviceId}) 关闭小红书应用`);
      } else if (device.socketId && device.socketId.startsWith('http_')) {
        // HTTP设备，添加到命令队列
        if (!pendingCommands.has(device.deviceId)) {
          pendingCommands.set(device.deviceId, []);
        }
        pendingCommands.get(device.deviceId).push({
          type: 'close_xiaohongshu_app',
          deviceId: device.deviceId,
          functionType: functionType,
          reason: `停止所有${functionType || ''}任务`,
          timestamp: new Date().toISOString()
        });
        console.log(`已通知HTTP设备 ${device.deviceName} (${device.deviceId}) 关闭小红书应用`);
      }
    }

    res.json({
      success: true,
      message: `已停止所有${functionType ? functionType : ''}任务，向 ${stoppedCount} 个设备发送停止命令，更新了 ${updatedCount} 个数据库记录`,
      data: {
        stoppedDevices: stoppedCount,
        updatedTasks: updatedCount
      }
    });

  } catch (error) {
    console.error('停止所有脚本失败:', error);
    res.status(500).json({
      success: false,
      message: '停止所有脚本失败: ' + error.message
    });
  }
});

// 调试API：查看当前执行状态（无需认证）
app.get('/api/xiaohongshu/debug-status', async (req, res) => {
  try {
    if (!pool) {
      return res.json({ success: false, message: '数据库未连接' });
    }

    // 查询所有正在执行的任务
    const [runningTasks] = await pool.execute(`
      SELECT device_id, device_name, function_type, execution_status, started_at, task_id
      FROM xiaohongshu_execution_logs
      WHERE execution_status = 'running'
      ORDER BY started_at DESC
    `);

    // 查询最近的任务记录
    const [recentTasks] = await pool.execute(`
      SELECT device_id, device_name, function_type, execution_status, started_at, task_id
      FROM xiaohongshu_execution_logs
      ORDER BY started_at DESC
      LIMIT 20
    `);

    res.json({
      success: true,
      data: {
        runningTasks: runningTasks,
        recentTasks: recentTasks,
        connectedDevices: Array.from(devices.values()).map(device => ({
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          status: device.status
        }))
      }
    });

  } catch (error) {
    console.error('查询调试状态失败:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// 按功能类型批量停止小红书脚本执行
app.post('/api/xiaohongshu/stop-by-function', authenticateToken, async (req, res) => {
  try {
    const { functionType } = req.body;

    if (!functionType) {
      return res.status(400).json({
        success: false,
        message: '缺少功能类型参数'
      });
    }

    console.log(`收到按功能类型批量停止请求: functionType=${functionType}`);

    let stoppedCount = 0;
    let updatedCount = 0;
    const stoppedDevices = [];

    // 1. 更新数据库中指定功能类型的正在执行任务
    if (pool) {
      try {
        const updateQuery = `
          UPDATE xiaohongshu_execution_logs
          SET execution_status = 'stopped',
              progress_percentage = 0,
              error_message = '用户批量停止',
              execution_result = '已停止',
              completed_at = NOW(),
              execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW())
          WHERE (execution_status = 'running' OR execution_status = 'pending') AND function_type = ?
        `;

        const [result] = await pool.execute(updateQuery, [functionType]);
        updatedCount = result.affectedRows;
        console.log(`已更新 ${updatedCount} 个${functionType}执行记录为停止状态`);
      } catch (dbError) {
        console.error('更新数据库记录失败:', dbError);
      }
    }

    // 2. 查找正在执行指定功能的设备并发送停止命令
    const runningDevices = new Set();

    // 从数据库查找正在执行该功能的设备
    if (pool) {
      try {
        console.log(`🔍 [批量停止] 查询正在执行${functionType}功能的设备...`);

        // 🔥 修复：查询最近5分钟内创建的记录，不管状态如何
        const [runningTasks] = await pool.execute(`
          SELECT DISTINCT device_id, device_name, execution_status, started_at, task_id
          FROM xiaohongshu_execution_logs
          WHERE function_type = ? AND started_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
          ORDER BY started_at DESC
        `, [functionType]);

        console.log(`📊 [批量停止] 数据库查询结果:`, runningTasks);

        runningTasks.forEach(task => {
          runningDevices.add(task.device_id);
          console.log(`📱 [批量停止] 找到正在执行的设备: ${task.device_name} (${task.device_id}), 状态: ${task.execution_status}, 开始时间: ${task.started_at}`);
        });

        console.log(`✅ [批量停止] 总共找到 ${runningDevices.size} 个设备正在执行${functionType}功能:`, Array.from(runningDevices));
        console.log(`🔍 [批量停止] 详细设备列表:`);
        runningDevices.forEach(deviceId => {
          console.log(`  - 设备ID: ${deviceId} (功能: ${functionType})`);
        });

        // 额外检查：查看所有相关记录（移除log_id字段）
        const [allTasks] = await pool.execute(`
          SELECT device_id, device_name, execution_status, function_type, started_at
          FROM xiaohongshu_execution_logs
          WHERE function_type = ?
          ORDER BY started_at DESC
          LIMIT 10
        `, [functionType]);

        console.log(`🔍 [批量停止] ${functionType}功能的最近10条记录:`, allTasks);

        // 🔥 新增：查看所有最近的执行记录（不限功能类型）
        const [recentAllTasks] = await pool.execute(`
          SELECT device_id, device_name, execution_status, function_type, started_at
          FROM xiaohongshu_execution_logs
          ORDER BY started_at DESC
          LIMIT 20
        `);

        console.log(`🔍 [批量停止] 所有功能的最近20条记录:`, recentAllTasks);

        // 🔥 新增：查看当前所有非完成状态的记录
        const [activeTasks] = await pool.execute(`
          SELECT device_id, device_name, execution_status, function_type, started_at
          FROM xiaohongshu_execution_logs
          WHERE execution_status NOT IN ('completed', 'failed', 'stopped')
          ORDER BY started_at DESC
        `);

        console.log(`🔍 [批量停止] 当前所有活跃记录:`, activeTasks);

      } catch (dbError) {
        console.error('❌ [批量停止] 查询正在执行的任务失败:', dbError);
      }
    }

    // 只向正在执行该功能的设备发送停止命令
    console.log(`🔍 [批量停止] 开始查找连接的设备，当前连接设备数: ${devices.size}`);
    console.log(`🔍 [批量停止] 需要停止的设备ID列表:`, Array.from(runningDevices));

    // 先列出所有连接的设备，用于调试
    const connectedDeviceIds = [];
    for (const [socketId, device] of devices) {
      connectedDeviceIds.push(device.deviceId);
      console.log(`📱 [批量停止] 连接的设备: ${device.deviceName} (${device.deviceId}), socketId: ${socketId}, status: ${device.status}`);
    }
    console.log(`📊 [批量停止] 所有连接设备ID:`, connectedDeviceIds);

    // 检查设备ID匹配情况
    const matchingDevices = [];
    const missingDevices = [];
    runningDevices.forEach(deviceId => {
      if (connectedDeviceIds.includes(deviceId)) {
        matchingDevices.push(deviceId);
      } else {
        missingDevices.push(deviceId);
      }
    });

    console.log(`✅ [批量停止] 匹配的设备ID (${matchingDevices.length}个):`, matchingDevices);
    console.log(`❌ [批量停止] 缺失的设备ID (${missingDevices.length}个):`, missingDevices);

    // 🔥 新策略：直接向所有正在执行的设备发送停止命令，不依赖连接状态
    runningDevices.forEach(deviceId => {
      console.log(`🔍 [批量停止] 处理设备: ${deviceId}`);

      // 首先尝试WebSocket连接
      let deviceFound = false;
      let deviceName = deviceId; // 默认使用deviceId作为名称

      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          deviceFound = true;
          deviceName = device.deviceName;

          const deviceSocket = io.sockets.sockets.get(socketId);
          if (deviceSocket) {
            console.log(`📡 [批量停止] WebSocket设备连接存在，发送停止命令: ${deviceId}`);
            deviceSocket.emit('script_command', {
              type: 'stop_script',
              functionType: functionType,
              reason: `用户批量停止${functionType}功能`
            });
            stoppedCount++;
            stoppedDevices.push({
              deviceId: deviceId,
              deviceName: deviceName
            });
            console.log(`✅ [批量停止] 已向WebSocket设备 ${deviceName} (${deviceId}) 发送停止命令`);
          } else {
            console.log(`⚠️ [批量停止] WebSocket连接已断开，尝试HTTP队列: ${deviceId}`);
          }
          break;
        }
      }

      // 对于所有设备（无论是否找到WebSocket连接），都使用HTTP命令队列
      // 因为HTTP设备在执行脚本时通常不保持WebSocket连接
      console.log(`📡 [批量停止] 使用HTTP命令队列发送停止命令: ${deviceId}`);

      if (!pendingCommands.has(deviceId)) {
        pendingCommands.set(deviceId, []);
      }

      pendingCommands.get(deviceId).push({
        type: 'stop_script',
        functionType: functionType,
        reason: `用户批量停止${functionType}功能`,
        timestamp: new Date()
      });

      stoppedCount++;
      stoppedDevices.push({
        deviceId: deviceId,
        deviceName: deviceName
      });

      console.log(`✅ [批量停止] 已向HTTP设备 ${deviceName} (${deviceId}) 添加停止命令到队列`);
    });

    console.log(`📊 [批量停止] 最终统计: 找到 ${runningDevices.size} 个运行设备，向 ${stoppedCount} 个设备发送停止命令`);


    // 3. 设置停止信号（只针对正在执行该功能的设备）
    runningDevices.forEach(deviceId => {
      deviceStopSignals.set(deviceId, {
        shouldStop: true,
        timestamp: new Date(),
        functionType: functionType
      });
      console.log(`已为设备 ${deviceId} 设置${functionType}功能停止信号`);
    });

    // 4. 通知前端指定功能的任务已停止
    io.emit('xiaohongshu_function_tasks_stopped', {
      functionType: functionType,
      stoppedDevices: stoppedCount,
      updatedTasks: updatedCount,
      deviceList: stoppedDevices,
      timestamp: new Date().toISOString()
    });

    // 5. 立即通知正在执行该功能的设备关闭小红书应用（手机端自己控制休眠20秒）
    console.log(`立即通知正在执行${functionType}功能的设备关闭小红书应用`);
    for (const [socketId, device] of devices) {
      if (runningDevices.has(device.deviceId)) {
        const deviceSocket = io.sockets.sockets.get(socketId);
        if (deviceSocket) {
          // WebSocket设备
          deviceSocket.emit('script_command', {
            type: 'close_xiaohongshu_app',
            deviceId: device.deviceId,
            functionType: functionType,
            reason: `${functionType}功能批量停止`,
            timestamp: new Date().toISOString()
          });
          console.log(`已通知正在执行${functionType}的WebSocket设备 ${device.deviceName} (${device.deviceId}) 关闭小红书应用`);
        } else if (device.socketId && device.socketId.startsWith('http_')) {
          // HTTP设备，添加到命令队列
          if (!pendingCommands.has(device.deviceId)) {
            pendingCommands.set(device.deviceId, []);
          }
          pendingCommands.get(device.deviceId).push({
            type: 'close_xiaohongshu_app',
            deviceId: device.deviceId,
            functionType: functionType,
            reason: `${functionType}功能批量停止`,
            timestamp: new Date().toISOString()
          });
          console.log(`已通知正在执行${functionType}的HTTP设备 ${device.deviceName} (${device.deviceId}) 关闭小红书应用`);
        }
      }
    }

    // 🔥 新增：批量停止完成后，通知前端强制刷新Vuex状态
    if (stoppedCount > 0) {
      console.log(`📡 [批量停止] 通知前端强制刷新Vuex状态`);
      io.emit('xiaohongshu_force_refresh_vuex', {
        action: 'batchStop',
        functionType: functionType,
        stoppedDevices: stoppedDevices,
        stoppedCount: stoppedCount,
        reason: '批量停止完成，强制刷新状态',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      message: `已批量停止${functionType}功能，向 ${stoppedCount} 个设备发送停止命令，更新了 ${updatedCount} 个数据库记录`,
      data: {
        functionType: functionType,
        stoppedDevices: stoppedCount,
        updatedTasks: updatedCount,
        deviceList: stoppedDevices
      }
    });

  } catch (error) {
    console.error('按功能类型批量停止脚本失败:', error);
    res.status(500).json({
      success: false,
      message: '按功能类型批量停止脚本失败: ' + error.message
    });
  }
});

// 获取小红书执行日志列表
app.get('/api/xiaohongshu/logs', authenticateToken, async (req, res) => {
  try {
    if (!xiaohongshuLogService) {
      return res.status(503).json({
        success: false,
        message: '日志服务不可用（数据库未配置）'
      });
    }

    const { page = 1, limit = 20, functionType, deviceId, executionStatus, startDate, endDate } = req.query;

    const filters = {};
    if (functionType) filters.functionType = functionType;
    if (deviceId) filters.deviceId = deviceId;
    if (executionStatus) filters.executionStatus = executionStatus;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const result = await xiaohongshuLogService.getExecutionLogs(
      parseInt(page),
      parseInt(limit),
      filters
    );

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('获取小红书执行日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取执行日志失败: ' + error.message
    });
  }
});

// 获取小红书执行日志详情
app.get('/api/xiaohongshu/logs/:taskId', authenticateToken, async (req, res) => {
  try {
    if (!xiaohongshuLogService) {
      return res.status(503).json({
        success: false,
        message: '日志服务不可用（数据库未配置）'
      });
    }

    const { taskId } = req.params;
    const logDetail = await xiaohongshuLogService.getExecutionLogDetail(taskId);

    if (!logDetail) {
      return res.status(404).json({
        success: false,
        message: '执行日志不存在'
      });
    }

    res.json({
      success: true,
      data: logDetail
    });
  } catch (error) {
    console.error('获取小红书执行日志详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取执行日志详情失败: ' + error.message
    });
  }
});

// 清空小红书执行日志
app.delete('/api/xiaohongshu/logs', authenticateToken, async (req, res) => {
  try {
    if (!xiaohongshuLogService) {
      return res.status(503).json({
        success: false,
        message: '日志服务不可用（数据库未配置）'
      });
    }

    const deletedCount = await xiaohongshuLogService.clearAllExecutionLogs();

    res.json({
      success: true,
      message: `已清空所有小红书执行日志，删除了 ${deletedCount} 条记录`,
      data: { deletedCount }
    });
  } catch (error) {
    console.error('清空小红书执行日志失败:', error);
    res.status(500).json({
      success: false,
      message: '清空执行日志失败: ' + error.message
    });
  }
});

// UID管理相关API接口
// 内存存储UID数据（测试用）
const uidStorage = new Map(); // 存储UID文件数据
const uidMessageStorage = new Map(); // 存储UID私信记录

// 上传UID文件
app.post('/api/xiaohongshu/upload-uids', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;
    const originalName = Buffer.from(req.file.originalname, 'latin1').toString('utf8');

    // 读取文件内容
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.split('\n').map(line => line.trim()).filter(line => line);

    if (lines.length === 0) {
      fs.unlinkSync(filePath); // 删除空文件
      return res.status(400).json({
        success: false,
        message: '文件内容为空或格式不正确'
      });
    }

    // 存储到内存中
    const fileData = {
      fileName: originalName,
      uploadTime: new Date().toISOString(),
      uids: lines.map((uid, index) => ({
        id: `${originalName}_${index}`,
        uid: uid,
        fileName: originalName,
        isUsed: false,
        usedTime: null,
        usedDeviceId: null
      }))
    };

    uidStorage.set(originalName, fileData);

    res.json({
      success: true,
      message: `成功上传 ${lines.length} 个UID`,
      data: {
        fileName: originalName,
        uidCount: lines.length
      }
    });

  } catch (error) {
    console.error('上传UID文件失败:', error);
    res.status(500).json({
      success: false,
      message: '上传失败: ' + error.message
    });
  }
});

// 记录UID私信结果
app.post('/api/xiaohongshu/record-uid-message', async (req, res) => {
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null);
  console.log('📥 收到UID私信结果上报请求');
  console.log('请求来源IP:', clientIP);
  console.log('请求体:', JSON.stringify(req.body, null, 2));

  try {
    const { taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId } = req.body;

    // 内存存储记录（保持原有逻辑）
    const messageRecord = {
      id: Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      taskId,
      deviceId,
      deviceName,
      uid,
      messageContent,
      sendStatus,
      errorMessage,
      sendTime: new Date().toISOString()
    };

    uidMessageStorage.set(messageRecord.id, messageRecord);

    // 数据库存储记录（新增逻辑）
    if (pool) {
      try {
        const connection = await pool.getConnection();

        try {
          // 根据taskId判断功能类型
          let tableName;
          let insertQuery;
          let insertParams;

          // 检查taskId中是否包含uidFileMessage，如果包含则写入文件上传表，否则写入手动输入表
          if (taskId && taskId.includes('uidFileMessage')) {
            tableName = 'xiaohongshu_file_uid_messages';
            insertQuery = `
              INSERT INTO ${tableName}
              (task_id, device_id, device_name, uid, message_content, send_status, error_message, file_id, send_time)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `;
            insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage, fileId || null];
          } else {
            tableName = 'xiaohongshu_manual_uid_messages';
            insertQuery = `
              INSERT INTO ${tableName}
              (task_id, device_id, device_name, uid, message_content, send_status, error_message, send_time)
              VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            `;
            insertParams = [taskId, deviceId, deviceName, uid, messageContent, sendStatus, errorMessage];
          }

          await connection.execute(insertQuery, insertParams);
          console.log(`UID私信记录已保存到数据库 ${tableName}: 设备${deviceId}, UID${uid}, 状态${sendStatus}`);

          // 只有在私信成功时才标记UID为已使用并记录设备信息
          if (uid && taskId && sendStatus === 'success') {
            try {
              // 首先尝试使用任务ID匹配更新
              let [uidUpdateResult] = await connection.execute(
                'UPDATE uid_data SET is_used = 1, used_time = NOW(), used_device_id = ?, used_device_name = ?, task_id = ? WHERE uid = ? AND (task_id = ? OR task_id IS NULL)',
                [deviceId, deviceName, taskId, uid, taskId]
              );

              // 如果没有更新任何记录，尝试不匹配任务ID的更新（兼容测试和特殊情况）
              if (uidUpdateResult.affectedRows === 0) {
                console.log(`⚠️  UID ${uid} 使用任务ID匹配更新失败，尝试直接更新...`);
                [uidUpdateResult] = await connection.execute(
                  'UPDATE uid_data SET is_used = 1, used_time = NOW(), used_device_id = ?, used_device_name = ?, task_id = ? WHERE uid = ?',
                  [deviceId, deviceName, taskId, uid]
                );
              }

              console.log(`✅ UID ${uid} 私信成功，已标记为已使用并记录设备信息, 影响行数: ${uidUpdateResult.affectedRows}`);

              if (uidUpdateResult.affectedRows === 0) {
                console.log(`⚠️  警告: UID ${uid} 状态更新失败，可能该UID不存在于数据库中`);
              }
            } catch (uidUpdateError) {
              console.error(`更新UID ${uid} 状态失败:`, uidUpdateError);
            }
          } else if (uid && taskId && sendStatus === 'failed') {
            console.log(`❌ UID ${uid} 私信失败，保持未使用状态，不记录设备信息`);
          }

        } finally {
          connection.release();
        }
      } catch (dbError) {
        console.error('保存UID私信记录到数据库失败:', dbError);
        // 数据库错误不影响响应，继续返回成功
      }
    }

    res.json({
      success: true,
      message: '私信记录保存成功'
    });

  } catch (error) {
    console.error('记录UID私信结果失败:', error);
    res.status(500).json({
      success: false,
      message: '记录失败: ' + error.message
    });
  }
});

// 查询UID状态API
app.get('/api/xiaohongshu/uid-status/:uid', async (req, res) => {
  try {
    const { uid } = req.params;

    if (!uid) {
      return res.status(400).json({
        success: false,
        message: '缺少UID参数'
      });
    }

    if (pool) {
      try {
        const [uidData] = await pool.execute(
          'SELECT uid, is_used, used_time, used_device_id, used_device_name, task_id, file_id FROM uid_data WHERE uid = ?',
          [uid]
        );

        if (uidData.length > 0) {
          const uidInfo = uidData[0];

          // 查询私信记录
          const [messageRecords] = await pool.execute(
            'SELECT send_status, send_time, error_message FROM xiaohongshu_file_uid_messages WHERE uid = ? ORDER BY send_time DESC LIMIT 5',
            [uid]
          );

          res.json({
            success: true,
            data: {
              uid: uidInfo.uid,
              isUsed: uidInfo.is_used === 1,
              usedTime: uidInfo.used_time,
              usedDeviceId: uidInfo.used_device_id,
              usedDeviceName: uidInfo.used_device_name,
              taskId: uidInfo.task_id,
              fileId: uidInfo.file_id,
              messageRecords: messageRecords
            }
          });
        } else {
          res.json({
            success: false,
            message: 'UID不存在'
          });
        }
      } catch (dbError) {
        console.error('查询UID状态失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库查询失败: ' + dbError.message
        });
      }
    } else {
      res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

  } catch (error) {
    console.error('查询UID状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询失败: ' + error.message
    });
  }
});

// 接收脚本执行完成通知
app.post('/api/xiaohongshu/execution-complete', async (req, res) => {
  try {
    const { taskId, deviceId, deviceName, totalCount, successCount, failedCount, executionLogs, completedAt } = req.body;

    console.log('收到UID私信执行完成通知:', {
      taskId,
      deviceId,
      successCount,
      failedCount
    });

    // 更新数据库中的UID使用状态
    if (pool && taskId) {
      try {
        console.log(`开始更新任务 ${taskId} 的UID使用状态...`);

        // 根据执行结果更新UID状态
        if (successCount > 0) {
          // 如果有成功的私信，保持UID为已使用状态
          const [updateResult] = await pool.execute(
            'UPDATE uid_data SET is_used = 1, used_time = NOW() WHERE task_id = ? AND is_used = 1',
            [taskId]
          );
          console.log(`确认 ${updateResult.affectedRows} 个成功UID的使用状态`);
        }

        if (failedCount > 0) {
          // 如果有失败的私信，可以选择重置UID状态供下次使用
          // 这里我们保持已使用状态，但记录失败信息
          console.log(`${failedCount} 个UID私信失败，保持已使用状态`);
        }

        // 更新执行日志表中的状态
        const [logUpdateResult] = await pool.execute(
          'UPDATE xiaohongshu_execution_logs SET execution_status = ?, progress = ?, updated_at = NOW() WHERE task_id = ? AND device_id = ?',
          [successCount > 0 ? 'completed' : 'failed', 100, taskId, deviceId]
        );
        console.log(`更新执行日志状态: 影响 ${logUpdateResult.affectedRows} 条记录`);

      } catch (dbError) {
        console.error('更新数据库状态失败:', dbError);
      }
    }

    // 通知前端页面执行完成
    io.emit('xiaohongshu-execution-complete', {
      taskId,
      deviceId,
      deviceName,
      totalCount,
      successCount,
      failedCount,
      executionLogs,
      completedAt
    });

    res.json({
      success: true,
      message: '执行结果接收成功'
    });

  } catch (error) {
    console.error('处理执行完成通知失败:', error);
    res.status(500).json({
      success: false,
      message: '处理失败: ' + error.message
    });
  }
});

// 小红书视频发布功能API

// 提供静态文件访问（缩略图等）
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 配置视频文件上传（独立配置，避免与其他multer实例冲突）
const videoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, 'uploads/videos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'video-' + uniqueSuffix + ext);
  }
});

// 创建专门用于视频上传的multer实例
const videoUpload = multer({
  storage: videoStorage,
  fileFilter: function (req, file, cb) {
    console.log('视频文件过滤检查:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // 只接受videos字段
    if (file.fieldname !== 'videos') {
      console.error('错误的字段名:', file.fieldname, '期望: videos');
      return cb(new Error(`错误的字段名: ${file.fieldname}，期望: videos`));
    }

    // 支持常见的视频格式
    const allowedTypes = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      console.log('视频文件验证通过:', file.originalname);
      cb(null, true);
    } else {
      console.error('不支持的视频格式:', ext);
      cb(new Error('只支持视频格式文件: ' + allowedTypes.join(', ')));
    }
  },
  limits: {
    fileSize: 2 * 1024 * 1024 * 1024, // 2GB限制
    files: 1000 // 最多1000个文件
  }
});

// 计算文件MD5哈希值
const crypto = require('crypto');
const { spawn } = require('child_process');

function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);

    stream.on('data', data => hash.update(data));
    stream.on('end', () => resolve(hash.digest('hex')));
    stream.on('error', reject);
  });
}

// 使用ffmpeg获取视频信息（如果可用）
function getVideoInfo(filePath) {
  return new Promise((resolve, reject) => {
    // 模拟视频信息（用于演示）
    const fileStats = fs.statSync(filePath);
    const fileExt = path.extname(filePath).toLowerCase();

    // 根据文件扩展名和大小模拟视频信息
    let mockDuration = Math.floor(Math.random() * 300) + 30; // 30-330秒
    let mockWidth = 1920;
    let mockHeight = 1080;

    // 根据文件大小调整模拟参数
    if (fileStats.size < 10 * 1024 * 1024) { // 小于10MB
      mockWidth = 1280;
      mockHeight = 720;
      mockDuration = Math.floor(Math.random() * 120) + 15;
    } else if (fileStats.size > 100 * 1024 * 1024) { // 大于100MB
      mockWidth = 3840;
      mockHeight = 2160;
      mockDuration = Math.floor(Math.random() * 600) + 120;
    }

    console.log(`模拟视频信息 - 文件: ${path.basename(filePath)}, 大小: ${fileStats.size} bytes`);

    // 返回模拟的视频信息
    resolve({
      duration: mockDuration,
      width: mockWidth,
      height: mockHeight,
      resolution: `${mockWidth}x${mockHeight}`,
      bitrate: Math.floor(fileStats.size * 8 / mockDuration), // 估算比特率
      fps: 30
    });

  });
}

// 生成视频缩略图（模拟实现）
function generateThumbnail(videoPath, outputPath, timeOffset = '00:00:01') {
  return new Promise((resolve, reject) => {
    console.log(`生成缩略图 - 输入: ${videoPath}, 输出: ${outputPath}`);

    // 创建一个简单的SVG缩略图作为占位符
    const svgContent = `
      <svg width="320" height="240" xmlns="http://www.w3.org/2000/svg">
        <rect width="320" height="240" fill="#f0f0f0"/>
        <rect x="10" y="10" width="300" height="220" fill="#e0e0e0" stroke="#ccc" stroke-width="2"/>
        <circle cx="160" cy="120" r="30" fill="#409eff"/>
        <polygon points="150,105 150,135 175,120" fill="white"/>
        <text x="160" y="180" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
          ${path.basename(videoPath)}
        </text>
        <text x="160" y="200" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">
          视频预览
        </text>
      </svg>
    `;

    try {
      // 将SVG保存为文件（作为缩略图占位符）
      fs.writeFileSync(outputPath.replace('.jpg', '.svg'), svgContent);

      // 如果有真实的ffmpeg，可以在这里调用
      // 现在返回SVG文件路径
      const svgPath = outputPath.replace('.jpg', '.svg');
      console.log(`缩略图已生成: ${svgPath}`);
      resolve(svgPath);

    } catch (error) {
      console.error('生成缩略图失败:', error);
      resolve(''); // 返回空字符串表示生成失败
    }

  });
}

// 生成默认缩略图
function generateDefaultThumbnail(outputPath, fileName) {
  return new Promise((resolve, reject) => {
    console.log(`生成默认缩略图 - 输出: ${outputPath}`);

    const svgContent = `
      <svg width="320" height="240" xmlns="http://www.w3.org/2000/svg">
        <rect width="320" height="240" fill="#f0f0f0"/>
        <rect x="10" y="10" width="300" height="220" fill="#e0e0e0" stroke="#ccc" stroke-width="2"/>
        <circle cx="160" cy="120" r="30" fill="#409eff"/>
        <polygon points="150,105 150,135 175,120" fill="white"/>
        <text x="160" y="180" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">
          ${fileName}
        </text>
        <text x="160" y="200" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">
          视频预览
        </text>
      </svg>
    `;

    try {
      fs.writeFileSync(outputPath, svgContent);
      console.log(`默认缩略图已生成: ${outputPath}`);
      resolve(outputPath);
    } catch (error) {
      console.error('生成默认缩略图失败:', error);
      reject(error);
    }
  });
}

// 上传视频文件（支持批量上传，带重复检测）
app.post('/api/xiaohongshu/upload-video-files', (req, res, next) => {
  console.log('开始处理视频文件上传请求');
  console.log('请求头:', req.headers['content-type']);
  console.log('请求体大小:', req.headers['content-length']);

  // 使用videoUpload处理文件上传
  videoUpload.array('videos', 1000)(req, res, (err) => {
    if (err) {
      console.error('视频上传multer错误:', err);

      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            success: false,
            message: '文件大小超过限制（最大2GB）'
          });
        } else if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(400).json({
            success: false,
            message: '文件数量超过限制（最大1000个文件）'
          });
        } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return res.status(400).json({
            success: false,
            message: `不支持的文件字段名: ${err.field}，应该使用 'videos'`
          });
        }
      }

      return res.status(400).json({
        success: false,
        message: '文件上传错误: ' + err.message
      });
    }

    console.log('Multer处理完成，文件数量:', req.files ? req.files.length : 0);
    next();
  });
}, async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ success: false, message: '没有上传视频文件' });
    }

    const uploadedBy = req.user ? req.user.username : 'admin';
    const uploadResults = [];
    const duplicateFiles = [];
    const connection = await pool.getConnection();

    try {
      for (const file of req.files) {
        const filePath = file.path;
        const fileName = file.filename;
        const originalName = file.originalname;
        const fileSize = file.size;
        const videoFormat = path.extname(originalName).toLowerCase().substring(1);

        // 计算文件哈希值
        const fileHash = await calculateFileHash(filePath);

        // 检查是否已存在相同的文件（基于文件哈希值和大小）
        const [existingFiles] = await connection.execute(
          `SELECT id, original_name, upload_time FROM xiaohongshu_video_files
           WHERE file_size = ? AND status = 'active'`,
          [fileSize]
        );

        let isDuplicate = false;
        let duplicateInfo = null;

        // 对于相同大小的文件，进一步检查哈希值
        for (const existingFile of existingFiles) {
          const [fileDetails] = await connection.execute(
            'SELECT file_path FROM xiaohongshu_video_files WHERE id = ?',
            [existingFile.id]
          );

          if (fileDetails.length > 0 && fs.existsSync(fileDetails[0].file_path)) {
            const existingHash = await calculateFileHash(fileDetails[0].file_path);
            if (existingHash === fileHash) {
              isDuplicate = true;
              duplicateInfo = {
                originalName: originalName,
                existingName: existingFile.original_name,
                existingId: existingFile.id,
                uploadTime: existingFile.upload_time
              };
              break;
            }
          }
        }

        if (isDuplicate) {
          // 删除重复的上传文件
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
          duplicateFiles.push(duplicateInfo);
        } else {
          // 获取视频信息
          let videoInfo = {
            duration: 0,
            resolution: '',
            width: 0,
            height: 0
          };

          let thumbnailPath = '';

          try {
            // 尝试获取视频信息
            videoInfo = await getVideoInfo(filePath);

            // 生成缩略图
            const thumbnailDir = path.join(__dirname, 'uploads/thumbnails');
            if (!fs.existsSync(thumbnailDir)) {
              fs.mkdirSync(thumbnailDir, { recursive: true });
            }

            const thumbnailFileName = `thumb-${fileName.replace(/\.[^/.]+$/, '')}.svg`;
            const thumbnailFullPath = path.join(thumbnailDir, thumbnailFileName);

            try {
              const generatedPath = await generateThumbnail(filePath, thumbnailFullPath);
              if (generatedPath) {
                thumbnailPath = `/uploads/thumbnails/${thumbnailFileName}`;
                console.log('缩略图生成成功:', thumbnailPath);
              } else {
                console.warn('缩略图生成失败，使用默认缩略图');
                // 如果生成失败，创建一个默认的缩略图
                await generateDefaultThumbnail(thumbnailFullPath, fileName);
                thumbnailPath = `/uploads/thumbnails/${thumbnailFileName}`;
              }
            } catch (thumbError) {
              console.error('缩略图生成异常:', thumbError);
              // 创建默认缩略图
              await generateDefaultThumbnail(thumbnailFullPath, fileName);
              thumbnailPath = `/uploads/thumbnails/${thumbnailFileName}`;
            }

          } catch (videoError) {
            console.warn(`处理视频 ${originalName} 时出错:`, videoError.message);
            // 继续处理，但不包含视频信息和缩略图
          }

          // 插入视频文件记录，包含视频信息
          try {
            // 先尝试包含file_hash字段的插入
            const [fileResult] = await connection.execute(
              `INSERT INTO xiaohongshu_video_files
               (file_name, original_name, file_path, file_size, video_format, uploaded_by, description, tags, file_hash, video_duration, video_resolution, thumbnail_path)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [fileName, originalName, filePath, fileSize, videoFormat, uploadedBy, req.body.description || '', req.body.tags || '', fileHash, Math.round(videoInfo.duration), videoInfo.resolution, thumbnailPath]
            );

            uploadResults.push({
              videoId: fileResult.insertId,
              fileName: originalName,
              fileSize: fileSize,
              videoFormat: videoFormat,
              videoDuration: Math.round(videoInfo.duration),
              videoResolution: videoInfo.resolution,
              thumbnailPath: thumbnailPath,
              uploadTime: new Date().toISOString()
            });

          } catch (insertError) {
            console.error('插入视频记录失败:', insertError.message);

            // 如果是字段不存在的错误，尝试不包含file_hash的插入
            if (insertError.message.includes('file_hash') || insertError.code === 'ER_BAD_FIELD_ERROR') {
              console.log('尝试不包含file_hash字段的插入...');
              const [fileResult] = await connection.execute(
                `INSERT INTO xiaohongshu_video_files
                 (file_name, original_name, file_path, file_size, video_format, uploaded_by, description, tags, video_duration, video_resolution, thumbnail_path)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [fileName, originalName, filePath, fileSize, videoFormat, uploadedBy, req.body.description || '', req.body.tags || '', Math.round(videoInfo.duration), videoInfo.resolution, thumbnailPath]
              );

              uploadResults.push({
                videoId: fileResult.insertId,
                fileName: originalName,
                fileSize: fileSize,
                videoFormat: videoFormat,
                videoDuration: Math.round(videoInfo.duration),
                videoResolution: videoInfo.resolution,
                thumbnailPath: thumbnailPath,
                uploadTime: new Date().toISOString()
              });
            } else {
              throw insertError;
            }
          }
        }
      }

      connection.release();

      // 构建响应消息
      let message = '';
      if (uploadResults.length > 0 && duplicateFiles.length > 0) {
        message = `成功上传 ${uploadResults.length} 个视频文件，跳过 ${duplicateFiles.length} 个重复文件`;
      } else if (uploadResults.length > 0) {
        message = `成功上传 ${uploadResults.length} 个视频文件`;
      } else {
        message = `所有 ${duplicateFiles.length} 个文件都是重复文件，已跳过上传`;
      }

      res.json({
        success: true,
        message: message,
        data: {
          uploadCount: uploadResults.length,
          duplicateCount: duplicateFiles.length,
          videos: uploadResults,
          duplicates: duplicateFiles
        }
      });

    } catch (dbError) {
      connection.release();
      // 删除已上传的文件
      req.files.forEach(file => {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      });
      throw dbError;
    }

  } catch (error) {
    console.error('上传视频文件失败:', error);
    console.error('错误堆栈:', error.stack);
    console.error('请求文件信息:', req.files ? req.files.map(f => ({ name: f.originalname, size: f.size })) : '无文件');
    res.status(500).json({
      success: false,
      message: '上传视频文件失败: ' + error.message,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// 删除单个视频
app.delete('/api/xiaohongshu/videos/:id', authenticateToken, async (req, res) => {
  try {
    const videoId = req.params.id;
    console.log('删除视频请求:', videoId);

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    // 先查询视频信息
    const [videos] = await pool.execute(
      'SELECT * FROM xiaohongshu_video_files WHERE id = ?',
      [videoId]
    );

    if (videos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '视频不存在'
      });
    }

    const video = videos[0];

    // 删除数据库记录
    await pool.execute('DELETE FROM xiaohongshu_video_files WHERE id = ?', [videoId]);

    // 删除视频文件
    if (video.file_path && fs.existsSync(video.file_path)) {
      try {
        fs.unlinkSync(video.file_path);
        console.log('视频文件已删除:', video.file_path);
      } catch (fileError) {
        console.warn('删除视频文件失败:', fileError.message);
      }
    }

    // 删除缩略图文件
    if (video.thumbnail_path) {
      const thumbnailFullPath = video.thumbnail_path.replace('/uploads/', './uploads/');
      if (fs.existsSync(thumbnailFullPath)) {
        try {
          fs.unlinkSync(thumbnailFullPath);
          console.log('缩略图文件已删除:', thumbnailFullPath);
        } catch (thumbError) {
          console.warn('删除缩略图文件失败:', thumbError.message);
        }
      }
    }

    res.json({
      success: true,
      message: '视频删除成功'
    });

  } catch (error) {
    console.error('删除视频失败:', error);
    res.status(500).json({
      success: false,
      message: '删除视频失败: ' + error.message
    });
  }
});

// 下载视频文件API - 支持分块传输和断点续传
app.get('/api/xiaohongshu/download-video/:id', authenticateToken, async (req, res) => {
  try {
    const videoId = req.params.id;
    const range = req.headers.range;
    console.log('视频下载请求:', videoId, '范围:', range);

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    // 查询视频信息
    const [videos] = await pool.execute(
      'SELECT * FROM xiaohongshu_video_files WHERE id = ?',
      [videoId]
    );

    if (videos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '视频文件不存在'
      });
    }

    const video = videos[0];
    const filePath = video.file_path;

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '视频文件在服务器上不存在'
      });
    }

    const fileSize = video.file_size;

    // 支持范围请求（断点续传）
    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      console.log(`📊 分块传输: ${start}-${end}/${fileSize} (${chunksize} bytes)`);

      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Length', chunksize);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${video.original_name}"`);

      const fileStream = fs.createReadStream(filePath, { start, end });
      fileStream.pipe(res);
    } else {
      // 完整文件传输
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${video.original_name}"`);
      res.setHeader('Content-Length', fileSize);
      res.setHeader('Accept-Ranges', 'bytes');

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    }

    console.log(`视频文件下载开始: ${video.original_name}`);

  } catch (error) {
    console.error('视频下载失败:', error);
    res.status(500).json({
      success: false,
      message: '视频下载失败: ' + error.message
    });
  }
});

// 传输视频到手机设备（独立功能）
app.post('/api/xiaohongshu/transfer-videos', authenticateToken, async (req, res) => {
  console.log('🚀🚀🚀 [视频传输API] ===== 传输视频API被调用 ===== 🚀🚀🚀');
  console.log('📱 [视频传输API] 请求时间:', new Date().toLocaleString());
  console.log('📱 [视频传输API] 请求IP:', req.ip);
  console.log('📱 [视频传输API] 请求体:', JSON.stringify(req.body, null, 2));

  try {
    const { deviceIds, selectedVideoIds, selectedVideos } = req.body;

    console.log('📱 [视频传输API] 视频传输请求:');
    console.log('📱 [视频传输API] - 设备ID:', deviceIds);
    console.log('📱 [视频传输API] - 视频ID:', selectedVideoIds);
    console.log('📱 [视频传输API] - 视频数量:', selectedVideos ? selectedVideos.length : 0);
    console.log('📹 [视频传输API] - 选中的视频:', selectedVideos);

    if (!deviceIds || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要传输的设备'
      });
    }

    if (!selectedVideoIds || selectedVideoIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要传输的视频'
      });
    }

    // 验证视频文件是否存在
    const videoInfos = [];
    for (const videoId of selectedVideoIds) {
      const [videos] = await pool.execute(
        'SELECT * FROM xiaohongshu_video_files WHERE id = ?',
        [videoId]
      );

      if (videos.length > 0) {
        const video = videos[0];
        if (fs.existsSync(video.file_path)) {
          videoInfos.push({
            id: video.id,
            original_name: video.original_name,
            file_path: video.file_path,
            file_size: video.file_size
          });
        } else {
          console.warn(`视频文件不存在: ${video.file_path}`);
        }
      }
    }

    if (videoInfos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '没有找到可传输的视频文件'
      });
    }

    // 创建传输任务
    const transferTaskId = `video_transfer_${Date.now()}`;
    const transferCommand = {
      type: 'video_transfer',
      taskId: transferTaskId,
      videos: videoInfos,
      serverHost: '************:3002',
      authToken: 'test-token',
      timestamp: Date.now(),
      // 添加进度监控配置
      progressConfig: {
        reportInterval: 2000, // 每2秒报告一次进度
        enableProgress: true,
        enableDetailedLog: true, // 启用详细日志
        timeoutSeconds: 300 // 5分钟超时
      }
    };

    // 获取设备信息
    const deviceInfos = {};
    for (const deviceId of deviceIds) {
      const [devices] = await pool.execute(
        'SELECT device_name FROM devices WHERE device_id = ?',
        [deviceId]
      );
      if (devices.length > 0) {
        deviceInfos[deviceId] = devices[0].device_name;
      } else {
        deviceInfos[deviceId] = deviceId; // 使用设备ID作为默认名称
      }
    }

    // 发送传输命令到指定设备并记录传输记录
    let successCount = 0;
    const results = [];

    console.log('🔄 [视频传输API] 开始处理设备列表:', deviceIds);
    console.log('📹 [视频传输API] 视频信息列表:', videoInfos);

    for (const deviceId of deviceIds) {
      try {
        console.log(`📱 [视频传输API] 处理设备: ${deviceId}`);
        console.log(`📱 [视频传输API] 设备名称: ${deviceInfos[deviceId]}`);

        // 为每个视频创建传输记录
        console.log(`📝 [传输记录] 开始为设备 ${deviceId} 创建传输记录`);
        for (const videoInfo of videoInfos) {
          console.log(`📝 [传输记录] 插入记录: 视频${videoInfo.id} -> 设备${deviceId}`);

          await pool.execute(`
            INSERT INTO xiaohongshu_video_transfers
            (video_id, device_id, device_name, transfer_type, task_id, status, file_size, video_filename, transfer_time)
            VALUES (?, ?, ?, 'manual', ?, 'pending', ?, ?, NOW())
          `, [videoInfo.id, deviceId, deviceInfos[deviceId], transferTaskId, videoInfo.file_size, videoInfo.original_name || '']);

          console.log(`✅ [传输记录] 已记录视频 ${videoInfo.id} (${videoInfo.original_name}) 传输到设备 ${deviceId}`);
        }

        // 将传输命令添加到设备命令队列
        if (!deviceCommands[deviceId]) {
          deviceCommands[deviceId] = [];
        }

        deviceCommands[deviceId].push(transferCommand);

        console.log(`📤 传输命令已发送到设备: ${deviceId}`);
        successCount++;

        results.push({
          deviceId: deviceId,
          status: 'success',
          message: '传输命令已发送'
        });

      } catch (error) {
        console.error(`发送传输命令到设备 ${deviceId} 失败:`, error);
        results.push({
          deviceId: deviceId,
          status: 'error',
          message: error.message
        });
      }
    }

    res.json({
      success: true,
      message: `视频传输命令已发送到 ${successCount} 个设备`,
      data: {
        transferTaskId: transferTaskId,
        videoCount: videoInfos.length,
        deviceCount: successCount,
        videos: videoInfos.map(v => ({
          id: v.id,
          name: v.original_name,
          size: v.file_size
        })),
        results: results
      }
    });

  } catch (error) {
    console.error('视频传输失败:', error);
    res.status(500).json({
      success: false,
      message: '视频传输失败: ' + error.message
    });
  }
});

// 上报视频传输进度（实时进度）
app.post('/api/xiaohongshu/report-transfer-progress', async (req, res) => {
  try {
    const { taskId, deviceId, videoId, videoName, progress, transferredBytes, totalBytes, transferSpeed, status } = req.body;

    console.log('📊 [传输进度] 收到进度上报:', { taskId, deviceId, videoId, videoName, progress, status });

    if (!taskId || !deviceId || !videoId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 更新数据库中的传输记录
    try {
      console.log('📝 [传输进度] 开始更新数据库传输记录');
      console.log('📝 [传输进度] 查询条件 - videoId:', videoId, 'deviceId:', deviceId, 'taskId:', taskId);

      // 首先检查是否存在对应的传输记录
      const [existingRecords] = await pool.execute(`
        SELECT id, status, transfer_progress FROM xiaohongshu_video_transfers
        WHERE video_id = ? AND device_id = ? AND (task_id = ? OR task_id LIKE ?)
        ORDER BY transfer_time DESC LIMIT 1
      `, [videoId, deviceId, taskId, `%${taskId.split('_').pop()}%`]);

      if (existingRecords.length > 0) {
        const recordId = existingRecords[0].id;
        console.log('📝 [传输进度] 找到现有记录，ID:', recordId, '当前状态:', existingRecords[0].status);

        // 更新现有记录的进度和状态
        const dbStatus = (status === 'downloading' || status === 'starting') ? 'transferring' : (status || 'transferring');
        await pool.execute(`
          UPDATE xiaohongshu_video_transfers
          SET transfer_progress = ?, status = ?, file_size = ?, transfer_speed = ?,
              completed_time = CASE WHEN ? = 'completed' THEN NOW() ELSE completed_time END
          WHERE id = ?
        `, [Math.floor(progress), dbStatus, totalBytes || 0, transferSpeed || 0, dbStatus, recordId]);

        console.log('✅ [传输进度] 已更新现有传输记录，进度:', progress + '%', '状态:', status);
      } else {
        console.log('⚠️ [传输进度] 未找到现有传输记录，创建新记录');

        // 获取设备名称
        let deviceName = deviceId;
        try {
          const [devices] = await pool.execute('SELECT device_name FROM devices WHERE device_id = ?', [deviceId]);
          if (devices.length > 0) {
            deviceName = devices[0].device_name;
          }
        } catch (error) {
          console.log('⚠️ [传输进度] 获取设备名称失败，使用设备ID:', error.message);
        }

        // 创建新的传输记录
        const dbStatus = (status === 'downloading' || status === 'starting') ? 'transferring' : (status || 'transferring');
        await pool.execute(`
          INSERT INTO xiaohongshu_video_transfers
          (video_id, device_id, device_name, transfer_type, task_id, status, transfer_progress,
           file_size, transfer_speed, transfer_time)
          VALUES (?, ?, ?, 'script_execution', ?, ?, ?, ?, ?, NOW())
        `, [videoId, deviceId, deviceName, taskId, dbStatus, Math.floor(progress),
            totalBytes || 0, transferSpeed || 0]);

        console.log('✅ [传输进度] 已创建新传输记录，进度:', progress + '%', '状态:', status);
      }
    } catch (dbError) {
      console.error('❌ [传输进度] 更新数据库失败:', dbError);
      // 数据库更新失败不影响进度广播
    }

    // 广播进度到前端
    const progressData = {
      type: 'video_transfer_progress',
      taskId,
      deviceId,
      videoId,
      videoName,
      progress: Math.floor(progress),
      transferredBytes: transferredBytes || 0,
      totalBytes: totalBytes || 0,
      transferSpeed: transferSpeed || 0,
      status: status || 'transferring',
      timestamp: Date.now()
    };

    // 发送到所有连接的Socket.IO客户端
    io.emit('video_transfer_progress', progressData);

    console.log('📡 [传输进度] 进度已广播到前端:', progress + '%');

    res.json({
      success: true,
      message: '进度上报成功'
    });

  } catch (error) {
    console.error('❌ [传输进度] 上报进度失败:', error);
    res.status(500).json({
      success: false,
      message: '上报进度失败: ' + error.message
    });
  }
});

// 查询视频传输记录
app.get('/api/xiaohongshu/transfer-records', authenticateToken, async (req, res) => {
  try {
    const { page = 1, pageSize = 20, deviceId, status, videoFilename } = req.query;

    console.log('📊 [传输记录] 查询传输记录:', { page, pageSize, deviceId, status, videoFilename });

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    // 构建查询条件
    let whereClause = '1=1';
    const queryParams = [];

    if (deviceId) {
      whereClause += ' AND xvt.device_id LIKE ?';
      queryParams.push(`%${deviceId}%`);
    }

    if (status) {
      whereClause += ' AND xvt.status = ?';
      queryParams.push(status);
    }

    if (videoFilename) {
      whereClause += ' AND xvt.video_filename LIKE ?';
      queryParams.push(`%${videoFilename}%`);
    }

    // 计算偏移量
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    // 查询总数
    const [countResult] = await pool.execute(`
      SELECT COUNT(*) as total
      FROM xiaohongshu_video_transfers xvt
      LEFT JOIN devices d ON xvt.device_id = d.device_id
      WHERE ${whereClause}
    `, queryParams);

    const total = countResult[0].total;

    // 查询记录，联合设备表获取设备详细信息
    const [records] = await pool.execute(`
      SELECT
        xvt.id, xvt.video_id, xvt.device_id, xvt.device_name, xvt.transfer_type, xvt.task_id,
        xvt.transfer_time, xvt.status, xvt.transfer_progress, xvt.error_message,
        xvt.completed_time, xvt.file_size, xvt.transfer_speed, xvt.video_filename,
        d.device_info, d.last_seen as device_last_seen, d.created_at as device_created_at
      FROM xiaohongshu_video_transfers xvt
      LEFT JOIN devices d ON xvt.device_id = d.device_id
      WHERE ${whereClause}
      ORDER BY xvt.transfer_time DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(pageSize), offset]);

    // 处理查询结果，解析设备信息
    const processedRecords = records.map(record => {
      let deviceInfo = {};
      let deviceIP = '未知';

      try {
        if (record.device_info) {
          deviceInfo = typeof record.device_info === 'string'
            ? JSON.parse(record.device_info)
            : record.device_info;
          deviceIP = deviceInfo.ipAddress || '未知';
        }
      } catch (error) {
        console.error('解析设备信息失败:', error);
      }

      return {
        ...record,
        device_ip: deviceIP,
        device_info_parsed: deviceInfo,
        // 格式化时间显示
        transfer_time_formatted: record.transfer_time ? new Date(record.transfer_time).toLocaleString('zh-CN') : '',
        completed_time_formatted: record.completed_time ? new Date(record.completed_time).toLocaleString('zh-CN') : '',
        device_last_seen_formatted: record.device_last_seen ? new Date(record.device_last_seen).toLocaleString('zh-CN') : ''
      };
    });

    console.log(`📊 [传输记录] 查询完成，共 ${total} 条记录，当前页 ${processedRecords.length} 条`);

    res.json({
      success: true,
      data: {
        records: processedRecords,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    });

  } catch (error) {
    console.error('❌ [传输记录] 查询失败:', error);
    res.status(500).json({
      success: false,
      message: '查询传输记录失败: ' + error.message
    });
  }
});

// 更新视频传输状态
app.post('/api/xiaohongshu/update-transfer-status', authenticateToken, async (req, res) => {
  try {
    const { taskId, deviceId, videoId, status, progress, errorMessage } = req.body;

    console.log('📊 更新传输状态:', { taskId, deviceId, videoId, status, progress });

    if (!taskId || !deviceId || !videoId || !status) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 更新传输记录状态
      const updateFields = ['status = ?', 'transfer_progress = ?'];
      const updateValues = [status, progress || 0];

      if (status === 'completed') {
        updateFields.push('completed_time = NOW()');
      }

      if (errorMessage) {
        updateFields.push('error_message = ?');
        updateValues.push(errorMessage);
      }

      updateValues.push(taskId, deviceId, videoId);

      await connection.execute(`
        UPDATE xiaohongshu_video_transfers
        SET ${updateFields.join(', ')}
        WHERE task_id = ? AND device_id = ? AND video_id = ?
      `, updateValues);

      // 如果传输完成，更新视频文件的传输统计
      if (status === 'completed') {
        // 获取当前视频的传输设备列表
        const [videoResult] = await connection.execute(
          'SELECT transfer_count, transferred_devices FROM xiaohongshu_video_files WHERE id = ?',
          [videoId]
        );

        if (videoResult.length > 0) {
          const currentCount = videoResult[0].transfer_count || 0;
          let transferredDevices = [];

          try {
            transferredDevices = videoResult[0].transferred_devices ?
              JSON.parse(videoResult[0].transferred_devices) : [];
          } catch (e) {
            transferredDevices = [];
          }

          // 检查设备是否已经在列表中
          const deviceExists = transferredDevices.some(device => device.deviceId === deviceId);

          if (!deviceExists) {
            // 获取设备名称
            const [deviceResult] = await connection.execute(
              'SELECT device_name FROM devices WHERE device_id = ?',
              [deviceId]
            );

            const deviceName = deviceResult.length > 0 ? deviceResult[0].device_name : deviceId;

            transferredDevices.push({
              deviceId: deviceId,
              deviceName: deviceName,
              transferTime: new Date().toISOString()
            });

            // 更新视频文件记录
            await connection.execute(`
              UPDATE xiaohongshu_video_files
              SET transfer_count = ?, transferred_devices = ?, last_transfer_time = NOW()
              WHERE id = ?
            `, [currentCount + 1, JSON.stringify(transferredDevices), videoId]);
          }
        }
      }

      await connection.commit();

      res.json({
        success: true,
        message: '传输状态更新成功'
      });

    } catch (dbError) {
      await connection.rollback();
      throw dbError;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('更新传输状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新传输状态失败: ' + error.message
    });
  }
});

// 视频下载API - 供手机端下载视频文件，支持分块传输
app.get('/api/xiaohongshu/download-video/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const range = req.headers.range;

    console.log(`📥 手机端请求下载视频: ${videoId}`, range ? `范围: ${range}` : '完整文件');

    if (!videoId) {
      return res.status(400).json({
        success: false,
        message: '缺少视频ID参数'
      });
    }

    // 从数据库获取视频文件信息
    const [videos] = await pool.execute(
      'SELECT * FROM xiaohongshu_video_files WHERE id = ? AND status = "active"',
      [videoId]
    );

    if (videos.length === 0) {
      return res.status(404).json({
        success: false,
        message: '视频文件不存在'
      });
    }

    const video = videos[0];
    const filePath = video.file_path;

    console.log(`📁 视频文件路径: ${filePath}`);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.error(`❌ 视频文件不存在: ${filePath}`);
      return res.status(404).json({
        success: false,
        message: '视频文件在服务器上不存在'
      });
    }

    const fileSize = video.file_size;

    // 支持范围请求（断点续传）
    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;

      console.log(`📊 手机端分块传输: ${start}-${end}/${fileSize} (${chunksize} bytes)`);

      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${fileSize}`);
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Length', chunksize);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${video.original_name}"`);

      const fileStream = fs.createReadStream(filePath, { start, end });

      fileStream.on('error', (error) => {
        console.error(`❌ 文件流错误: ${error.message}`);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: '文件读取失败'
          });
        }
      });

      fileStream.pipe(res);
    } else {
      // 完整文件传输
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${video.original_name}"`);
      res.setHeader('Content-Length', fileSize);
      res.setHeader('Accept-Ranges', 'bytes');

      const fileStream = fs.createReadStream(filePath);

      fileStream.on('error', (error) => {
        console.error(`❌ 文件流错误: ${error.message}`);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: '文件读取失败'
          });
        }
      });

      fileStream.on('end', () => {
        console.log(`✅ 视频文件发送完成: ${video.original_name}`);
      });

      fileStream.pipe(res);
    }

  } catch (error) {
    console.error('视频下载失败:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        message: '视频下载失败: ' + error.message
      });
    }
  }
});

// 小红书实时状态API（统一处理所有功能）
app.post('/api/xiaohongshu/realtime-status', async (req, res) => {
  try {
    const {
      deviceId,
      taskId,
      // 视频发布功能字段
      publishedVideoCount,
      totalVideoCount,
      currentStep,
      errorMessage,
      // 循环群发功能字段
      sentMessageCount,
      processedControlCount,
      executionCount,
      loopCount,
      // 修改资料功能字段
      operationCount,
      processedStepCount,
      // 搜索群聊功能字段
      joinedGroupCount,
      scrollAttemptCount,
      // 文章评论功能字段
      commentedArticleCount,
      searchAttemptCount,
      // UID私信功能字段
      processedUidCount,
      successCount,
      failedCount,
      // 通用字段
      currentStatus,
      message,
      timestamp
    } = req.body;

    console.log(`📊 [小红书实时状态] 收到更新:`, {
      deviceId,
      taskId,
      currentStep,
      currentStatus,
      message,
      publishedVideoCount,
      totalVideoCount
    });

    // 构造完整的状态数据
    const statusData = {
      deviceId,
      taskId,
      // 视频发布功能字段
      publishedVideoCount,
      totalVideoCount,
      currentStep,
      errorMessage,
      // 循环群发功能字段
      sentMessageCount,
      processedControlCount,
      executionCount,
      loopCount,
      // 修改资料功能字段
      operationCount,
      processedStepCount,
      // 搜索群聊功能字段
      joinedGroupCount,
      scrollAttemptCount,
      // 文章评论功能字段
      commentedArticleCount,
      searchAttemptCount,
      // UID私信功能字段
      processedUidCount,
      successCount,
      failedCount,
      // 通用字段
      currentStatus,
      message,
      timestamp: timestamp || new Date().toISOString()
    };

    // 广播实时状态到所有连接的客户端
    io.emit('xiaohongshu_realtime_status', statusData);

    console.log(`✅ [小红书发布视频] 实时状态已广播`);

    res.json({
      success: true,
      message: '实时状态更新成功'
    });

  } catch (error) {
    console.error('处理实时状态更新失败:', error);
    res.status(500).json({
      success: false,
      message: '处理实时状态更新失败: ' + error.message
    });
  }
});

// 智能视频选择API
app.post('/api/xiaohongshu/smart-select-videos', async (req, res) => {
  try {
    const { deviceIds, videoCount } = req.body;

    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID列表'
      });
    }

    const requestedVideoCount = videoCount || deviceIds.length;

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    const connection = await pool.getConnection();

    try {
      // 获取所有可用视频，包含传输信息
      const [allVideos] = await connection.execute(
        `SELECT
          vf.id,
          vf.original_name,
          vf.file_size,
          vf.transfer_count,
          vf.transferred_devices,
          vf.last_transfer_time,
          COUNT(DISTINCT vt.device_id) as total_transfers,
          COUNT(CASE WHEN vt.status = 'completed' THEN 1 END) as successful_transfers
        FROM xiaohongshu_video_files vf
        LEFT JOIN xiaohongshu_video_transfers vt ON vf.id = vt.video_id
        WHERE vf.status = 'active'
        GROUP BY vf.id
        ORDER BY vf.upload_time DESC`
      );

      if (allVideos.length === 0) {
        connection.release();
        return res.status(400).json({
          success: false,
          message: '没有可用的视频文件'
        });
      }

      // 智能选择视频的逻辑
      const selectedVideos = smartSelectVideos(allVideos, deviceIds, requestedVideoCount);

      connection.release();

      res.json({
        success: true,
        message: `智能选择了 ${selectedVideos.length} 个视频`,
        data: {
          selectedVideos: selectedVideos,
          totalAvailable: allVideos.length,
          selectionStrategy: getSelectionStrategy(allVideos, deviceIds)
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('智能视频选择失败:', error);
    res.status(500).json({
      success: false,
      message: '智能视频选择失败: ' + error.message
    });
  }
});

// 智能视频选择算法
function smartSelectVideos(allVideos, deviceIds, requestedCount) {
  const selectedVideos = [];
  const deviceIdsSet = new Set(deviceIds);

  // 1. 优先选择从未传输过的视频
  const neverTransferred = allVideos.filter(video =>
    (video.total_transfers || 0) === 0
  );

  // 2. 选择没有传输到当前设备的视频
  const notTransferredToSelectedDevices = allVideos.filter(video => {
    if (!video.transferred_devices) return true;

    try {
      const transferredDevices = typeof video.transferred_devices === 'string'
        ? JSON.parse(video.transferred_devices)
        : video.transferred_devices;

      if (!Array.isArray(transferredDevices)) return true;

      // 检查是否有任何选中的设备已经传输过这个视频
      return !transferredDevices.some(device =>
        deviceIdsSet.has(device.deviceId)
      );
    } catch (e) {
      return true; // 解析失败时认为没有传输过
    }
  });

  // 3. 按传输次数排序（传输次数少的优先）
  const sortedByTransferCount = [...allVideos].sort((a, b) => {
    const aCount = a.total_transfers || 0;
    const bCount = b.total_transfers || 0;
    return aCount - bCount;
  });

  // 选择策略：优先级递减
  const selectionPools = [
    neverTransferred,
    notTransferredToSelectedDevices,
    sortedByTransferCount
  ];

  for (const pool of selectionPools) {
    if (selectedVideos.length >= requestedCount) break;

    const remainingCount = requestedCount - selectedVideos.length;
    const availableVideos = pool.filter(video =>
      !selectedVideos.some(selected => selected.id === video.id)
    );

    // 随机选择或按顺序选择
    const videosToAdd = availableVideos.slice(0, remainingCount);
    selectedVideos.push(...videosToAdd);
  }

  return selectedVideos;
}

// 获取选择策略说明
function getSelectionStrategy(allVideos, deviceIds) {
  const neverTransferredCount = allVideos.filter(v => (v.total_transfers || 0) === 0).length;
  const deviceIdsSet = new Set(deviceIds);

  const notToSelectedDevicesCount = allVideos.filter(video => {
    if (!video.transferred_devices) return true;
    try {
      const transferredDevices = typeof video.transferred_devices === 'string'
        ? JSON.parse(video.transferred_devices)
        : video.transferred_devices;
      if (!Array.isArray(transferredDevices)) return true;
      return !transferredDevices.some(device => deviceIdsSet.has(device.deviceId));
    } catch (e) {
      return true;
    }
  }).length;

  return {
    neverTransferredCount,
    notToSelectedDevicesCount,
    totalVideos: allVideos.length,
    strategy: '优先选择未传输视频 → 未传输到选中设备的视频 → 传输次数最少的视频'
  };
}

// 获取已上传的视频文件列表
app.get('/api/xiaohongshu/video-files', async (req, res) => {
  try {
    const { page = 1, limit = 20, status = 'active' } = req.query;
    const offset = (page - 1) * limit;

    const connection = await pool.getConnection();

    try {
      // 获取视频文件列表（包含传输信息）
      let videos = [];

      try {
        // 首先检查传输相关表是否存在
        const [transferTables] = await connection.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND TABLE_NAME IN ('xiaohongshu_video_transfers', 'xiaohongshu_video_assignments')
        `);

        const hasTransferTable = transferTables.some(t => t.TABLE_NAME === 'xiaohongshu_video_transfers');
        const hasAssignmentTable = transferTables.some(t => t.TABLE_NAME === 'xiaohongshu_video_assignments');

        if (hasTransferTable && hasAssignmentTable) {
          // 表存在，查询真实数据
          const [realVideos] = await connection.execute(
            `SELECT
              vf.id,
              vf.file_name,
              vf.original_name,
              vf.file_size,
              vf.video_duration,
              vf.video_format,
              vf.video_resolution,
              vf.thumbnail_path,
              vf.uploaded_by,
              vf.upload_time,
              vf.description,
              vf.tags,
              vf.transfer_count,
              vf.transferred_devices,
              vf.last_transfer_time,
              COUNT(va.id) as assignment_count,
              COUNT(CASE WHEN va.status = 'completed' THEN 1 END) as completed_count,
              COUNT(DISTINCT CASE WHEN vt.status = 'completed' THEN vt.device_id END) as total_transfer_count,
              COUNT(DISTINCT CASE WHEN vt.status = 'completed' THEN vt.device_id END) as successful_transfer_count,
              MAX(vt.transfer_time) as latest_transfer_time
            FROM xiaohongshu_video_files vf
            LEFT JOIN xiaohongshu_video_assignments va ON vf.id = va.video_id
            LEFT JOIN xiaohongshu_video_transfers vt ON vf.id = vt.video_id
            WHERE vf.status = ?
            GROUP BY vf.id
            ORDER BY vf.upload_time DESC
            LIMIT ? OFFSET ?`,
            [status, parseInt(limit), parseInt(offset)]
          );

          // 为每个视频查询实际的传输设备信息
          for (let video of realVideos) {
            try {
              const [transferDevices] = await connection.execute(
                `SELECT DISTINCT
                   vt.device_id,
                   vt.device_name,
                   vt.transfer_time,
                   d.device_info
                 FROM xiaohongshu_video_transfers vt
                 LEFT JOIN devices d ON vt.device_id = d.device_id
                 WHERE vt.video_id = ? AND vt.status = 'completed'
                 ORDER BY vt.transfer_time DESC`,
                [video.id]
              );

              // 构建传输设备信息，去重并提取IP地址
              if (transferDevices.length > 0) {
                const uniqueDevices = new Map();

                transferDevices.forEach(device => {
                  const deviceId = device.device_id;

                  // 解析设备信息获取IP地址
                  let ipAddress = '未知';
                  try {
                    if (device.device_info) {
                      const deviceInfo = typeof device.device_info === 'string'
                        ? JSON.parse(device.device_info)
                        : device.device_info;
                      ipAddress = deviceInfo.ipAddress || '未知';
                    }
                  } catch (e) {
                    console.error('解析设备信息失败:', e);
                  }

                  // 如果设备已存在，只更新最新传输时间
                  if (uniqueDevices.has(deviceId)) {
                    const existing = uniqueDevices.get(deviceId);
                    if (new Date(device.transfer_time) > new Date(existing.transferTime)) {
                      existing.transferTime = device.transfer_time;
                    }
                  } else {
                    // 新设备，添加到Map中
                    uniqueDevices.set(deviceId, {
                      deviceId: device.device_id,
                      deviceName: device.device_name,
                      ipAddress: ipAddress,
                      transferTime: device.transfer_time
                    });
                  }
                });

                video.transferred_devices = JSON.stringify(Array.from(uniqueDevices.values()));
                // 使用最新的传输时间
                video.last_transfer_time = video.latest_transfer_time || transferDevices[0].transfer_time;
              }
            } catch (error) {
              console.error(`查询视频${video.id}的传输设备信息失败:`, error);
            }
          }

          videos = realVideos;
        } else {
          // 表不存在，只查询基本视频信息并生成模拟传输数据
          const [basicVideos] = await connection.execute(
            `SELECT
              vf.id,
              vf.file_name,
              vf.original_name,
              vf.file_size,
              vf.video_duration,
              vf.video_format,
              vf.video_resolution,
              vf.thumbnail_path,
              vf.uploaded_by,
              vf.upload_time,
              vf.description,
              vf.tags
            FROM xiaohongshu_video_files vf
            WHERE vf.status = ?
            ORDER BY vf.upload_time DESC
            LIMIT ? OFFSET ?`,
            [status, parseInt(limit), parseInt(offset)]
          );

          // 直接返回基本视频信息，不生成模拟数据
          videos = basicVideos;
        }
      } catch (error) {
        console.log('查询视频传输信息失败，使用基本信息:', error.message);
        // 查询失败，只获取基本视频信息
        const [basicVideos] = await connection.execute(
          `SELECT * FROM xiaohongshu_video_files WHERE status = ? ORDER BY upload_time DESC LIMIT ? OFFSET ?`,
          [status, parseInt(limit), parseInt(offset)]
        );

        // 直接返回基本视频信息，不生成模拟数据
        videos = basicVideos;
      }

      // 获取总数
      const [countResult] = await connection.execute(
        'SELECT COUNT(*) as total FROM xiaohongshu_video_files WHERE status = ?',
        [status]
      );

      connection.release();

      res.json({
        success: true,
        data: {
          videos: videos,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].total,
            totalPages: Math.ceil(countResult[0].total / limit)
          }
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('获取视频文件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取视频文件列表失败: ' + error.message
    });
  }
});

// 获取视频文件统计信息
app.get('/api/xiaohongshu/video-stats', async (req, res) => {
  try {
    if (!pool) {
      return res.json({
        success: true,
        data: {
          totalVideos: 0,
          totalSize: 0,
          todayUploads: 0
        }
      });
    }

    const connection = await pool.getConnection();

    try {
      // 获取总数和总大小
      const [statsResult] = await connection.execute(
        `SELECT
          COUNT(*) as total_count,
          COALESCE(SUM(file_size), 0) as total_size
        FROM xiaohongshu_video_files
        WHERE status = 'active'`
      );

      // 获取今日上传数量
      const [todayResult] = await connection.execute(
        `SELECT COUNT(*) as today_uploads
        FROM xiaohongshu_video_files
        WHERE status = 'active'
        AND DATE(upload_time) = CURDATE()`
      );

      connection.release();

      res.json({
        success: true,
        data: {
          totalVideos: statsResult[0].total_count,
          totalSize: statsResult[0].total_size,
          todayUploads: todayResult[0].today_uploads
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('获取视频统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取视频统计信息失败: ' + error.message
    });
  }
});

// 删除视频文件
app.delete('/api/xiaohongshu/video-files/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    const connection = await pool.getConnection();

    try {
      // 获取视频文件信息
      const [videos] = await connection.execute(
        'SELECT file_path FROM xiaohongshu_video_files WHERE id = ? AND status = "active"',
        [videoId]
      );

      if (videos.length === 0) {
        connection.release();
        return res.status(404).json({ success: false, message: '视频文件不存在' });
      }

      // 软删除视频文件记录
      await connection.execute(
        'UPDATE xiaohongshu_video_files SET status = "deleted" WHERE id = ?',
        [videoId]
      );

      // 删除物理文件
      const filePath = videos[0].file_path;
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      connection.release();

      res.json({
        success: true,
        message: '视频文件删除成功'
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('删除视频文件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除视频文件失败: ' + error.message
    });
  }
});

// 分配视频给设备
app.post('/api/xiaohongshu/assign-videos', async (req, res) => {
  try {
    const { videoIds, deviceIds, taskId } = req.body;

    if (!videoIds || !deviceIds || !taskId || videoIds.length === 0 || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：videoIds, deviceIds, taskId'
      });
    }

    const connection = await pool.getConnection();

    try {
      // 获取设备信息
      const devicePlaceholders = deviceIds.map(() => '?').join(',');
      const [devices] = await connection.execute(
        `SELECT device_id, device_name FROM devices WHERE device_id IN (${devicePlaceholders})`,
        deviceIds
      );

      if (devices.length !== deviceIds.length) {
        connection.release();
        return res.status(400).json({
          success: false,
          message: '部分设备不存在或离线'
        });
      }

      // 验证视频文件存在
      const videoPlaceholders = videoIds.map(() => '?').join(',');
      const [videos] = await connection.execute(
        `SELECT id, original_name FROM xiaohongshu_video_files WHERE id IN (${videoPlaceholders}) AND status = 'active'`,
        videoIds
      );

      if (videos.length !== videoIds.length) {
        connection.release();
        return res.status(400).json({
          success: false,
          message: '部分视频文件不存在'
        });
      }

      // 平均分配视频给设备
      const assignments = [];
      const videosPerDevice = Math.ceil(videoIds.length / deviceIds.length);

      for (let i = 0; i < deviceIds.length; i++) {
        const device = devices[i];
        const startIndex = i * videosPerDevice;
        const endIndex = Math.min(startIndex + videosPerDevice, videoIds.length);
        const assignedVideoIds = videoIds.slice(startIndex, endIndex);

        for (const videoId of assignedVideoIds) {
          assignments.push([
            videoId,
            device.device_id,
            device.device_name,
            taskId
          ]);
        }
      }

      // 批量插入分配记录
      if (assignments.length > 0) {
        await connection.query(
          `INSERT INTO xiaohongshu_video_assignments
           (video_id, device_id, device_name, task_id) VALUES ?`,
          [assignments]
        );
      }

      connection.release();

      res.json({
        success: true,
        message: '视频分配成功',
        data: {
          totalVideos: videoIds.length,
          totalDevices: deviceIds.length,
          assignments: assignments.length
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('分配视频失败:', error);
    res.status(500).json({
      success: false,
      message: '分配视频失败: ' + error.message
    });
  }
});

// 获取设备的视频分配情况
app.get('/api/xiaohongshu/device-video-assignments/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { taskId } = req.query;

    const connection = await pool.getConnection();

    try {
      let query = `
        SELECT
          va.id as assignment_id,
          va.video_id,
          va.status,
          va.upload_progress,
          va.assignment_time,
          va.completed_time,
          va.video_title,
          va.video_description,
          va.error_message,
          vf.original_name,
          vf.file_size,
          vf.video_format,
          vf.video_duration,
          vf.description as video_description_original,
          vf.tags
        FROM xiaohongshu_video_assignments va
        JOIN xiaohongshu_video_files vf ON va.video_id = vf.id
        WHERE va.device_id = ?
      `;

      const params = [deviceId];

      if (taskId) {
        query += ' AND va.task_id = ?';
        params.push(taskId);
      }

      query += ' ORDER BY va.assignment_time DESC';

      const [assignments] = await connection.execute(query, params);

      connection.release();

      res.json({
        success: true,
        data: {
          deviceId: deviceId,
          assignments: assignments
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('获取设备视频分配失败:', error);
    res.status(500).json({
      success: false,
      message: '获取设备视频分配失败: ' + error.message
    });
  }
});

// 视频发布进度上报
app.post('/api/xiaohongshu/video-publish-progress', async (req, res) => {
  try {
    const { taskId, deviceId, deviceName, publishedCount, totalCount, currentStep, errorMessage } = req.body;

    if (!taskId || !deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：taskId, deviceId'
      });
    }

    // 计算进度百分比
    const progress = totalCount > 0 ? Math.floor((publishedCount / totalCount) * 100) : 0;

    // 更新数据库中的执行状态
    if (pool) {
      try {
        await pool.execute(
          `UPDATE xiaohongshu_video_execution_logs
           SET progress_percentage = ?,
               current_step = ?,
               published_count = ?,
               error_message = ?,
               updated_at = NOW()
           WHERE task_id = ? AND device_id = ?`,
          [progress, currentStep, publishedCount, errorMessage || null, taskId, deviceId]
        );
      } catch (dbError) {
        console.error('更新视频发布进度失败:', dbError);
      }
    }

    // 通过WebSocket发送进度更新到前端
    io.emit('xiaohongshu_video_publish_progress', {
      taskId,
      deviceId,
      deviceName,
      publishedCount,
      totalCount,
      progress,
      currentStep,
      errorMessage,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: '进度上报成功'
    });

  } catch (error) {
    console.error('处理视频发布进度上报失败:', error);
    res.status(500).json({
      success: false,
      message: '处理进度上报失败: ' + error.message
    });
  }
});

// 视频发布结果上报
app.post('/api/xiaohongshu/video-publish-result', async (req, res) => {
  try {
    const { taskId, deviceId, success, message, publishedCount, totalCount } = req.body;

    if (!taskId || !deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：taskId, deviceId'
      });
    }

    console.log(`📊 [视频发布结果] 设备 ${deviceId} 任务 ${taskId} 执行${success ? '成功' : '失败'}: ${message}`);

    // 更新小红书执行日志
    if (xiaohongshuLogService) {
      try {
        const logId = taskId + '_' + deviceId;
        const finalStatus = success ? 'completed' : 'failed';
        const progress = success ? 100 : 0;
        const statusMessage = success ? '执行完成' : '执行失败';
        const executionLogs = `脚本执行${success ? '成功' : '失败'}: ${message}`;

        await xiaohongshuLogService.updateExecutionStatus(
          logId,
          finalStatus,
          progress,
          statusMessage,
          executionLogs
        );

        if (message) {
          await xiaohongshuLogService.updateExecutionResult(logId, {
            status: success ? 'success' : 'failed',
            result: message,
            publishedCount: publishedCount || 0,
            totalCount: totalCount || 0,
            timestamp: new Date().toISOString()
          });
        }

        console.log(`✅ [视频发布结果] 执行日志已更新: ${logId} -> ${finalStatus} (${progress}%)`);
      } catch (logError) {
        console.error('更新视频发布执行日志失败:', logError);
      }
    }

    // 更新对应的视频传输记录状态
    if (pool) {
      try {
        const transferStatus = success ? 'completed' : 'failed';
        const [updateResult] = await pool.execute(`
          UPDATE xiaohongshu_video_transfers
          SET status = ?, completed_time = NOW()
          WHERE task_id = ? AND status = 'pending'
        `, [transferStatus, taskId]);

        console.log(`✅ [视频传输记录] 已更新 ${updateResult.affectedRows} 条传输记录状态为: ${transferStatus}`);
      } catch (transferError) {
        console.error('更新视频传输记录状态失败:', transferError);
      }
    }

    // 从活动任务中移除已完成的任务，防止设备断开连接时被误标记为失败
    if (xiaohongshuActiveTasks.has(taskId)) {
      const task = xiaohongshuActiveTasks.get(taskId);
      task.status = success ? 'completed' : 'failed';
      task.endTime = new Date();
      task.result = message;

      // 移除活动任务
      xiaohongshuActiveTasks.delete(taskId);
      console.log(`✅ [活动任务] 已从活动任务列表中移除: ${taskId}`);
    }

    // 通过WebSocket发送执行完成事件到前端
    io.emit('xiaohongshu_video_publish_completed', {
      taskId,
      deviceId,
      success,
      message,
      publishedCount: publishedCount || 0,
      totalCount: totalCount || 0,
      timestamp: new Date().toISOString()
    });

    // 更新设备状态为在线（执行完成后）
    try {
      await updateDeviceStatus(deviceId, 'online');
      console.log(`🔄 [设备状态] 设备 ${deviceId} 状态已更新为在线`);
      // updateDeviceStatus函数已经会广播设备状态更新，不需要重复发送
    } catch (error) {
      console.error('更新设备状态失败:', error);
    }

    // 通知手机端关闭小红书应用
    console.log(`通知设备 ${deviceId} 关闭小红书应用`);
    io.to(deviceId).emit('script_command', {
      type: 'close_xiaohongshu_app',
      deviceId: deviceId,
      reason: success ? '视频发布完成' : '视频发布失败',
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: '执行结果上报成功'
    });

  } catch (error) {
    console.error('视频发布结果上报失败:', error);
    res.status(500).json({
      success: false,
      message: '结果上报失败: ' + error.message
    });
  }
});

// 获取视频传输记录
app.get('/api/videos/:videoId/transfer-records', authenticateToken, async (req, res) => {
  try {
    console.log('🚀 [API] /api/videos/:videoId/transfer-records 被调用');
    const { videoId } = req.params;
    const userId = req.user?.id;

    console.log('📋 [API] 请求参数:', { videoId, userId });

    if (!userId) {
      console.log('❌ [API] 用户未登录');
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    console.log(`📊 [视频传输记录] 获取视频 ${videoId} 的传输记录，用户 ${userId}`);

    // 获取传输记录
    let records = [];

    if (pool) {
      try {
        // 检查可能的传输记录表
        console.log('🔍 [视频传输记录] 检查可能的传输记录表...');

        // 检查所有可能的传输相关表
        const [allTables] = await pool.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND (TABLE_NAME LIKE '%transfer%' OR TABLE_NAME LIKE '%video%')
        `);

        console.log('📋 [视频传输记录] 所有相关表:', allTables);

        // 优先检查xiaohongshu_video_transfers表（与视频列表API保持一致）
        const [xhsTables] = await pool.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND TABLE_NAME = 'xiaohongshu_video_transfers'
        `);

        console.log('📋 [视频传输记录] xiaohongshu_video_transfers表检查结果:', xhsTables);

        if (xhsTables.length > 0) {
          // xiaohongshu_video_transfers表存在，查询真实数据
          console.log('✅ [视频传输记录] xiaohongshu_video_transfers表存在，查询真实数据...');
          // 先查询所有该视频的传输记录，不过滤user_id
          const [allTransferRecords] = await pool.execute(`
            SELECT
              xvt.*,
              d.device_name as devices_device_name,
              d.device_info,
              xvt.transfer_time,
              xvt.status as transfer_status
            FROM xiaohongshu_video_transfers xvt
            LEFT JOIN devices d ON xvt.device_id = d.device_id
            WHERE xvt.video_id = ?
            ORDER BY xvt.transfer_time DESC
          `, [videoId]);

          console.log('📊 [视频传输记录] 所有传输记录查询结果:', allTransferRecords.length, '条记录');
          console.log('📊 [视频传输记录] 所有传输记录内容:', allTransferRecords);

          // 直接返回所有找到的记录，不过滤user_id
          console.log('📊 [视频传输记录] xiaohongshu_video_transfers查询结果:', allTransferRecords.length, '条记录');
          console.log('📊 [视频传输记录] xiaohongshu_video_transfers记录内容:', allTransferRecords);

          // 处理记录，解析设备信息
          records = allTransferRecords.map(record => {
            let deviceInfo = {};
            let deviceIP = '未知';

            try {
              if (record.device_info) {
                deviceInfo = typeof record.device_info === 'string'
                  ? JSON.parse(record.device_info)
                  : record.device_info;
                deviceIP = deviceInfo.ipAddress || '未知';
              }
            } catch (error) {
              console.error('解析设备信息失败:', error);
            }

            return {
              ...record,
              device_ip: deviceIP,
              device_info_parsed: deviceInfo
            };
          });

          if (allTransferRecords.length === 0) {
            console.log('⚠️ [视频传输记录] xiaohongshu_video_transfers数据为空，返回空记录');
            records = [];
          }
        } else {
          // 检查video_transfer_logs表
          const [tables] = await pool.execute(`
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = 'autojs_control'
            AND TABLE_NAME = 'video_transfer_logs'
          `);

          console.log('📋 [视频传输记录] video_transfer_logs表检查结果:', tables);

          if (tables.length > 0) {
            // video_transfer_logs表存在，查询真实数据
            console.log('✅ [视频传输记录] video_transfer_logs表存在，查询真实数据...');
            const [transferRecords] = await pool.execute(`
              SELECT
                vtl.*,
                d.device_name,
                d.device_id,
                d.device_ip
              FROM video_transfer_logs vtl
              LEFT JOIN devices d ON vtl.device_id = d.device_id
              WHERE vtl.video_id = ? AND vtl.user_id = ?
              ORDER BY vtl.transfer_time DESC
            `, [videoId, userId]);

            console.log('📊 [视频传输记录] video_transfer_logs查询结果:', transferRecords.length, '条记录');
            console.log('📊 [视频传输记录] video_transfer_logs记录内容:', transferRecords);
            records = transferRecords;

            if (transferRecords.length === 0) {
              console.log('⚠️ [视频传输记录] video_transfer_logs数据为空，返回空记录');
              records = [];
            }
          } else {
            // video_transfer_logs表不存在，返回空记录
            console.log('❌ [视频传输记录] video_transfer_logs表不存在，返回空记录');
            records = [];
          }
        }
      } catch (error) {
        console.log('查询传输记录失败，返回空记录:', error.message);
        records = [];
      }
    } else {
      // 数据库不可用，返回空记录
      console.log('❌ [视频传输记录] 数据库不可用，返回空记录');
      records = [];
    }

    // 计算统计信息
    const totalTransfers = records.length;
    const uniqueDevices = new Set(records.map(r => r.device_id)).size;
    const successfulTransfers = records.filter(r => r.transfer_status === 'completed').length;
    const successRate = totalTransfers > 0 ? Math.round((successfulTransfers / totalTransfers) * 100) : 0;

    res.json({
      success: true,
      records: records,
      statistics: {
        totalTransfers,
        uniqueDevices,
        successRate
      }
    });

  } catch (error) {
    console.error('获取视频传输记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取传输记录失败: ' + error.message
    });
  }
});

// 测试API：模拟视频传输进度
app.post('/api/videos/test-transfer-progress', authenticateToken, async (req, res) => {
  try {
    const { taskId, deviceId } = req.body;

    console.log('🧪 [测试传输进度] 开始模拟传输进度');
    console.log('🧪 [测试传输进度] 任务ID:', taskId);
    console.log('🧪 [测试传输进度] 设备ID:', deviceId);

    // 模拟传输进度
    let progress = 0;
    const totalBytes = 50 * 1024 * 1024; // 50MB
    const videoName = '测试视频.mp4';

    const progressInterval = setInterval(() => {
      progress += Math.random() * 15; // 每次增加0-15%
      if (progress > 100) progress = 100;

      const transferredBytes = Math.floor((progress / 100) * totalBytes);
      const transferSpeed = Math.random() * 2 * 1024 * 1024; // 0-2MB/s

      const progressData = {
        type: 'video_transfer_progress',
        taskId,
        deviceId,
        videoId: 999,
        videoName,
        progress: Math.floor(progress),
        transferredBytes,
        totalBytes,
        transferSpeed,
        status: progress >= 100 ? 'completed' : 'transferring',
        timestamp: Date.now()
      };

      console.log(`🧪 [测试传输进度] 进度: ${Math.floor(progress)}%`);

      // 发送到前端
      io.emit('video_transfer_progress', progressData);

      if (progress >= 100) {
        clearInterval(progressInterval);
        console.log('🧪 [测试传输进度] 模拟传输完成');
      }
    }, 2000); // 每2秒更新一次

    res.json({
      success: true,
      message: '开始模拟传输进度'
    });

  } catch (error) {
    console.error('❌ [测试传输进度] 模拟失败:', error);
    res.status(500).json({
      success: false,
      message: '模拟传输进度失败: ' + error.message
    });
  }
});

// 接收视频传输进度报告
app.post('/api/videos/transfer-progress', authenticateToken, async (req, res) => {
  try {
    const {
      taskId,
      deviceId,
      videoId,
      videoName,
      progress,
      transferredBytes,
      totalBytes,
      transferSpeed,
      status,
      errorMessage
    } = req.body;

    console.log('📊📊📊 [传输进度] ===== 接收到传输进度报告 ===== 📊📊📊');
    console.log('📊 [传输进度] 任务ID:', taskId);
    console.log('📊 [传输进度] 设备ID:', deviceId);
    console.log('📊 [传输进度] 视频ID:', videoId);
    console.log('📊 [传输进度] 视频名称:', videoName);
    console.log('📊 [传输进度] 进度:', progress + '%');
    console.log('📊 [传输进度] 已传输:', transferredBytes, '/', totalBytes, '字节');
    console.log('📊 [传输进度] 传输速度:', transferSpeed, 'bytes/s');
    console.log('📊 [传输进度] 状态:', status);

    // 广播进度到前端
    const progressData = {
      type: 'video_transfer_progress',
      taskId,
      deviceId,
      videoId,
      videoName,
      progress,
      transferredBytes,
      totalBytes,
      transferSpeed,
      status,
      errorMessage,
      timestamp: Date.now()
    };

    // 发送到所有连接的Socket.IO客户端
    io.emit('video_transfer_progress', progressData);

    console.log('📡 [传输进度] 进度已广播到前端');

    res.json({
      success: true,
      message: '进度报告已接收'
    });

  } catch (error) {
    console.error('❌ [传输进度] 处理进度报告失败:', error);
    res.status(500).json({
      success: false,
      message: '处理进度报告失败: ' + error.message
    });
  }
});

// 记录视频传输到设备
app.post('/api/videos/transfer-record', authenticateToken, async (req, res) => {
  try {
    console.log('📝 [视频传输记录] 记录视频传输操作');
    const { videoId, deviceId, status = 'completed', errorMessage = null } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    if (!videoId || !deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：videoId, deviceId'
      });
    }

    console.log('📝 [视频传输记录] 参数:', { videoId, deviceId, status, userId });

    if (pool) {
      try {
        // 检查xiaohongshu_video_transfers表是否存在
        const [tables] = await pool.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND TABLE_NAME = 'xiaohongshu_video_transfers'
        `);

        if (tables.length > 0) {
          // 表存在，插入记录
          console.log('✅ [视频传输记录] xiaohongshu_video_transfers表存在，插入记录');

          // 获取设备信息
          let deviceName = deviceId;
          try {
            const [devices] = await pool.execute(`
              SELECT device_name FROM devices WHERE device_id = ?
            `, [deviceId]);

            if (devices.length > 0) {
              deviceName = devices[0].device_name;
            }
          } catch (error) {
            console.log('⚠️ [视频传输记录] 获取设备名称失败，使用设备ID:', error.message);
          }

          // 生成任务ID
          const taskId = `manual_transfer_${Date.now()}`;

          // 获取视频文件名
          let videoFilename = '';
          try {
            const [videoFiles] = await pool.execute(`
              SELECT original_name FROM xiaohongshu_video_files WHERE id = ?
            `, [videoId]);

            if (videoFiles.length > 0) {
              videoFilename = videoFiles[0].original_name;
            }
          } catch (error) {
            console.log('⚠️ [视频传输记录] 获取视频文件名失败，使用空值:', error.message);
          }

          await pool.execute(`
            INSERT INTO xiaohongshu_video_transfers
            (video_id, device_id, device_name, transfer_type, task_id, status, error_message, video_filename, transfer_time, completed_time)
            VALUES (?, ?, ?, 'manual', ?, ?, ?, ?, NOW(), NOW())
          `, [videoId, deviceId, deviceName, taskId, status, errorMessage, videoFilename]);

          console.log('✅ [视频传输记录] 记录插入成功，任务ID:', taskId);
        } else {
          // 表不存在，尝试创建表
          console.log('❌ [视频传输记录] xiaohongshu_video_transfers表不存在，尝试创建');
          await pool.execute(`
            CREATE TABLE IF NOT EXISTS xiaohongshu_video_transfers (
              id INT AUTO_INCREMENT PRIMARY KEY,
              video_id INT NOT NULL,
              device_id VARCHAR(255) NOT NULL,
              device_name VARCHAR(255) NOT NULL,
              transfer_type ENUM('manual', 'script_execution') DEFAULT 'manual',
              task_id VARCHAR(255) NULL,
              transfer_time TIMESTAMP NULL,
              status ENUM('pending', 'in_progress', 'completed', 'failed') DEFAULT 'completed',
              transfer_progress INT DEFAULT 100,
              error_message TEXT NULL,
              completed_time TIMESTAMP NULL,
              file_size BIGINT NULL,
              transfer_speed DECIMAL(10,2) NULL,
              INDEX idx_video_id (video_id),
              INDEX idx_device_id (device_id),
              INDEX idx_transfer_time (transfer_time)
            )
          `);

          // 插入记录
          await pool.execute(`
            INSERT INTO xiaohongshu_video_transfers
            (video_id, device_id, status, error_message, video_filename, transfer_time, completed_time)
            VALUES (?, ?, ?, ?, '', NOW(), NOW())
          `, [videoId, deviceId, status, errorMessage]);

          console.log('✅ [视频传输记录] 表创建成功并插入记录');
        }

        res.json({
          success: true,
          message: '传输记录保存成功'
        });

      } catch (error) {
        console.error('❌ [视频传输记录] 数据库操作失败:', error);
        res.status(500).json({
          success: false,
          message: '保存传输记录失败: ' + error.message
        });
      }
    } else {
      res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

  } catch (error) {
    console.error('❌ [视频传输记录] 记录传输操作失败:', error);
    res.status(500).json({
      success: false,
      message: '记录传输操作失败: ' + error.message
    });
  }
});

// 调试API：查看视频传输设备信息处理结果
app.get('/api/debug/video-devices/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;
    console.log('🔍 [调试] 查看视频传输设备处理结果，视频ID:', videoId);

    if (!pool) {
      return res.json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    const connection = await pool.getConnection();

    try {
      // 执行与主API相同的查询逻辑
      const [transferDevices] = await connection.execute(
        `SELECT DISTINCT
           vt.device_id,
           vt.device_name,
           vt.transfer_time,
           d.device_info
         FROM xiaohongshu_video_transfers vt
         LEFT JOIN devices d ON vt.device_id = d.device_id
         WHERE vt.video_id = ? AND vt.status = 'completed'
         ORDER BY vt.transfer_time DESC`,
        [videoId]
      );

      console.log('📊 [调试] 原始查询结果:', transferDevices);

      // 执行与主API相同的处理逻辑
      const uniqueDevices = new Map();

      transferDevices.forEach(device => {
        const deviceId = device.device_id;

        // 解析设备信息获取IP地址
        let ipAddress = '未知';
        try {
          if (device.device_info) {
            const deviceInfo = typeof device.device_info === 'string'
              ? JSON.parse(device.device_info)
              : device.device_info;
            ipAddress = deviceInfo.ipAddress || '未知';
          }
        } catch (e) {
          console.error('解析设备信息失败:', e);
        }

        // 如果设备已存在，只更新最新传输时间
        if (uniqueDevices.has(deviceId)) {
          const existing = uniqueDevices.get(deviceId);
          if (new Date(device.transfer_time) > new Date(existing.transferTime)) {
            existing.transferTime = device.transfer_time;
          }
        } else {
          // 新设备，添加到Map中
          uniqueDevices.set(deviceId, {
            deviceId: device.device_id,
            deviceName: device.device_name,
            ipAddress: ipAddress,
            transferTime: device.transfer_time
          });
        }
      });

      const finalDevices = Array.from(uniqueDevices.values());
      console.log('📊 [调试] 最终处理结果:', finalDevices);

      connection.release();

      res.json({
        success: true,
        data: {
          videoId: videoId,
          rawData: transferDevices,
          processedDevices: finalDevices,
          deviceCount: finalDevices.length
        }
      });

    } catch (dbError) {
      connection.release();
      throw dbError;
    }

  } catch (error) {
    console.error('调试查询失败:', error);
    res.status(500).json({
      success: false,
      message: '查询失败: ' + error.message
    });
  }
});

// 调试API：查看具体视频的传输记录
app.get('/api/debug/video-transfers/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    console.log('🔍 [调试] 查看视频传输记录，视频ID:', videoId);

    if (!pool) {
      return res.json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    // 查询xiaohongshu_video_transfers表
    const [transfers] = await pool.execute(`
      SELECT * FROM xiaohongshu_video_transfers
      WHERE video_id = ?
      ORDER BY transfer_time DESC
    `, [videoId]);

    console.log('📊 [调试] 传输记录查询结果:', transfers);

    // 查询视频基本信息
    const [videos] = await pool.execute(`
      SELECT * FROM xiaohongshu_video_files
      WHERE id = ?
    `, [videoId]);

    console.log('📊 [调试] 视频基本信息:', videos);

    res.json({
      success: true,
      data: {
        videoInfo: videos[0] || null,
        transfers: transfers,
        transferCount: transfers.length
      }
    });

  } catch (error) {
    console.error('❌ [调试] 查看视频传输记录失败:', error);
    res.status(500).json({
      success: false,
      message: '查看视频传输记录失败: ' + error.message
    });
  }
});

// 调试API：查看数据库表结构
app.get('/api/debug/database-tables', authenticateToken, async (req, res) => {
  try {
    console.log('🔍 [调试] 查看数据库表结构');

    if (!pool) {
      return res.json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    // 获取所有表
    const [allTables] = await pool.execute(`
      SELECT TABLE_NAME, TABLE_COMMENT
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_SCHEMA = 'autojs_control'
      ORDER BY TABLE_NAME
    `);

    // 获取包含video或transfer的表
    const [videoTables] = await pool.execute(`
      SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = 'autojs_control'
      AND (TABLE_NAME LIKE '%video%' OR TABLE_NAME LIKE '%transfer%')
      ORDER BY TABLE_NAME, ORDINAL_POSITION
    `);

    // 获取xiaohongshu相关表的数据量
    const tableStats = {};
    for (const table of allTables) {
      if (table.TABLE_NAME.includes('video') || table.TABLE_NAME.includes('transfer') || table.TABLE_NAME.includes('xiaohongshu')) {
        try {
          const [count] = await pool.execute(`SELECT COUNT(*) as count FROM ${table.TABLE_NAME}`);
          tableStats[table.TABLE_NAME] = count[0].count;
        } catch (error) {
          tableStats[table.TABLE_NAME] = `查询失败: ${error.message}`;
        }
      }
    }

    console.log('📊 [调试] 数据库表统计:', tableStats);

    res.json({
      success: true,
      data: {
        allTables,
        videoTables,
        tableStats
      }
    });

  } catch (error) {
    console.error('❌ [调试] 查看数据库表结构失败:', error);
    res.status(500).json({
      success: false,
      message: '查看数据库表结构失败: ' + error.message
    });
  }
});

// 生成模拟传输记录
function generateMockTransferRecords(videoId) {
  const mockRecords = [];

  // 根据videoId生成不同的数据
  const seed = parseInt(videoId) || 1;
  const deviceCount = (seed % 3) + 1; // 1-3个设备

  const deviceNames = [
    `小米手机_${seed}`,
    `华为手机_${seed + 1}`,
    `OPPO手机_${seed + 2}`,
    `vivo手机_${seed + 3}`,
    `三星手机_${seed + 4}`
  ];

  const deviceIPs = [
    `192.168.1.${100 + seed}`,
    `192.168.1.${101 + seed}`,
    `192.168.1.${102 + seed}`,
    `192.168.1.${103 + seed}`,
    `192.168.1.${104 + seed}`
  ];

  // 根据videoId生成不同的状态组合
  const statusOptions = [
    ['completed', 'completed', 'completed'],
    ['completed', 'failed', 'completed'],
    ['completed', 'completed', 'failed'],
    ['failed', 'completed', 'completed'],
    ['completed', 'transferring', 'completed']
  ];
  const statuses = statusOptions[seed % statusOptions.length];

  for (let i = 0; i < deviceCount; i++) {
    mockRecords.push({
      id: seed * 10 + i + 1,
      video_id: videoId,
      device_id: `device_${seed}_${i + 1}`,
      device_name: deviceNames[i % deviceNames.length],
      device_ip: deviceIPs[i % deviceIPs.length],
      transfer_status: statuses[i % statuses.length],
      transfer_time: new Date(Date.now() - (seed + i + 1) * 12 * 60 * 60 * 1000).toISOString(), // 不同的时间
      error_message: statuses[i % statuses.length] === 'failed' ?
        ['网络连接超时', '设备存储空间不足', '传输中断'][i % 3] : null
    });
  }

  console.log(`📊 为视频 ${videoId} 生成了 ${mockRecords.length} 条传输记录:`, mockRecords);
  return mockRecords;
}

// 生成模拟视频统计
function generateMockVideoStats(videoIds) {
  return videoIds.map((videoId, index) => ({
    id: videoId,
    original_name: `测试视频_${videoId}.mp4`,
    file_size: Math.floor(Math.random() * 100000000) + 10000000, // 10MB-110MB
    video_duration: Math.floor(Math.random() * 300) + 30, // 30-330秒
    created_at: new Date(Date.now() - (index + 1) * 24 * 60 * 60 * 1000).toISOString(),
    transfer_count: Math.floor(Math.random() * 5) + 1,
    device_count: Math.floor(Math.random() * 3) + 1,
    success_rate: Math.floor(Math.random() * 20) + 80, // 80-100%
    last_transfer_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
  }));
}

// 记录脚本执行时的视频传输
async function recordVideoTransfersForScript(selectedVideos, deviceId, taskId, status = 'completed') {
  try {
    console.log('📝📝📝 [脚本传输记录] ===== 开始记录脚本执行的视频传输 ===== 📝📝📝');
    console.log('📝 [脚本传输记录] 函数被调用时间:', new Date().toLocaleString());
    console.log('📝 [脚本传输记录] 设备ID:', deviceId);
    console.log('📝 [脚本传输记录] 任务ID:', taskId);
    console.log('📝 [脚本传输记录] 传输状态:', status);
    console.log('📝 [脚本传输记录] 选中的视频:', selectedVideos);
    console.log('📝 [脚本传输记录] 视频数量:', selectedVideos ? selectedVideos.length : 0);

    if (!selectedVideos || selectedVideos.length === 0) {
      console.log('📝 [脚本传输记录] 没有选中的视频，跳过记录');
      return;
    }

    if (!pool) {
      console.log('❌ [脚本传输记录] 数据库连接不可用');
      return;
    }

    // 检查是否已存在相同任务ID的记录，避免重复创建
    console.log('🔍 [脚本传输记录] 检查是否已存在相同任务ID的记录...');
    const [existingRecords] = await pool.execute(`
      SELECT COUNT(*) as count FROM xiaohongshu_video_transfers
      WHERE task_id = ? AND device_id = ?
    `, [taskId, deviceId]);

    if (existingRecords[0].count > 0) {
      console.log('⚠️ [脚本传输记录] 任务ID已存在记录，跳过重复创建:', taskId);
      return;
    }

    // 获取设备信息
    let deviceName = deviceId;
    try {
      const [devices] = await pool.execute(`
        SELECT device_name FROM devices WHERE device_id = ?
      `, [deviceId]);

      if (devices.length > 0) {
        deviceName = devices[0].device_name;
      }
    } catch (error) {
      console.log('⚠️ [脚本传输记录] 获取设备名称失败，使用设备ID:', error.message);
    }

    // 为每个视频创建传输记录
    for (const video of selectedVideos) {
      try {
        console.log(`📝 [脚本传输记录] 记录视频 ${video.id} (${video.original_name}) 传输到设备 ${deviceId}`);

        const completedTime = status === 'completed' ? 'NOW()' : 'NULL';

        await pool.execute(`
          INSERT INTO xiaohongshu_video_transfers
          (video_id, device_id, device_name, transfer_type, task_id, status, file_size, video_filename, transfer_time, completed_time)
          VALUES (?, ?, ?, 'script_execution', ?, ?, ?, ?, NOW(), ${completedTime})
        `, [video.id, deviceId, deviceName, taskId, status, video.file_size || 0, video.original_name || '']);

        console.log(`✅ [脚本传输记录] 已记录视频 ${video.id} 传输到设备 ${deviceId}`);
      } catch (error) {
        console.error(`❌ [脚本传输记录] 记录视频 ${video.id} 传输失败:`, error.message);
      }
    }

    console.log('✅ [脚本传输记录] 所有视频传输记录完成');
  } catch (error) {
    console.error('❌ [脚本传输记录] 记录视频传输失败:', error);
  }
}

// 将函数设置为全局函数，供其他模块调用
global.recordVideoTransfersForScript = recordVideoTransfersForScript;

// 更新视频传输状态
async function updateVideoTransferStatus(logId, deviceId, finalStatus, result) {
  try {
    console.log('📝 [传输状态更新] 开始更新视频传输状态');
    console.log('📝 [传输状态更新] 日志ID:', logId);
    console.log('📝 [传输状态更新] 设备ID:', deviceId);
    console.log('📝 [传输状态更新] 最终状态:', finalStatus);
    console.log('📝 [传输状态更新] 执行结果:', result);

    if (!pool) {
      console.log('❌ [传输状态更新] 数据库连接不可用');
      return;
    }

    // 从logId中提取taskId
    const taskId = logId.replace('xiaohongshu_', '').replace('_' + deviceId, '');
    console.log('📝 [传输状态更新] 提取的任务ID:', taskId);

    // 根据执行结果更新传输状态
    const transferStatus = finalStatus === 'completed' ? 'completed' : 'failed';
    const errorMessage = finalStatus === 'failed' ? (result || '脚本执行失败') : null;

    console.log('📝 [传输状态更新] 传输状态:', transferStatus);
    console.log('📝 [传输状态更新] 错误信息:', errorMessage);

    // 更新该任务相关的所有传输记录
    const updateQuery = `
      UPDATE xiaohongshu_video_transfers
      SET status = ?,
          error_message = ?,
          updated_at = NOW()
      WHERE task_id = ? AND device_id = ? AND transfer_type = 'script_execution'
    `;

    const [updateResult] = await pool.execute(updateQuery, [
      transferStatus,
      errorMessage,
      taskId,
      deviceId
    ]);

    console.log('📝 [传输状态更新] 更新结果:', updateResult);
    console.log('📝 [传输状态更新] 影响的行数:', updateResult.affectedRows);

    if (updateResult.affectedRows > 0) {
      console.log(`✅ [传输状态更新] 已更新 ${updateResult.affectedRows} 条传输记录状态为 ${transferStatus}`);
    } else {
      console.log('⚠️ [传输状态更新] 没有找到匹配的传输记录');
    }

  } catch (error) {
    console.error('❌ [传输状态更新] 更新视频传输状态失败:', error);
  }
}

// 获取多个视频的传输汇总信息
app.post('/api/videos/transfer-summary', authenticateToken, async (req, res) => {
  try {
    const { videoIds } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未登录'
      });
    }

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的视频ID列表'
      });
    }

    // 获取视频基本信息和传输统计
    let videoStats = [];

    if (pool) {
      try {
        // 检查video_transfer_logs表是否存在
        const [tables] = await pool.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND TABLE_NAME = 'video_transfer_logs'
        `);

        if (tables.length > 0) {
          // 表存在，查询真实数据
          const placeholders = videoIds.map(() => '?').join(',');
          const [realVideoStats] = await pool.execute(`
            SELECT
              v.id,
              v.original_name,
              v.file_size,
              v.video_duration,
              v.created_at,
              COUNT(vtl.id) as transfer_count,
              COUNT(DISTINCT vtl.device_id) as device_count,
              ROUND(
                (COUNT(CASE WHEN vtl.transfer_status = 'completed' THEN 1 END) * 100.0 /
                 NULLIF(COUNT(vtl.id), 0)), 0
              ) as success_rate,
              MAX(vtl.transfer_time) as last_transfer_time
            FROM videos v
            LEFT JOIN video_transfer_logs vtl ON v.id = vtl.video_id AND vtl.user_id = ?
            WHERE v.id IN (${placeholders}) AND v.user_id = ?
            GROUP BY v.id
            ORDER BY v.created_at DESC
          `, [userId, ...videoIds, userId]);
          videoStats = realVideoStats;
        } else {
          // 表不存在，从videos表获取基本信息并生成模拟统计
          const placeholders = videoIds.map(() => '?').join(',');
          const [basicVideoInfo] = await pool.execute(`
            SELECT
              v.id,
              v.original_name,
              v.file_size,
              v.video_duration,
              v.created_at
            FROM videos v
            WHERE v.id IN (${placeholders}) AND v.user_id = ?
            ORDER BY v.created_at DESC
          `, [...videoIds, userId]);

          // 为每个视频生成模拟统计
          videoStats = basicVideoInfo.map(video => ({
            ...video,
            transfer_count: Math.floor(Math.random() * 5) + 1,
            device_count: Math.floor(Math.random() * 3) + 1,
            success_rate: Math.floor(Math.random() * 20) + 80, // 80-100%
            last_transfer_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
          }));
        }
      } catch (error) {
        console.log('查询视频统计失败，生成模拟数据:', error.message);
        videoStats = generateMockVideoStats(videoIds);
      }
    } else {
      // 数据库不可用，生成模拟数据
      videoStats = generateMockVideoStats(videoIds);
    }

    // 计算总体统计
    const totalVideos = videoStats.length;
    const totalTransfers = videoStats.reduce((sum, v) => sum + (v.transfer_count || 0), 0);

    // 获取所有涉及的设备数量
    let totalUniqueDevices = 0;
    if (pool) {
      try {
        const [tables] = await pool.execute(`
          SELECT TABLE_NAME
          FROM INFORMATION_SCHEMA.TABLES
          WHERE TABLE_SCHEMA = 'autojs_control'
          AND TABLE_NAME = 'video_transfer_logs'
        `);

        if (tables.length > 0) {
          const placeholders = videoIds.map(() => '?').join(',');
          const [deviceStats] = await pool.execute(`
            SELECT DISTINCT device_id
            FROM video_transfer_logs
            WHERE video_id IN (${placeholders}) AND user_id = ?
          `, [...videoIds, userId]);
          totalUniqueDevices = deviceStats.length;
        } else {
          // 表不存在，使用模拟数据
          totalUniqueDevices = Math.min(videoStats.reduce((max, v) => Math.max(max, v.device_count || 0), 0), 3);
        }
      } catch (error) {
        console.log('查询设备统计失败，使用模拟数据:', error.message);
        totalUniqueDevices = Math.min(videoStats.reduce((max, v) => Math.max(max, v.device_count || 0), 0), 3);
      }
    } else {
      totalUniqueDevices = Math.min(videoStats.reduce((max, v) => Math.max(max, v.device_count || 0), 0), 3);
    }

    const avgSuccessRate = videoStats.length > 0 ?
      Math.round(videoStats.reduce((sum, v) => sum + (v.success_rate || 0), 0) / videoStats.length) : 0;

    res.json({
      success: true,
      videos: videoStats,
      statistics: {
        totalVideos,
        totalTransfers,
        uniqueDevices: totalUniqueDevices,
        successRate: avgSuccessRate
      }
    });

  } catch (error) {
    console.error('获取视频传输汇总信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取传输汇总信息失败: ' + error.message
    });
  }
});

// 闲鱼自动化API路由

// 接收闲鱼脚本实时状态上报API
app.post('/api/xianyu/realtime-status', (req, res) => {
  try {
    const statusData = req.body;
    console.log('收到闲鱼脚本实时状态上报:', statusData);

    // 验证必要字段
    if (!statusData.taskId || !statusData.deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的taskId或deviceId'
      });
    }

    // 通过WebSocket广播实时状态给前端
    console.log('广播闲鱼实时状态到前端:', statusData);
    io.emit('xianyu_realtime_status', statusData);

    res.json({
      success: true,
      message: '实时状态上报成功',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('处理闲鱼实时状态上报失败:', error);
    res.status(500).json({
      success: false,
      message: '处理实时状态失败: ' + error.message
    });
  }
});

// 接收闲鱼脚本执行完成通知API
app.post('/api/xianyu/execution-completed', (req, res) => {
  try {
    const resultData = req.body;
    console.log('收到闲鱼脚本执行完成通知:', resultData);

    // 验证必要字段
    if (!resultData.taskId || !resultData.deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少必要的taskId或deviceId'
      });
    }

    // 通过WebSocket通知前端执行完成
    console.log('广播闲鱼执行完成通知到前端:', resultData);
    io.emit('xianyu_execution_completed', resultData);

    res.json({
      success: true,
      message: '执行完成通知处理成功',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('处理闲鱼执行完成通知失败:', error);
    res.status(500).json({
      success: false,
      message: '处理执行完成通知失败: ' + error.message
    });
  }
});

// 辅助函数：根据设备ID查找设备Socket
function findDeviceSocket(deviceId) {
  for (const [socketId, device] of devices) {
    if (device.deviceId === deviceId) {
      return io.sockets.sockets.get(socketId);
    }
  }
  return null;
}

// 清空闲鱼执行日志
app.delete('/api/xianyu/logs', authenticateToken, async (req, res) => {
  try {
    if (!xianyuLogService) {
      return res.status(503).json({
        success: false,
        message: '日志服务不可用（数据库未配置）'
      });
    }

    const deletedCount = await xianyuLogService.clearAllExecutionLogs();

    res.json({
      success: true,
      message: `已清空所有闲鱼执行日志，删除了 ${deletedCount} 条记录`,
      data: { deletedCount }
    });
  } catch (error) {
    console.error('清空闲鱼执行日志失败:', error);
    res.status(500).json({
      success: false,
      message: '清空执行日志失败: ' + error.message
    });
  }
});

// 终止所有闲鱼任务
app.post('/api/xianyu/stop-all', authenticateToken, async (req, res) => {
  try {
    console.log('收到终止所有闲鱼任务请求');

    let stoppedCount = 0;
    let updatedCount = 0;

    // 1. 更新数据库中所有正在执行的任务状态
    if (xianyuLogService) {
      try {
        updatedCount = await xianyuLogService.updateAllRunningTasksToStopped('用户手动终止所有任务');
        console.log(`数据库中已更新 ${updatedCount} 个正在执行的闲鱼任务状态`);
      } catch (dbError) {
        console.error('更新数据库任务状态失败:', dbError);
      }
    }

    // 2. 向所有在线设备发送停止命令
    for (const [socketId, device] of devices) {
      if (device.status === 'online' || device.status === 'busy') {
        try {
          // 发送停止命令
          const deviceSocket = io.sockets.sockets.get(socketId);
          if (deviceSocket) {
            deviceSocket.emit('script_command', {
              type: 'stop_all_scripts',
              reason: '用户手动终止所有任务'
            });
            stoppedCount++;
            console.log(`已向设备 ${device.deviceName} (${device.deviceId}) 发送停止命令`);
          }

          // 恢复设备状态为在线
          await updateDeviceStatus(device.deviceId, 'online');
        } catch (error) {
          console.error(`向设备 ${device.deviceId} 发送停止命令失败:`, error);
        }
      }
    }

    // 3. 通知前端所有任务已停止
    io.emit('xianyu_all_tasks_stopped', {
      stoppedDevices: stoppedCount,
      updatedTasks: updatedCount,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: `已终止所有闲鱼任务，向 ${stoppedCount} 个设备发送停止命令，更新了 ${updatedCount} 个数据库记录`,
      data: {
        stoppedDevices: stoppedCount,
        updatedTasks: updatedCount
      }
    });

  } catch (error) {
    console.error('终止所有闲鱼任务失败:', error);
    res.status(500).json({
      success: false,
      message: '终止失败: ' + error.message
    });
  }
});

// 测试路由
app.get('/api/xianyu/test', (req, res) => {
  res.json({
    success: true,
    message: '闲鱼自动化API正常工作',
    timestamp: new Date().toISOString(),
    routes: [
      'GET /api/xianyu/test',
      'POST /api/xianyu/execute',
      'POST /api/xianyu/stop',
      'GET /api/xianyu/logs'
    ]
  });
});

// 执行闲鱼自动化任务
app.post('/api/xianyu/execute', async (req, res) => {
  try {
    const { functionType, deviceConfigs, deviceIds } = req.body;

    console.log('收到闲鱼自动化执行请求:', {
      function: functionType,
      devices: deviceIds ? deviceIds.length : 0,
      deviceConfigs: deviceConfigs ? Object.keys(deviceConfigs).length : 0
    });

    // 验证参数
    if (!functionType || !deviceIds || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数'
      });
    }

    // 根据功能类型选择对应的脚本文件
    const scriptMap = {
      'keywordMessage': './xy-jb/闲鱼关键词私信-无ui界面.js'
    };

    const scriptPath = scriptMap[functionType];
    if (!scriptPath) {
      return res.status(400).json({
        success: false,
        message: '不支持的功能类型: ' + functionType
      });
    }

    // 检查脚本文件是否存在
    if (!fs.existsSync(scriptPath)) {
      console.error('闲鱼脚本文件不存在:', scriptPath);
      return res.status(500).json({
        success: false,
        message: '脚本文件不存在: ' + scriptPath
      });
    }

    console.log('使用闲鱼脚本文件:', scriptPath);

    // 为每个设备执行任务
    const results = [];
    for (const deviceId of deviceIds) {
      try {
        const config = deviceConfigs[deviceId] || {};
        console.log(`开始为设备 ${deviceId} 执行闲鱼自动化任务`);
        console.log('设备配置:', config);
        console.log('选择的应用:', config.selectedApp, '类型:', typeof config.selectedApp);

        // 创建任务ID
        const taskId = Date.now().toString() + '_' + deviceId;

        // 读取脚本内容
        let scriptContent = fs.readFileSync(scriptPath, 'utf8');

        // 获取应用选择信息
        let appCoordinates = null;
        let appPackageName = null;
        let appLaunchMethod = 'coordinate'; // 默认启动方法

        if (config.selectedApp) {
          console.log('查找设备应用信息:', deviceId, config.selectedApp);

          // 从数据库获取设备应用信息
          try {
            if (pool) {
              console.log('查询参数:', { deviceId, selectedApp: config.selectedApp });

              // 先查询该设备的所有闲鱼应用
              const [allApps] = await pool.execute(`
                SELECT app_name, app_text, app_bounds, is_clickable, detection_method
                FROM device_apps
                WHERE device_id = ? AND app_type = 'xianyu'
                ORDER BY detected_at DESC
              `, [deviceId]);

              console.log(`设备 ${deviceId} 的所有闲鱼应用:`, allApps);

              const [rows] = await pool.execute(`
                SELECT app_name, app_text, app_bounds, is_clickable, detection_method
                FROM device_apps
                WHERE device_id = ? AND app_type = 'xianyu' AND app_text = ?
                ORDER BY detected_at DESC
                LIMIT 1
              `, [deviceId, config.selectedApp]);

              if (rows.length > 0) {
                const appInfo = rows[0];
                appPackageName = appInfo.app_name;
                appCoordinates = appInfo.app_bounds ? JSON.parse(appInfo.app_bounds) : null;
                appLaunchMethod = appInfo.detection_method === 'keyword' || appInfo.detection_method === 'regex' ? 'text' : 'coordinate';

                console.log('从数据库获取到闲鱼应用信息:', {
                  selectedApp: config.selectedApp,
                  packageName: appPackageName,
                  coordinates: appCoordinates,
                  launchMethod: appLaunchMethod,
                  detectionMethod: appInfo.detection_method
                });
              } else {
                console.log('数据库中未找到匹配的应用信息，使用默认配置');
                console.log('查询条件:', { deviceId, app_type: 'xianyu', app_text: config.selectedApp });
                appPackageName = 'com.taobao.idlefish';
                appCoordinates = { x: 270, y: 1350 };
              }
            }
          } catch (dbError) {
            console.error('查询数据库应用信息失败:', dbError);
            // 使用默认配置
            appPackageName = 'com.taobao.idlefish';
            appCoordinates = { x: 270, y: 1350 };
          }
        }

        // 注入配置参数到脚本中（覆盖现有配置，避免重复定义）
        const configInjection = `
// 从服务器注入的配置参数（覆盖脚本中的默认配置）
if (typeof config !== 'undefined') {
    config.targetCount = ${config.targetCount || 10};
    config.keyword = "${config.keyword || ''}";
    config.message = "${config.message || ''}";
    config.chattedCount = 0;
    config.dataFile = "data/chatted_posts.json";
} else {
    var config = {
        targetCount: ${config.targetCount || 10},
        keyword: "${config.keyword || ''}",
        message: "${config.message || ''}",
        chattedCount: 0,
        dataFile: "data/chatted_posts.json"
    };
}

// 覆盖调试模式设置
if (typeof DEBUG_MODE !== 'undefined') {
    DEBUG_MODE = ${config.debugMode !== false};
} else {
    var DEBUG_MODE = ${config.debugMode !== false};
}

// 全局配置信息（包含应用选择信息）
var globalConfig = {
    taskId: "${taskId}",
    deviceId: "${deviceId}",
    selectedApp: "${config.selectedApp || ''}",
    appCoordinates: ${appCoordinates ? JSON.stringify(appCoordinates) : 'null'},
    appPackageName: "${appPackageName || 'com.taobao.idlefish'}",
    appLaunchMethod: "${appLaunchMethod || 'coordinate'}"
};
`;

        // 将配置注入到脚本开头
        scriptContent = configInjection + '\n' + scriptContent;

        console.log(`设备 ${deviceId} 闲鱼脚本配置注入完成:`, {
          selectedApp: config.selectedApp,
          appCoordinates: appCoordinates,
          appPackageName: appPackageName
        });

        // 创建任务记录
        const task = {
          id: taskId,
          deviceId: deviceId,
          functionType: functionType,
          status: 'running',
          startTime: new Date(),
          config: config,
          scriptPath: scriptPath
        };

        xianyuActiveTasks.set(taskId, task);

        // 创建数据库执行日志记录
        if (xianyuLogService) {
          try {
            // 获取设备名称
            let deviceName = deviceId;
            for (const [socketId, device] of devices) {
              if (device.deviceId === deviceId) {
                deviceName = device.deviceName || device.deviceId;
                break;
              }
            }

            console.log(`创建闲鱼执行日志: ${taskId}, 设备: ${deviceName}`);
            await xianyuLogService.createExecutionLog(
              taskId,
              functionType,
              deviceId,
              deviceName,
              config
            );
            console.log(`✅ 闲鱼执行日志已创建: ${taskId}`);

            // 立即更新状态为运行中
            try {
              await xianyuLogService.updateExecutionStatus(
                taskId,
                'running',
                10,
                '脚本已下发',
                '脚本已发送到设备，开始执行'
              );
              console.log(`✅ 闲鱼执行状态已更新为运行中: ${taskId}`);
            } catch (statusError) {
              console.error('❌ 更新闲鱼执行状态失败:', statusError);
            }
          } catch (logError) {
            console.error('❌ 创建闲鱼执行日志失败:', logError);
          }
        } else {
          console.warn('⚠️ xianyuLogService 不可用，无法创建执行日志');
        }

        // 发送脚本到设备
        const scriptData = {
          type: 'execute_script',
          taskId: taskId,
          script: scriptContent,
          config: config,
          functionType: functionType
        };

        // 查找设备连接方式
        const deviceSocket = findDeviceSocket(deviceId);
        let deviceData = null;

        // 查找设备数据
        for (const [socketId, device] of devices) {
          if (device.deviceId === deviceId) {
            deviceData = device;
            break;
          }
        }

        if (deviceSocket) {
          // WebSocket连接的设备
          deviceSocket.emit('script_command', scriptData);
          console.log(`已通过WebSocket发送闲鱼自动化脚本到设备 ${deviceId}`);

          // 更新设备状态为忙碌
          await updateDeviceStatus(deviceId, 'busy');

          results.push({
            deviceId: deviceId,
            taskId: taskId,
            status: 'sent',
            message: '脚本已通过WebSocket发送到设备'
          });
        } else if (deviceData) {
          // HTTP连接的设备，使用pendingCommands队列
          console.log(`设备 ${deviceId} 通过HTTP连接，使用HTTP方式发送闲鱼任务`);

          // 更新设备状态为忙碌
          await updateDeviceStatus(deviceId, 'busy');

          // 添加到待执行队列
          if (!pendingCommands.has(deviceId)) {
            pendingCommands.set(deviceId, []);
          }

          // 为了兼容手机端期望的格式，存储script和logId
          pendingCommands.get(deviceId).push({
            logId: taskId,
            script: scriptContent,
            taskId: taskId,
            functionType: functionType,
            config: config
          });

          console.log(`已将闲鱼自动化脚本添加到设备 ${deviceId} 的待执行队列`);

          results.push({
            deviceId: deviceId,
            taskId: taskId,
            status: 'sent',
            message: '脚本已添加到HTTP设备队列'
          });
        } else {
          console.error(`设备 ${deviceId} 未连接`);
          results.push({
            deviceId: deviceId,
            taskId: taskId,
            status: 'error',
            message: '设备未连接'
          });
        }

        // 通知前端任务开始
        io.emit('xianyu_task_started', {
          taskId: taskId,
          deviceId: deviceId,
          functionType: functionType,
          config: config
        });

      } catch (error) {
        console.error(`设备 ${deviceId} 执行失败:`, error);
        results.push({
          deviceId: deviceId,
          status: 'error',
          message: error.message
        });
      }
    }

    // 获取第一个任务的taskId作为主要taskId返回
    const mainTaskId = results.length > 0 ? results[0].taskId : null;

    res.json({
      success: true,
      message: '闲鱼自动化任务已启动',
      taskId: mainTaskId, // 添加taskId到响应根级别
      data: {
        functionType: functionType,
        taskId: mainTaskId, // 也在data中包含taskId
        results: results,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('执行闲鱼自动化任务失败:', error);
    res.status(500).json({
      success: false,
      message: '执行失败: ' + error.message
    });
  }
});

// 停止闲鱼自动化任务
app.post('/api/xianyu/stop', async (req, res) => {
  try {
    const { deviceId, taskId, logId } = req.body;

    console.log('收到停止闲鱼自动化任务请求:', { deviceId, taskId, logId });

    // 如果传递了logId但没有taskId，将logId作为taskId使用
    let actualTaskId = taskId;
    if (!actualTaskId && logId) {
      actualTaskId = logId;
      console.log(`使用logId作为taskId: ${actualTaskId}`);
    }

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID'
      });
    }

    // 查找设备连接方式并发送停止命令
    const deviceSocket = findDeviceSocket(deviceId);
    let deviceData = null;

    // 查找设备数据
    for (const [socketId, device] of devices) {
      if (device.deviceId === deviceId) {
        deviceData = device;
        break;
      }
    }

    if (deviceSocket) {
      // WebSocket连接的设备
      console.log(`向WebSocket设备发送停止命令: ${deviceId}`);
      deviceSocket.emit('script_command', {
        type: 'stop_script',
        taskId: actualTaskId
      });
    } else if (deviceData) {
      // HTTP连接的设备，添加停止命令到队列
      console.log(`设备 ${deviceId} 通过HTTP连接，添加停止命令到队列`);

      if (!pendingCommands.has(deviceId)) {
        pendingCommands.set(deviceId, []);
      }

      // 添加停止命令到队列
      pendingCommands.get(deviceId).push({
        script: 'STOP_SCRIPT_COMMAND',
        type: 'stop_script',
        taskId: actualTaskId,
        timestamp: new Date()
      });

      console.log(`已将停止命令添加到设备 ${deviceId} 的队列`);
    } else {
      console.log(`设备 ${deviceId} 未连接，无法发送停止命令`);
    }

    // 更新任务状态
    if (actualTaskId && xianyuActiveTasks.has(actualTaskId)) {
      const task = xianyuActiveTasks.get(actualTaskId);
      task.status = 'stopped';
      task.endTime = new Date();
      xianyuActiveTasks.delete(actualTaskId);
      xianyuTaskHistory.push(task);
    }

    // 更新数据库中的执行日志状态
    if (xianyuLogService) {
      try {
        if (actualTaskId) {
          // 有明确的taskId，直接更新
          console.log(`更新数据库执行日志状态: ${actualTaskId} -> stopped`);
          await xianyuLogService.updateExecutionStatus(
            actualTaskId,
            'stopped',  // 执行状态改为已停止
            0,          // 进度重置为0
            '已停止',   // 阶段描述
            '用户手动停止任务'  // 消息
          );
          console.log(`✅ 数据库执行日志已更新: ${actualTaskId} -> stopped (0%)`);
        } else {
          // 没有taskId，尝试查找该设备正在执行的任务
          console.log(`没有taskId，查找设备 ${deviceId} 正在执行的任务`);

          // 查找该设备最近的执行中任务
          const runningLogs = await xianyuLogService.getExecutionLogs(
            1, // page
            1, // limit
            {  // filters
              deviceId: deviceId,
              executionStatus: 'running'
            }
          );

          if (runningLogs && runningLogs.logs && runningLogs.logs.length > 0) {
            const runningLog = runningLogs.logs[0];
            console.log(`找到正在执行的任务: ${runningLog.id}`);

            await xianyuLogService.updateExecutionStatus(
              runningLog.id,
              'stopped',  // 执行状态改为已停止
              0,          // 进度重置为0
              '已停止',   // 阶段描述
              '用户手动停止任务'  // 消息
            );
            console.log(`✅ 数据库执行日志已更新: ${runningLog.id} -> stopped (0%)`);
          } else {
            console.log(`⚠️ 未找到设备 ${deviceId} 正在执行的任务`);
          }
        }
      } catch (logError) {
        console.error('❌ 更新数据库执行日志失败:', logError);
      }
    } else {
      console.warn('⚠️ xianyuLogService 不可用，无法更新数据库执行日志');
    }

    // 同时清理小红书执行日志中的相关任务（防止设备注册时被误判为忙碌）
    if (pool) {
      try {
        console.log(`🧹 清理小红书执行日志中的相关任务: ${deviceId}`);
        const [result] = await pool.execute(`
          UPDATE xiaohongshu_execution_logs 
          SET execution_status = 'stopped', 
              progress_percentage = 0, 
              status_message = '已停止',
              execution_logs = CONCAT(COALESCE(execution_logs, ''), '\n用户手动停止闲鱼任务，同步清理小红书任务状态')
          WHERE device_id = ? AND execution_status IN ('running', 'pending')
        `, [deviceId]);
        
        if (result.affectedRows > 0) {
          console.log(`✅ 已清理 ${result.affectedRows} 个小红书执行日志任务`);
        } else {
          console.log(`ℹ️ 没有需要清理的小红书执行日志任务`);
        }
      } catch (cleanupError) {
        console.error('❌ 清理小红书执行日志失败:', cleanupError);
      }
    }

    // 延迟清理命令队列，确保设备能接收到停止命令
    setTimeout(() => {
      if (pendingCommands.has(deviceId)) {
        console.log(`🧹 延迟清理设备 ${deviceId} 的命令队列`);
        pendingCommands.delete(deviceId);
        console.log(`✅ 设备 ${deviceId} 的命令队列已清理`);
      }
    }, 10000); // 延迟10秒清理，确保设备有足够时间轮询到停止命令

    // 延迟恢复设备状态为在线，给设备时间处理停止命令
    setTimeout(async () => {
      try {
        console.log(`🔄 延迟恢复设备状态为在线: ${deviceId}`);
        await updateDeviceStatus(deviceId, 'online');
        console.log(`✅ 闲鱼任务停止，设备状态已恢复为在线: ${deviceId}`);
        
        // 发送设备状态更新事件到前端
        io.emit('device_status_update', {
          deviceId: deviceId,
          status: 'online',
          lastSeen: new Date()
        });
        console.log(`📡 已发送设备状态更新事件: ${deviceId} -> online`);
        
      } catch (error) {
        console.error('❌ 恢复设备状态失败:', error);
      }
    }, 5000); // 延迟5秒恢复状态，给设备时间处理停止命令

    // 立即通知前端任务停止（不等待延迟）
    io.emit('xianyu_task_stopped', {
      deviceId: deviceId,
      taskId: actualTaskId,
      functionType: 'keywordMessage', // 添加功能类型
      message: '任务已停止',
      timestamp: new Date().toISOString()
    });

    // 立即发送设备状态更新事件（不等待延迟）
    io.emit('device_status_update', {
      deviceId: deviceId,
      status: 'online',
      lastSeen: new Date()
    });

    res.json({
      success: true,
      message: '停止命令已发送，设备状态已更新为在线',
      data: {
        deviceId: deviceId,
        taskId: actualTaskId
      }
    });

  } catch (error) {
    console.error('停止闲鱼自动化任务失败:', error);
    res.status(500).json({
      success: false,
      message: '停止失败: ' + error.message
    });
  }
});

// 检查设备执行状态
app.get('/api/xianyu/device-status/:deviceId', authenticateToken, async (req, res) => {
  try {
    const { deviceId } = req.params;
    const userId = req.user.id;

    console.log(`检查设备 ${deviceId} 的执行状态`);

    // 1. 检查设备基本状态
    let device = null;
    if (devices.has(deviceId)) {
      const deviceData = devices.get(deviceId);
      device = {
        device_id: deviceId,
        device_name: deviceData.deviceName || `设备_${deviceId}`,
        status: deviceData.status || 'offline',
        last_seen: deviceData.lastSeen || new Date()
      };
    }

    if (!device) {
      return res.status(404).json({
        success: false,
        message: '设备不存在'
      });
    }

    // 2. 检查是否有正在执行的任务（使用数据库）
    let hasRunningTasks = false;
    let runningTasksCount = 0;

    if (xianyuLogService) {
      try {
        const runningLogs = await xianyuLogService.getExecutionLogs(1, 10, {
          userId,
          deviceId,
          executionStatus: 'running'
        });
        hasRunningTasks = runningLogs.logs && runningLogs.logs.length > 0;
        runningTasksCount = runningLogs.total || 0;
      } catch (error) {
        console.warn('查询数据库执行日志失败，使用内存数据:', error);
      }
    }

    // 3. 检查内存中的活动任务
    let hasActiveTask = false;
    for (const [taskId, task] of xianyuActiveTasks) {
      if (task.deviceId === deviceId && task.status === 'running') {
        hasActiveTask = true;
        break;
      }
    }

    // 如果数据库查询失败，使用内存数据作为备选
    if (!hasRunningTasks && !xianyuLogService) {
      hasRunningTasks = hasActiveTask;
      runningTasksCount = hasActiveTask ? 1 : 0;
    }

    const executionStatus = {
      device: device,
      hasRunningTasks: hasRunningTasks,
      runningTasksCount: runningTasksCount,
      hasActiveTask: hasActiveTask,
      isActuallyRunning: hasRunningTasks || hasActiveTask
    };

    console.log(`设备 ${deviceId} 执行状态检查结果:`, executionStatus);

    res.json({
      success: true,
      data: executionStatus
    });

  } catch (error) {
    console.error('检查设备执行状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查设备执行状态失败: ' + error.message
    });
  }
});

// 获取闲鱼执行日志
app.get('/api/xianyu/logs', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      functionType,
      deviceId,
      executionStatus,
      startDate,
      endDate
    } = req.query;

    console.log('获取闲鱼执行日志请求:', req.query);

    // 使用数据库查询闲鱼执行日志
    if (xianyuLogService) {
      try {
        const filters = {};
        if (functionType) filters.functionType = functionType;
        if (deviceId) filters.deviceId = deviceId;
        if (executionStatus) filters.executionStatus = executionStatus;
        if (startDate) filters.startDate = startDate;
        if (endDate) filters.endDate = endDate;

        const result = await xianyuLogService.getExecutionLogs(
          parseInt(page),
          parseInt(limit),
          filters
        );

        console.log(`✅ 闲鱼执行日志查询成功: ${result.total} 条记录`);

        res.json({
          success: true,
          data: result
        });
        return;
      } catch (error) {
        console.error('❌ 数据库查询闲鱼执行日志失败:', error);
        // 如果数据库查询失败，继续使用内存数据作为备选
      }
    }

    // 备选方案：使用内存中的任务数据（当数据库不可用时）
    console.log('⚠️ 使用内存数据作为闲鱼执行日志备选方案');
    let logs = [
      ...Array.from(xianyuActiveTasks.values()).map(task => ({
        id: task.id,
        functionType: task.functionType,
        deviceInfo: {
          id: task.deviceId,
          name: `设备_${task.deviceId}`,
          ip: '*************'
        },
        executionStatus: task.status,
        progress: task.status === 'running' ? 50 : (task.status === 'completed' ? 100 : 0),
        configParams: task.config,
        executionResult: task.result || null,
        executionTime: task.endTime ? Math.floor((task.endTime - task.startTime) / 1000) : 0,
        createdAt: task.startTime,
        debugLogs: task.debugLogs || []
      })),
      ...xianyuTaskHistory.map(task => ({
        id: task.id,
        functionType: task.functionType,
        deviceInfo: {
          id: task.deviceId,
          name: `设备_${task.deviceId}`,
          ip: '*************'
        },
        executionStatus: task.status,
        progress: task.status === 'completed' ? 100 : 0,
        configParams: task.config,
        executionResult: task.result || null,
        executionTime: task.endTime ? Math.floor((task.endTime - task.startTime) / 1000) : 0,
        createdAt: task.startTime,
        debugLogs: task.debugLogs || []
      }))
    ];

    // 应用筛选条件
    if (functionType) {
      logs = logs.filter(log => log.functionType === functionType);
    }
    if (deviceId) {
      logs = logs.filter(log => log.deviceInfo.id === deviceId);
    }
    if (executionStatus) {
      logs = logs.filter(log => log.executionStatus === executionStatus);
    }
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      logs = logs.filter(log => {
        const logDate = new Date(log.createdAt);
        return logDate >= start && logDate <= end;
      });
    }

    // 排序（最新的在前）
    logs.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // 分页
    const total = logs.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedLogs = logs.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        logs: paginatedLogs,
        total: total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('获取闲鱼执行日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取日志失败: ' + error.message
    });
  }
});

// HTTP设备主动断开连接API
app.post('/api/device/:deviceId/disconnect', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;
  console.log(`收到设备主动断开请求: ${deviceId}`);

  // 尝试更新数据库状态（如果可用）
  if (pool) {
    try {
      await pool.execute(`
        UPDATE devices SET status = 'offline', last_seen = NOW()
        WHERE device_id = ?
      `, [deviceId]);

      console.log('✅ 数据库状态已更新为离线:', deviceId);

      // 验证数据库更新是否成功
      const [rows] = await pool.execute('SELECT status FROM devices WHERE device_id = ?', [deviceId]);
      if (rows.length > 0) {
        console.log('📊 数据库中当前状态:', rows[0].status);
      }
    } catch (dbError) {
      console.error('更新设备离线状态失败:', dbError);
    }
  }

  // 查找并移除设备
  let deviceToRemove = null;
  let socketIdToRemove = null;

  console.log(`查找设备进行断开: ${deviceId}`);
  console.log(`当前内存中的设备数量: ${devices.size}`);

  for (const [socketId, device] of devices) {
    console.log(`检查设备: socketId=${socketId}, deviceId=${device.deviceId}, deviceName=${device.deviceName}`);
    if (device.deviceId === deviceId) {
      deviceToRemove = device;
      socketIdToRemove = socketId;
      console.log(`找到匹配的设备: ${device.deviceName} (${device.deviceId})`);
      break;
    }
  }

  if (!deviceToRemove) {
    console.log(`⚠️ 未找到设备 ${deviceId}，可能已经断开或不存在`);
  }

  if (deviceToRemove) {
    // 清理该设备相关的小红书任务
    await cleanupXiaohongshuTasksForDevice(deviceId);

    // 清理该设备相关的闲鱼任务
    await cleanupXianyuTasksForDevice(deviceId);

    // 移除设备
    console.log(`准备删除设备: socketId=${socketIdToRemove}, deviceId=${deviceId}`);
    console.log(`删除前设备数量: ${devices.size}`);

    devices.delete(socketIdToRemove);

    console.log(`删除后设备数量: ${devices.size}`);
    console.log(`验证设备是否已删除: ${devices.has(socketIdToRemove) ? '仍存在' : '已删除'}`);

    // 清理待执行命令
    if (pendingCommands.has(deviceId)) {
      pendingCommands.delete(deviceId);
    }

    console.log(`HTTP设备主动断开: ${deviceToRemove.deviceName} (${deviceId})`);
    console.log(`当前剩余设备数量: ${devices.size}`);

    // 列出剩余的设备
    console.log(`剩余设备列表:`);
    for (const [socketId, device] of devices) {
      console.log(`- socketId: ${socketId}, deviceId: ${device.deviceId}, deviceName: ${device.deviceName}, status: ${device.status}`);
    }

    // 通知所有Web客户端设备离线
    for (const [clientSocketId] of webClients) {
      const clientSocket = io.sockets.sockets.get(clientSocketId);
      if (clientSocket) {
        clientSocket.emit('device_offline', {
          deviceId: deviceId
        });
      }
    }

    // 通知所有Web客户端设备状态变化
    console.log('发送WebSocket通知: device_status_changed');
    const statusChange = {
      type: 'device_disconnected',
      deviceId: deviceId,
      deviceName: deviceToRemove.deviceName,
      timestamp: new Date()
    };

    io.emit('device_status_changed', statusChange);

    // 长轮询相关代码已移除，只使用WebSocket通信

    console.log('WebSocket通知已发送');

    res.json({
      success: true,
      message: '设备已断开连接'
    });
  } else {
    res.status(404).json({
      success: false,
      message: '设备不存在'
    });
  }
});

// 设备应用信息上报API
app.post('/api/device/:deviceId/apps', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;
  const { apps, detectedAt } = req.body;

  console.log(`收到设备应用信息上报: ${deviceId}`);
  console.log('请求体:', JSON.stringify(req.body, null, 2));
  console.log('apps类型:', typeof apps);
  console.log('apps内容:', apps);

  if (!apps) {
    return res.status(400).json({
      success: false,
      message: '缺少应用信息'
    });
  }

  if (typeof apps !== 'object') {
    return res.status(400).json({
      success: false,
      message: '应用信息格式错误：apps必须是对象'
    });
  }

  // 确保数组存在，并提供默认值
  const xiaohongshuApps = Array.isArray(apps.xiaohongshu) ? apps.xiaohongshu : [];
  const xianyuApps = Array.isArray(apps.xianyu) ? apps.xianyu : [];

  console.log(`小红书应用数量: ${xiaohongshuApps.length}`);
  console.log(`闲鱼应用数量: ${xianyuApps.length}`);

  // 检查是否至少有一个应用类型包含数据
  if (xiaohongshuApps.length === 0 && xianyuApps.length === 0) {
    console.log('警告：未检测到任何应用，但仍然保存记录');
  }

  try {
    if (pool) {
      // 先清除该设备的旧应用记录
      await pool.execute('DELETE FROM device_apps WHERE device_id = ?', [deviceId]);

      // 插入小红书应用信息
      if (xiaohongshuApps.length > 0) {
        for (const app of xiaohongshuApps) {
          await pool.execute(`
            INSERT INTO device_apps (device_id, app_type, app_name, app_text, app_bounds, is_clickable, detection_method)
            VALUES (?, 'xiaohongshu', ?, ?, ?, ?, ?)
          `, [deviceId, app.text, app.text, app.bounds, app.clickable, app.method]);
        }
        console.log(`保存了 ${xiaohongshuApps.length} 个小红书应用`);
      }

      // 插入闲鱼应用信息
      if (xianyuApps.length > 0) {
        for (const app of xianyuApps) {
          await pool.execute(`
            INSERT INTO device_apps (device_id, app_type, app_name, app_text, app_bounds, is_clickable, detection_method)
            VALUES (?, 'xianyu', ?, ?, ?, ?, ?)
          `, [deviceId, app.text, app.text, app.bounds, app.clickable, app.method]);
        }
        console.log(`保存了 ${xianyuApps.length} 个闲鱼应用`);
      }

      res.json({
        success: true,
        message: '应用信息保存成功',
        data: {
          xiaohongshu: xiaohongshuApps.length,
          xianyu: xianyuApps.length
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: '数据库不可用'
      });
    }
  } catch (error) {
    console.error('保存应用信息失败:', error);
    res.status(500).json({
      success: false,
      message: '保存应用信息失败: ' + error.message
    });
  }
});

// 获取设备应用信息API
app.get('/api/device/:deviceId/apps', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;

  console.log(`查询设备应用信息: ${deviceId}`);

  try {
    if (pool) {
      const [rows] = await pool.execute(`
        SELECT app_type, app_name, app_text, app_bounds, is_clickable, detection_method, detected_at
        FROM device_apps
        WHERE device_id = ?
        ORDER BY app_type, detected_at DESC
      `, [deviceId]);

      // 按应用类型分组
      const apps = {
        xiaohongshu: [],
        xianyu: []
      };

      rows.forEach(row => {
        const appInfo = {
          name: row.app_name,
          text: row.app_text,
          bounds: row.app_bounds,
          clickable: row.is_clickable,
          method: row.detection_method,
          detectedAt: row.detected_at
        };

        if (row.app_type === 'xiaohongshu') {
          apps.xiaohongshu.push(appInfo);
        } else if (row.app_type === 'xianyu') {
          apps.xianyu.push(appInfo);
        }
      });

      res.json({
        success: true,
        data: apps
      });
    } else {
      res.status(500).json({
        success: false,
        message: '数据库不可用'
      });
    }
  } catch (error) {
    console.error('查询应用信息失败:', error);
    res.status(500).json({
      success: false,
      message: '查询应用信息失败: ' + error.message
    });
  }
});

// 测试API端点（用于CORS测试）
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'CORS测试成功',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin || 'no-origin',
    userAgent: req.headers['user-agent'] || 'no-user-agent',
    method: req.method,
    url: req.url
  });
});

// 静态文件服务 - 先提供静态资源
app.use(express.static(path.join(__dirname, 'web/dist')));

// 提供根目录的HTML文件（用于测试页面）
app.use(express.static(__dirname));

// 提供上传文件的静态访问
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 设置SVG缩略图的正确MIME类型
app.use('/uploads/thumbnails', (req, res, next) => {
  if (req.path.endsWith('.svg')) {
    res.setHeader('Content-Type', 'image/svg+xml');
  }
  next();
});



// 增强版脚本配置页面路由
app.get('/script-config-enhanced.html', (req, res) => {
  const testPagePath = path.join(__dirname, 'script-config-enhanced.html');

  if (fs.existsSync(testPagePath)) {
    res.sendFile(testPagePath);
  } else {
    res.status(404).send('增强版脚本配置页面未找到');
  }
});

// 屏幕流测试页面路由
app.get('/screen-stream-test.html', (req, res) => {
  const testPagePath = path.join(__dirname, 'screen-stream-test.html');

  if (fs.existsSync(testPagePath)) {
    res.sendFile(testPagePath);
  } else {
    res.status(404).send('屏幕流测试页面未找到');
  }
});

// 设备管理测试页面路由
app.get('/device-test.html', (req, res) => {
  const testPagePath = path.join(__dirname, 'device-test.html');

  if (fs.existsSync(testPagePath)) {
    res.sendFile(testPagePath);
  } else {
    res.status(404).send('设备测试页面未找到');
  }
});

// ==================== 闲鱼私聊记录管理API ====================

// 创建私聊记录（由手机端脚本调用）
app.post('/api/xianyu/chat-record', async (req, res) => {
  try {
    const chatRecord = req.body;

    console.log('收到私聊记录上报:', {
      deviceId: chatRecord.deviceId,
      keyword: chatRecord.keyword,
      postTitle: chatRecord.postTitle
    });

    // 验证必要字段
    if (!chatRecord.deviceId || !chatRecord.keyword || !chatRecord.messageContent) {
      return res.status(400).json({
        success: false,
        message: '缺少必要字段: deviceId, keyword, messageContent'
      });
    }

    // 如果有闲鱼私聊服务，保存到数据库
    if (xianyuChatService) {
      try {
        // 检查是否存在重复记录
        const isDuplicate = await xianyuChatService.checkDuplicateRecord(
          chatRecord.deviceId,
          chatRecord.postId,
          chatRecord.keyword
        );

        if (isDuplicate) {
          console.log('发现重复私聊记录，跳过保存');
          return res.json({
            success: true,
            message: '记录已存在，跳过保存',
            duplicate: true
          });
        }

        // 创建私聊记录
        const recordId = await xianyuChatService.createChatRecord(chatRecord);
        console.log('私聊记录已保存到数据库:', recordId);

        res.json({
          success: true,
          message: '私聊记录保存成功',
          data: {
            recordId: recordId
          }
        });
      } catch (dbError) {
        console.error('数据库保存私聊记录失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库保存失败: ' + dbError.message
        });
      }
    } else {
      // 数据库不可用，返回成功但不保存
      console.log('闲鱼私聊服务不可用，无法保存到数据库');
      res.json({
        success: true,
        message: '私聊记录已接收（数据库不可用）',
        data: {
          recordId: null
        }
      });
    }

  } catch (error) {
    console.error('保存私聊记录失败:', error);
    res.status(500).json({
      success: false,
      message: '保存失败: ' + error.message
    });
  }
});

// 获取设备的私聊记录列表
app.get('/api/xianyu/chat-records/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { page = 1, limit = 50, keyword = '' } = req.query;

    console.log('获取私聊记录:', { deviceId, page, limit, keyword });

    if (xianyuChatService) {
      try {
        const result = await xianyuChatService.getChatRecordsByDevice(
          deviceId,
          parseInt(page),
          parseInt(limit),
          keyword
        );

        res.json({
          success: true,
          data: result
        });
      } catch (dbError) {
        console.error('数据库查询私聊记录失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库查询失败: ' + dbError.message
        });
      }
    } else {
      // 数据库不可用，返回空结果
      res.json({
        success: true,
        data: {
          records: [],
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: 0
        }
      });
    }

  } catch (error) {
    console.error('获取私聊记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 获取设备私聊统计信息
app.get('/api/xianyu/chat-statistics/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;

    if (xianyuChatService) {
      try {
        const statistics = await xianyuChatService.getChatStatistics(deviceId);
        const keywordStats = await xianyuChatService.getKeywordStatistics(deviceId);

        res.json({
          success: true,
          data: {
            statistics: statistics,
            keywordStats: keywordStats
          }
        });
      } catch (dbError) {
        console.error('数据库获取私聊统计失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库查询失败: ' + dbError.message
        });
      }
    } else {
      // 数据库不可用，返回空统计
      res.json({
        success: true,
        data: {
          statistics: {
            totalChats: 0,
            uniqueKeywords: 0,
            activeDays: 0,
            lastChatTime: null,
            firstChatTime: null
          },
          keywordStats: []
        }
      });
    }

  } catch (error) {
    console.error('获取私聊统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计失败: ' + error.message
    });
  }
});

// 删除单条私聊记录
app.delete('/api/xianyu/chat-record/:recordId', async (req, res) => {
  try {
    const { recordId } = req.params;
    const { deviceId } = req.body;

    if (!deviceId) {
      return res.status(400).json({
        success: false,
        message: '缺少设备ID'
      });
    }

    if (xianyuChatService) {
      try {
        const success = await xianyuChatService.deleteChatRecord(recordId, deviceId);

        if (success) {
          res.json({
            success: true,
            message: '删除成功'
          });
        } else {
          res.status(404).json({
            success: false,
            message: '记录不存在或无权限删除'
          });
        }
      } catch (dbError) {
        console.error('数据库删除私聊记录失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库删除失败: ' + dbError.message
        });
      }
    } else {
      res.json({
        success: true,
        message: '删除成功（数据库不可用）'
      });
    }

  } catch (error) {
    console.error('删除私聊记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败: ' + error.message
    });
  }
});

// 批量删除私聊记录
app.delete('/api/xianyu/chat-records/batch', async (req, res) => {
  try {
    const { recordIds, deviceId } = req.body;

    if (!deviceId || !recordIds || !Array.isArray(recordIds)) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: deviceId, recordIds'
      });
    }

    if (xianyuChatService) {
      try {
        const deletedCount = await xianyuChatService.deleteChatRecords(recordIds, deviceId);

        res.json({
          success: true,
          message: `成功删除 ${deletedCount} 条记录`,
          data: {
            deletedCount: deletedCount
          }
        });
      } catch (dbError) {
        console.error('数据库批量删除私聊记录失败:', dbError);
        res.status(500).json({
          success: false,
          message: '数据库批量删除失败: ' + dbError.message
        });
      }
    } else {
      res.json({
        success: true,
        message: `批量删除成功（数据库不可用）`,
        data: {
          deletedCount: recordIds.length
        }
      });
    }

  } catch (error) {
    console.error('批量删除私聊记录失败:', error);
    res.status(500).json({
      success: false,
      message: '批量删除失败: ' + error.message
    });
  }
});

// 清空设备的所有私聊记录
app.delete('/api/xianyu/chat-records/:deviceId/all', async (req, res) => {
  try {
    const { deviceId } = req.params;

    console.log('清空设备私聊记录:', deviceId);

    let dbDeletedCount = 0;
    let fileCleared = false;

    // 1. 清空数据库记录
    if (xianyuChatService) {
      try {
        dbDeletedCount = await xianyuChatService.clearAllChatRecords(deviceId);
        console.log(`数据库清空成功: ${dbDeletedCount} 条记录`);
      } catch (dbError) {
        console.error('数据库清空私聊记录失败:', dbError);
        return res.status(500).json({
          success: false,
          message: '数据库清空失败: ' + dbError.message
        });
      }
    }

    // 2. 通知手机端清空文件记录（使用命令队列机制）
    try {
      // 检查设备是否在线（通过最近的轮询时间判断）
      const device = devices.get(deviceId);
      console.log('设备查找结果:', {
        deviceId: deviceId,
        deviceFound: !!device,
        lastPollTime: device ? device.lastPollTime : 'N/A',
        currentTime: Date.now(),
        timeDiff: device && device.lastPollTime ? (Date.now() - device.lastPollTime) : 'N/A'
      });

      const isDeviceOnline = device && device.lastPollTime &&
                           (Date.now() - device.lastPollTime < 30000); // 30秒内有轮询认为在线

      if (isDeviceOnline) {
        console.log('设备在线，发送清空文件记录命令:', deviceId);

        // 创建清空文件记录的命令
        const clearFileCommand = {
          type: 'clearChatRecords',
          deviceId: deviceId,
          timestamp: Date.now()
        };

        // 将命令添加到设备的命令队列（使用全局pendingCommands）
        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }
        pendingCommands.get(deviceId).push(clearFileCommand);

        console.log('清空命令已添加到设备命令队列');

        // 等待设备处理命令并响应（轮询检查）
        const waitForResponse = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('设备响应超时'));
          }, 10000); // 10秒超时

          const checkInterval = setInterval(() => {
            // 检查是否有清空响应（这里简化处理，实际可以通过全局变量或数据库存储响应）
            // 由于我们使用HTTP轮询，设备会通过单独的API发送响应
            // 这里我们假设如果10秒内没有错误，就认为成功
            clearTimeout(timeout);
            clearInterval(checkInterval);
            resolve(true); // 简化处理，假设成功
          }, 1000);
        });

        try {
          fileCleared = await waitForResponse;
          console.log('设备文件清空命令已发送');
        } catch (waitError) {
          console.warn('等待设备文件清空响应失败:', waitError.message);
          fileCleared = false;
        }
      } else {
        console.warn('设备未在线，无法清空文件记录:', deviceId);
        fileCleared = false;
      }
    } catch (commandError) {
      console.error('发送清空文件命令失败:', commandError);
      fileCleared = false;
    }

    // 3. 返回结果
    const message = fileCleared
      ? `成功清空 ${dbDeletedCount} 条数据库记录和设备文件记录`
      : `成功清空 ${dbDeletedCount} 条数据库记录（设备文件清空失败或设备未连接）`;

    res.json({
      success: true,
      message: message,
      data: {
        deletedCount: dbDeletedCount,
        fileCleared: fileCleared,
        deviceConnected: !!devices.get(deviceId)
      }
    });

  } catch (error) {
    console.error('清空私聊记录失败:', error);
    res.status(500).json({
      success: false,
      message: '清空失败: ' + error.message
    });
  }
});

// 接收设备清空私聊记录的响应
app.post('/api/device/clear-chat-records-response', (req, res) => {
  try {
    const { deviceId, success, message } = req.body;

    console.log('收到设备清空私聊记录响应:', {
      deviceId: deviceId,
      success: success,
      message: message
    });

    // 这里可以存储响应结果，供清空API使用
    // 由于我们使用的是同步等待机制，这个响应会被清空API的等待逻辑处理

    res.json({
      success: true,
      message: '响应已接收'
    });

  } catch (error) {
    console.error('处理设备清空响应失败:', error);
    res.status(500).json({
      success: false,
      message: '处理响应失败: ' + error.message
    });
  }
});

// 接收设备脚本完成确认的响应
app.post('/api/device/script-completion-ack', (req, res) => {
  try {
    const { deviceId, taskId, message, hasRunningScript } = req.body;

    console.log('收到设备脚本完成确认响应:', {
      deviceId: deviceId,
      taskId: taskId,
      message: message,
      hasRunningScript: hasRunningScript
    });

    // 如果设备端仍有脚本在运行，记录警告
    if (hasRunningScript) {
      console.warn(`⚠️ 设备 ${deviceId} 确认收到脚本完成通知，但仍有脚本在运行`);
    } else {
      console.log(`✅ 设备 ${deviceId} 确认脚本已完成，状态正常`);
    }

    res.json({
      success: true,
      message: '脚本完成确认已接收'
    });

  } catch (error) {
    console.error('处理设备脚本完成确认响应失败:', error);
    res.status(500).json({
      success: false,
      message: '处理确认响应失败'
    });
  }
});

// SPA路由 - 所有其他请求返回index.html
app.get('*', (req, res) => {
  // 排除API请求
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ message: 'API not found' });
  }

  const indexPath = path.join(__dirname, 'web/dist/index.html');

  if (require('fs').existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).send('页面未找到，请确保前端已构建');
  }
});

// WebSocket连接处理
io.on('connection', (socket) => {
  console.log('新连接:', socket.id);

  // 设备注册
  socket.on('device_register', async (data) => {
    const { deviceId, deviceName, deviceInfo } = data;
    const currentIP = deviceInfo && deviceInfo.ipAddress || '未知';

    try {
      // 尝试保存到数据库（如果可用）
      if (pool) {
        try {
          await pool.execute(`
            INSERT INTO devices (device_id, device_name, device_info, status, last_seen)
            VALUES (?, ?, ?, 'online', NOW())
            ON DUPLICATE KEY UPDATE
            device_name = VALUES(device_name),
            device_info = VALUES(device_info),
            status = 'online',
            last_seen = NOW()
          `, [deviceId, deviceName, JSON.stringify({
            ...deviceInfo,
            ipAddress: currentIP,
            registrationTime: new Date().toISOString(),
            connectionType: 'websocket'
          })]);

          console.log('WebSocket设备信息已保存到数据库:', deviceId);
        } catch (dbError) {
          console.error('数据库保存失败，继续使用内存模式:', dbError);
        }
      }

    // 检查是否有相同IP的设备已连接，如果有则断开旧连接
    // 注意：只断开群控服务器连接，不影响VSCode调试连接（端口9317）
    if (currentIP && currentIP !== '未知') {
      for (const [socketId, device] of devices) {
        if (device.deviceInfo && device.deviceInfo.ipAddress === currentIP &&
            device.status === 'online' &&
            !socketId.startsWith('vscode_') && // 不影响VSCode调试连接
            !socketId.startsWith('debug_')) {  // 不影响其他调试连接
          console.log(`检测到同IP设备重连: ${currentIP}, 断开旧WebSocket连接: ${device.deviceName} (${device.deviceId})`);

          // 通知Web客户端旧设备离线
          for (const [clientSocketId] of webClients) {
            const clientSocket = io.sockets.sockets.get(clientSocketId);
            if (clientSocket) {
              clientSocket.emit('device_offline', {
                deviceId: device.deviceId
              });
            }
          }

          // 断开旧的socket连接
          const oldSocket = io.sockets.sockets.get(socketId);
          if (oldSocket) {
            oldSocket.disconnect(true);
          }

          // 移除旧设备连接
          devices.delete(socketId);
          break;
        }
      }
    }

    devices.set(socket.id, {
      socketId: socket.id,
      deviceId,
      deviceName,
      deviceInfo,
      status: 'online',
      connectedAt: new Date()
    });

    console.log(`设备已连接: ${deviceName} (${deviceId}) IP: ${currentIP}`);

    // 通知所有Web客户端设备上线
    for (const [clientSocketId] of webClients) {
      const clientSocket = io.sockets.sockets.get(clientSocketId);
      if (clientSocket) {
        clientSocket.emit('device_online', {
          deviceId,
          deviceName,
          deviceInfo,
          status: 'online'
        });
      }
    }

    // 广播设备状态变化事件（与HTTP注册保持一致）
    const statusChange = {
      type: 'device_connected',
      deviceId: deviceId,
      deviceName: deviceName,
      deviceIP: currentIP,
      deviceInfo: deviceInfo,
      timestamp: new Date()
    };

    io.emit('device_status_changed', statusChange);

    // 长轮询相关代码已移除，只使用WebSocket通信

    socket.emit('register_success', { message: '设备注册成功' });

    } catch (error) {
      console.error('WebSocket设备注册失败:', error);
      socket.emit('register_error', { message: '设备注册失败: ' + error.message });
    }
  });

  // Web客户端连接
  socket.on('web_client_connect', (data) => {
    webClients.set(socket.id, {
      socketId: socket.id,
      userId: data.userId || data.clientType,
      clientType: data.clientType,
      connectedAt: new Date()
    });

    console.log(`Web客户端已连接: ${data.userId || data.clientType}`);

    // 发送测试事件确认连接
    socket.emit('test_event', {
      message: '服务器确认连接成功',
      clientType: data.clientType,
      timestamp: new Date().toISOString()
    });

    // 发送当前在线设备列表
    const onlineDevices = Array.from(devices.values()).map(device => ({
      deviceId: device.deviceId,
      deviceName: device.deviceName,
      deviceInfo: device.deviceInfo,
      status: device.status,
      connectedAt: device.connectedAt
    }));

    socket.emit('devices_list', onlineDevices);
  });

  // 监听客户端测试事件
  socket.on('client_test', (data) => {
    console.log('🧪 收到客户端测试事件:', data);
    // 回复测试事件
    socket.emit('test_event', {
      message: '服务器收到测试消息',
      originalMessage: data.message,
      timestamp: new Date().toISOString()
    });
  });

  // 监听测试连接事件
  socket.on('test_connection', (data) => {
    console.log('🧪 收到测试连接事件:', data);
    socket.emit('test_event', {
      message: '连接测试成功',
      timestamp: new Date().toISOString()
    });
  });

  // 监听小红书停止脚本事件
  socket.on('xiaohongshu_stop_script', async (data) => {
    console.log('🛑 收到小红书停止脚本事件:', data);
    const { functionType, deviceId, logId, taskId } = data;

    try {
      // 查找目标设备
      let targetDevice = null;
      let targetSocket = null;

      for (const [socketId, device] of devices.entries()) {
        if (device.deviceId === deviceId) {
          targetDevice = device;
          targetSocket = io.sockets.sockets.get(socketId);
          break;
        }
      }

      if (targetDevice) {
        console.log(`向设备 ${deviceId} 发送停止脚本命令`);

        // 将停止命令添加到待执行队列（使用HTTP轮询方式）
        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        const stopCommand = {
          type: 'stop_script',
          functionType,
          taskId,
          reason: 'user_stop',
          timestamp: new Date().toISOString()
        };

        pendingCommands.get(deviceId).push(stopCommand);
        console.log(`停止命令已添加到设备 ${deviceId} 的待执行队列`);

        // 更新设备状态为在线
        targetDevice.status = 'online';

        // 通知所有Web客户端设备状态变化
        io.emit('device_status_changed', {
          type: 'status_update',
          deviceId: deviceId,
          status: 'online',
          timestamp: new Date().toISOString()
        });

        // 如果有数据库连接，更新执行日志
        if (pool && (logId || taskId)) {
          try {
            const logIdentifier = logId || taskId;
            await pool.execute(`
              UPDATE xiaohongshu_execution_logs
              SET execution_status = 'stopped',
                  progress_percentage = 0,
                  completed_at = NOW(),
                  execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW()),
                  error_message = '用户手动停止'
              WHERE task_id = ?
            `, [logIdentifier]);

            console.log(`已更新执行日志 ${logIdentifier} 状态为已停止`);
          } catch (dbError) {
            console.error('更新执行日志失败:', dbError);
          }
        }

        // 通知Web客户端脚本已停止
        io.emit('xiaohongshu_script_completed', {
          functionType,
          deviceId,
          taskId: taskId || logId,
          status: 'stopped',
          message: '脚本已被用户停止'
        });

        // 通知手机端关闭小红书应用
        console.log(`通知设备 ${deviceId} 关闭小红书应用`);
        if (targetSocket) {
          targetSocket.emit('script_command', {
            type: 'close_xiaohongshu_app',
            deviceId: deviceId,
            reason: '脚本被停止',
            timestamp: new Date().toISOString()
          });
        }

        console.log(`✅ 成功停止设备 ${deviceId} 的脚本执行`);
      } else {
        console.warn(`⚠️ 未找到设备 ${deviceId}，但仍尝试添加停止命令到队列`);

        // 即使设备不在当前连接列表中，也要添加停止命令到队列
        // 设备可能稍后会轮询到这个命令
        if (!pendingCommands.has(deviceId)) {
          pendingCommands.set(deviceId, []);
        }

        const stopCommand = {
          type: 'stop_script',
          functionType,
          taskId,
          reason: 'user_stop',
          timestamp: new Date().toISOString()
        };

        pendingCommands.get(deviceId).push(stopCommand);
        console.log(`停止命令已添加到离线设备 ${deviceId} 的待执行队列`);

        // 更新数据库状态
        if (pool && (logId || taskId)) {
          try {
            const logIdentifier = logId || taskId;
            await pool.execute(`
              UPDATE xiaohongshu_execution_logs
              SET execution_status = 'stopped',
                  progress_percentage = 0,
                  completed_at = NOW(),
                  execution_duration = TIMESTAMPDIFF(SECOND, started_at, NOW()),
                  error_message = '用户手动停止'
              WHERE task_id = ?
            `, [logIdentifier]);

            console.log(`已更新执行日志 ${logIdentifier} 状态为已停止`);
          } catch (dbError) {
            console.error('更新执行日志失败:', dbError);
          }
        }

        // 通知Web客户端
        io.emit('xiaohongshu_script_completed', {
          functionType,
          deviceId,
          taskId: taskId || logId,
          status: 'stopped',
          message: '停止命令已发送'
        });

        // 通知手机端关闭小红书应用
        console.log(`通知设备 ${deviceId} 关闭小红书应用`);
        if (pendingCommands.has(deviceId)) {
          pendingCommands.get(deviceId).push({
            type: 'close_xiaohongshu_app',
            deviceId: deviceId,
            reason: '脚本被停止',
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      console.error('处理停止脚本事件失败:', error);
    }
  });

  // 脚本执行结果
  socket.on('script_result', async (data) => {
    const { logId, result, status } = data;

    // 获取设备信息
    const device = devices.get(socket.id);
    const deviceId = device ? device.deviceId : null;

    // 更新日志
    const log = logs.find(l => l.id === logId);
    if (log) {
      log.result = result;
      log.status = status;
      log.completed_at = new Date();
    }

    // 检查是否是小红书任务的执行结果
    if (xiaohongshuLogService && logId && logId.includes('xiaohongshu_')) {
      try {
        // 更新小红书执行日志
        // 判断执行是否成功：status为'success'或者result中包含成功信息
        const isSuccess = status === 'success' ||
                         (result && (
                           result.includes('执行完成') ||
                           result.includes('成功') ||
                           result.includes('完成')
                         )) ||
                         status === 'completed';

        const finalStatus = isSuccess ? 'completed' : 'failed';
        const progress = isSuccess ? 100 : 0;

        await xiaohongshuLogService.updateExecutionStatus(
          logId,
          finalStatus,
          progress,
          isSuccess ? '执行完成' : '执行失败',
          `脚本执行${isSuccess ? '成功' : '失败'}: ${result || '无详细信息'}`
        );

        if (result) {
          await xiaohongshuLogService.updateExecutionResult(logId, {
            status: status,
            result: result,
            timestamp: new Date().toISOString()
          });
        }

        console.log(`小红书执行日志已更新: ${logId} -> ${finalStatus}`);

        // 从活动任务中移除已完成的任务，防止设备断开连接时被误标记为失败
        if (xiaohongshuActiveTasks.has(logId)) {
          const task = xiaohongshuActiveTasks.get(logId);
          task.status = finalStatus;
          task.endTime = new Date();
          task.result = result;

          // 移除活动任务
          xiaohongshuActiveTasks.delete(logId);
          console.log(`✅ [活动任务] 已从小红书活动任务列表中移除: ${logId}`);
        }

        // 如果是视频发布任务，更新传输记录状态
        if (logId.includes('videoPublish')) {
          await updateVideoTransferStatus(logId, deviceId, finalStatus, result);
        }

        // 恢复设备状态为在线
        if (deviceId) {
          try {
            await updateDeviceStatus(deviceId, 'online');
            console.log(`脚本执行完成，设备状态已恢复为在线: ${deviceId}`);
          } catch (error) {
            console.error('恢复设备状态失败:', error);
          }

          // 通知前端重置脚本执行状态
          io.emit('xiaohongshu_execution_completed', {
            deviceId: deviceId,
            taskId: logId,
            status: isSuccess ? 'success' : 'failed',
            message: result,
            timestamp: new Date().toISOString()
          });

          // 通知手机端关闭小红书应用
          console.log(`通知设备 ${deviceId} 关闭小红书应用`);
          io.to(deviceId).emit('script_command', {
            type: 'close_xiaohongshu_app',
            deviceId: deviceId,
            reason: isSuccess ? '脚本执行完成' : '脚本执行失败',
            timestamp: new Date().toISOString()
          });
        }
      } catch (logError) {
        console.error('更新小红书执行日志失败:', logError);
      }
    }

    // 检查是否是闲鱼任务的执行结果
    if (xianyuLogService && logId && logId.includes('xianyu_')) {
      try {
        console.log(`[闲鱼任务结果] 收到设备结果: logId=${logId}, status=${status}, result=${result}`);

        // 处理停止命令的logId（去掉stop_前缀）
        let actualLogId = logId;
        if (logId.startsWith('stop_')) {
          actualLogId = logId.substring(5); // 去掉"stop_"前缀
          console.log(`[闲鱼任务结果] 检测到停止命令，原始logId: ${actualLogId}`);
        }

        // 判断执行是否成功
        const isSuccess = status === 'success' ||
                         status === '完成' ||
                         status === 'completed' ||
                         (result && (
                           result.includes('执行完成') ||
                           result.includes('成功') ||
                           result.includes('完成')
                         ));

        console.log(`[闲鱼任务结果] 成功判断: status=${status}, result=${result}, isSuccess=${isSuccess}`);

        const finalStatus = isSuccess ? 'completed' : 'failed';
        const finalProgress = isSuccess ? 100 : 0;

        console.log(`[闲鱼任务结果] 更新执行日志: ${actualLogId} -> ${finalStatus} (${finalProgress}%)`);

        // 更新闲鱼执行日志
        await xianyuLogService.updateExecutionStatus(
          actualLogId,
          finalStatus,
          finalProgress,
          isSuccess ? '执行完成' : '执行失败',
          null, // debugLogs
          isSuccess ? result : `执行失败: ${result}`
        );

        console.log(`闲鱼执行日志已更新: ${actualLogId} -> ${finalStatus}`);

        // 恢复设备状态为在线
        if (deviceId) {
          try {
            await updateDeviceStatus(deviceId, 'online');
            console.log(`闲鱼脚本执行完成，设备状态已恢复为在线: ${deviceId}`);
          } catch (error) {
            console.error('恢复设备状态失败:', error);
          }

          // 通知前端重置脚本执行状态
          io.emit('xianyu_execution_completed', {
            deviceId: deviceId,
            taskId: actualLogId,
            status: isSuccess ? 'success' : 'failed',
            message: result,
            timestamp: new Date().toISOString()
          });
        }
      } catch (logError) {
        console.error('更新闲鱼执行日志失败:', logError);
      }
    }

    // 检查是否是闲鱼任务的执行结果（兼容旧版本逻辑）
    if (deviceId) {
      // 查找该设备的闲鱼任务
      let xianyuTask = null;
      for (const [taskId, task] of xianyuActiveTasks) {
        if (task.deviceId === deviceId) {
          xianyuTask = task;
          break;
        }
      }

      if (xianyuTask) {
        try {
          // 判断执行是否成功
          const isSuccess = status === 'success' ||
                           (result && (
                             result.includes('执行完成') ||
                             result.includes('成功') ||
                             result.includes('完成')
                           )) ||
                           status === 'completed';

          const finalStatus = isSuccess ? 'completed' : 'failed';

          // 更新任务状态
          xianyuTask.status = finalStatus;
          xianyuTask.endTime = new Date();
          xianyuTask.result = {
            status: status,
            result: result,
            timestamp: new Date().toISOString()
          };

          // 移动到历史记录
          xianyuActiveTasks.delete(xianyuTask.id);
          xianyuTaskHistory.push(xianyuTask);

          console.log(`闲鱼任务执行完成: ${xianyuTask.id} -> ${finalStatus}`);

          // 恢复设备状态为在线
          try {
            await updateDeviceStatus(deviceId, 'online');
            console.log(`闲鱼任务完成，设备状态已恢复为在线: ${deviceId}`);
          } catch (error) {
            console.error('恢复设备状态失败:', error);
          }

          // 通知前端任务完成
          io.emit('xianyu_task_completed', {
            taskId: xianyuTask.id,
            deviceId: deviceId,
            functionType: xianyuTask.functionType,
            status: isSuccess ? 'success' : 'failed',
            result: xianyuTask.result,
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          console.error('处理闲鱼任务完成失败:', error);
        }
      }
    }

    // 通知Web客户端
    for (const [clientSocketId] of webClients) {
      const clientSocket = io.sockets.sockets.get(clientSocketId);
      if (clientSocket) {
        clientSocket.emit('script_result', data);
      }
    }

    console.log(`脚本执行完成: 状态=${status}`);
  });

  // 心跳处理
  socket.on('ping', () => {
    socket.emit('pong');
  });

  // 断开连接
  socket.on('disconnect', async () => {
    const device = devices.get(socket.id);
    const webClient = webClients.get(socket.id);

    // 如果是设备断开连接，需要处理正在执行的任务
    if (device) {
      console.log(`设备断开连接: ${device.deviceId}`);

      // 检查是否有正在执行的闲鱼任务
      for (const [taskId, task] of xianyuActiveTasks) {
        if (task.deviceId === device.deviceId && task.status === 'running') {
          console.log(`设备断开连接，标记闲鱼任务失败: ${taskId}`);

          // 更新任务状态
          task.status = 'failed';
          task.endTime = new Date();
          task.result = '设备断开连接';

          // 更新数据库记录
          if (xianyuLogService) {
            try {
              await xianyuLogService.updateExecutionStatus(
                taskId,
                'failed',
                0,
                '设备断开连接',
                '设备在脚本执行过程中断开连接',
                '设备在脚本执行过程中断开连接'
              );
              console.log(`✅ 已更新闲鱼执行日志: ${taskId}`);
            } catch (logError) {
              console.error('❌ 更新闲鱼执行日志失败:', logError);
            }
          }

          // 通知前端任务失败
          io.emit('xianyu_task_completed', {
            taskId: taskId,
            deviceId: device.deviceId,
            functionType: task.functionType,
            status: 'failed',
            result: '设备断开连接',
            timestamp: new Date().toISOString()
          });

          // 从活动任务中移除
          xianyuActiveTasks.delete(taskId);
        }
      }

      // 检查是否有正在执行的小红书任务
      for (const [taskId, task] of xiaohongshuActiveTasks) {
        if (task.deviceId === device.deviceId && task.status === 'running') {
          console.log(`设备断开连接，标记小红书任务失败: ${taskId}`);

          // 更新任务状态
          task.status = 'failed';
          task.endTime = new Date();
          task.result = '设备断开连接';

          // 更新数据库记录
          if (xiaohongshuLogService) {
            try {
              await xiaohongshuLogService.updateExecutionStatus(
                taskId,
                'failed',
                0,
                '设备断开连接',
                '设备在脚本执行过程中断开连接',
                '设备在脚本执行过程中断开连接'
              );
              console.log(`✅ 已更新小红书执行日志: ${taskId}`);
            } catch (logError) {
              console.error('❌ 更新小红书执行日志失败:', logError);
            }
          }

          // 通知前端任务失败
          io.emit('xiaohongshu_execution_completed', {
            taskId: taskId,
            deviceId: device.deviceId,
            functionType: task.functionType,
            status: 'failed',
            result: '设备断开连接',
            timestamp: new Date().toISOString()
          });

          // 从活动任务中移除
          activeTasks.delete(taskId);
        }
      }
    }

    if (device) {
      console.log(`设备已断开: ${device.deviceName} (${device.deviceId})`);

      // 尝试更新数据库状态（如果可用）
      if (pool) {
        try {
          await pool.execute(`
            UPDATE devices SET status = 'offline', last_seen = NOW()
            WHERE device_id = ?
          `, [device.deviceId]);

          console.log('设备状态已更新为离线:', device.deviceId);
        } catch (dbError) {
          console.error('更新设备离线状态失败:', dbError);
        }
      }

      // 清理该设备相关的小红书任务
      await cleanupXiaohongshuTasksForDevice(device.deviceId);

      // 清理该设备相关的闲鱼任务
      await cleanupXianyuTasksForDevice(device.deviceId);

      // 通知所有Web客户端设备离线
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket) {
          clientSocket.emit('device_offline', {
            deviceId: device.deviceId
          });
        }
      }

      // 通知所有Web客户端设备状态变化（设备断开连接）
      console.log('发送WebSocket通知: device_status_changed (设备主动断开)');
      const statusChange = {
        type: 'device_disconnected',
        deviceId: device.deviceId,
        deviceName: device.deviceName,
        timestamp: new Date()
      };

      io.emit('device_status_changed', statusChange);
      // 长轮询相关代码已移除，只使用WebSocket通信

      devices.delete(socket.id);
    }

    if (webClient) {
      console.log(`Web客户端已断开: ${webClient.userId}`);
      webClients.delete(socket.id);
    }
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    connectedDevices: devices.size,
    connectedWebClients: webClients.size
  });
});

// 定期清理离线设备状态（每5分钟执行一次）
setInterval(async () => {
  if (!pool) return;

  try {
    // 获取所有当前连接的设备ID
    const connectedDeviceIds = Array.from(devices.values()).map(device => device.deviceId);

    if (connectedDeviceIds.length === 0) {
      // 如果没有连接的设备，将所有设备标记为离线
      const [result] = await pool.execute(`
        UPDATE devices SET status = 'offline', last_seen = NOW()
        WHERE status != 'offline'
      `);
      if (result.affectedRows > 0) {
        console.log(`已将 ${result.affectedRows} 个设备状态更新为离线`);
      }
    } else {
      // 将未连接的设备标记为离线
      const placeholders = connectedDeviceIds.map(() => '?').join(',');
      const [result] = await pool.execute(`
        UPDATE devices SET status = 'offline', last_seen = NOW()
        WHERE device_id NOT IN (${placeholders}) AND status != 'offline'
      `, connectedDeviceIds);
      if (result.affectedRows > 0) {
        console.log(`已将 ${result.affectedRows} 个未连接设备状态更新为离线，当前在线设备: ${connectedDeviceIds.length}个`);
      }
    }
  } catch (error) {
    console.error('定期清理设备状态失败:', error);
  }
}, 5 * 60 * 1000); // 5分钟

// Multer错误处理中间件（放在所有路由之后）
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    console.error('Multer错误:', error);
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大2GB）'
      });
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超过限制（最大1000个文件）'
      });
    } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: `不支持的文件字段名: ${error.field}`
      });
    } else {
      return res.status(400).json({
        success: false,
        message: '文件上传错误: ' + error.message
      });
    }
  }
  next(error);
});

// 设置设备连接码管理模块
if (pool) {
  try {
    const { setupDeviceConnectionCodes } = require('./server/device/device-connection-codes');
    setupDeviceConnectionCodes(app, pool, authenticateToken, userIsolationMiddleware);
    console.log('✅ 设备连接码管理模块已加载');
  } catch (error) {
    console.log('⚠️ 设备连接码管理模块加载失败:', error.message);
  }
}

// 通用错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

const PORT = process.env.PORT || 3002;
server.listen(PORT, '0.0.0.0', () => {
  console.log('================================');
  console.log('Auto.js云群控系统测试服务器');
  console.log('================================');
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`主界面: http://localhost:${PORT}`);
  console.log(`设备状态同步机制已启动（每5分钟检查一次）`);
  console.log(`外部访问: http://0.0.0.0:${PORT}`);
  console.log(`设备管理: http://localhost:${PORT}/device-test.html`);
  console.log(`屏幕流测试: http://localhost:${PORT}/screen-stream-test.html`);
  console.log(`脚本配置: http://localhost:${PORT}/script-config-enhanced.html`);
  console.log(`健康检查: http://localhost:${PORT}/health`);
  console.log('默认登录账号: admin / admin123');
  console.log('');
  console.log('注意: 这是测试服务器，数据存储在内存中');
  console.log('生产环境请使用完整版本的服务器');
  console.log('================================');

  // 服务器启动时立即同步一次设备状态
  setTimeout(async () => {
    if (pool) {
      try {
        // 清理过期的执行任务状态（超过10分钟的任务标记为失败）
        const [expiredTasks] = await pool.execute(`
          UPDATE xiaohongshu_execution_logs 
          SET execution_status = 'failed', 
              progress_percentage = 0,
              error_message = '服务器重启，任务已过期',
              execution_result = '执行失败',
              completed_at = NOW()
          WHERE execution_status IN ('running', 'pending') 
          AND started_at < DATE_SUB(NOW(), INTERVAL 10 MINUTE)
        `);
        
        if (expiredTasks.affectedRows > 0) {
          console.log(`服务器启动时已清理 ${expiredTasks.affectedRows} 个过期的执行任务`);
        }

        // 同步设备状态为离线
        const [result] = await pool.execute(`
          UPDATE devices SET status = 'offline', last_seen = NOW()
          WHERE status != 'offline'
        `);
        if (result.affectedRows > 0) {
          console.log(`服务器启动时已将 ${result.affectedRows} 个设备状态同步为离线`);
        }
        console.log('设备状态初始化同步完成');
      } catch (error) {
        console.error('初始化设备状态同步失败:', error);
      }
    }
  }, 1000); // 1秒后执行
});

// 服务器关闭时的清理逻辑
async function gracefulShutdown(signal) {
  console.log(`\n🚨🚨🚨 收到 ${signal} 信号，开始优雅关闭服务器 🚨🚨🚨`);
  console.log(`🚨 当前时间: ${new Date().toISOString()}`);
  console.log(`🚨 当前设备数量: ${devices.size}, Web客户端数量: ${webClients.size}`);

  // 防止重复调用
  if (gracefulShutdown.isShuttingDown) {
    console.log('🚨 服务器已在关闭过程中，忽略重复信号');
    return;
  }
  gracefulShutdown.isShuttingDown = true;

  try {
    // 第一步：停止所有设备的脚本任务
    console.log('🛑🛑🛑 第一步：停止所有设备的脚本任务 🛑🛑🛑');
    console.log(`🛑 当前设备数量: ${devices.size}`);

    if (devices.size > 0) {
      for (const [socketId, device] of devices) {
        console.log(`🛑 处理设备: ${device.deviceName} (${device.deviceId})`);
        console.log(`🛑 Socket ID: ${socketId}`);

        // 检查是否是HTTP设备（socketId以http_开头）
        if (socketId.startsWith('http_')) {
          console.log(`🛑 ✅ 设备 ${device.deviceId} 是HTTP连接设备`);

          // 对于HTTP设备，添加停止命令到pendingCommands队列
          if (!pendingCommands.has(device.deviceId)) {
            pendingCommands.set(device.deviceId, []);
          }

          const stopCommand = {
            type: 'stop_script',
            deviceId: device.deviceId,
            reason: '服务器关闭',
            timestamp: new Date().toISOString()
          };

          try {
            pendingCommands.get(device.deviceId).push(stopCommand);
            console.log(`🛑 ✅ 已添加停止脚本命令到HTTP设备 ${device.deviceId} 的队列`);
            console.log(`🛑 当前队列长度: ${pendingCommands.get(device.deviceId).length}`);
          } catch (error) {
            console.error(`🛑 ❌ 添加停止脚本命令失败:`, error);
          }

        } else {
          // WebSocket设备
          const deviceSocket = io.sockets.sockets.get(socketId);
          if (deviceSocket && deviceSocket.connected) {
            console.log(`🛑 ✅ 设备 ${device.deviceId} WebSocket连接正常`);

            const stopCommand = {
              type: 'stop_script',
              deviceId: device.deviceId,
              reason: '服务器关闭',
              timestamp: new Date().toISOString()
            };

            try {
              deviceSocket.emit('script_command', stopCommand);
              console.log(`🛑 ✅ 已发送停止脚本命令到WebSocket设备 ${device.deviceId}`);
            } catch (error) {
              console.error(`🛑 ❌ 发送停止脚本命令失败:`, error);
            }

          } else {
            console.log(`🛑 ❌ 设备 ${device.deviceId} WebSocket连接异常`);
          }
        }
      }

      // 等待脚本停止
      console.log('🛑 等待脚本停止完成（10秒）...');
      for (let i = 10; i > 0; i--) {
        console.log(`🛑 倒计时: ${i} 秒`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('🛑 ✅ 脚本停止等待完成');
    } else {
      console.log('🛑 没有连接的设备需要停止脚本');
    }

    // 第二步：关闭所有设备的小红书应用
    console.log('📱📱📱 第二步：关闭所有设备的小红书应用 📱📱📱');

    if (devices.size > 0) {
      for (const [socketId, device] of devices) {
        console.log(`📱 关闭设备 ${device.deviceName} (${device.deviceId}) 的小红书应用`);
        console.log(`📱 Socket ID: ${socketId}`);

        // 检查是否是HTTP设备
        if (socketId.startsWith('http_')) {
          console.log(`📱 ✅ 设备 ${device.deviceId} 是HTTP连接设备`);

          // 对于HTTP设备，添加关闭应用命令到pendingCommands队列
          if (!pendingCommands.has(device.deviceId)) {
            pendingCommands.set(device.deviceId, []);
          }

          const closeCommand = {
            type: 'close_xiaohongshu_app',
            deviceId: device.deviceId,
            reason: '服务器关闭',
            timestamp: new Date().toISOString()
          };

          try {
            pendingCommands.get(device.deviceId).push(closeCommand);
            console.log(`📱 ✅ 已添加关闭应用命令到HTTP设备 ${device.deviceId} 的队列`);
            console.log(`📱 当前队列长度: ${pendingCommands.get(device.deviceId).length}`);
          } catch (error) {
            console.error(`📱 ❌ 添加关闭应用命令失败:`, error);
          }

        } else {
          // WebSocket设备
          const deviceSocket = io.sockets.sockets.get(socketId);
          if (deviceSocket && deviceSocket.connected) {
            console.log(`📱 ✅ 设备 ${device.deviceId} WebSocket连接正常`);

            const closeCommand = {
              type: 'close_xiaohongshu_app',
              deviceId: device.deviceId,
              reason: '服务器关闭',
              timestamp: new Date().toISOString()
            };

            try {
              deviceSocket.emit('script_command', closeCommand);
              console.log(`📱 ✅ 已发送关闭应用命令到WebSocket设备 ${device.deviceId}`);
            } catch (error) {
              console.error(`📱 ❌ 发送关闭应用命令失败:`, error);
            }

          } else {
            console.log(`📱 ❌ 设备 ${device.deviceId} WebSocket连接异常`);
          }
        }
      }

      // 等待应用关闭和状态上报
      console.log('📱 等待应用关闭和状态上报完成（15秒）...');
      for (let i = 15; i > 0; i--) {
        console.log(`📱 倒计时: ${i} 秒`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('📱 ✅ 应用关闭等待完成');
    } else {
      console.log('📱 没有连接的设备需要关闭应用');
    }

    // 第三步：更新数据库中所有执行中的任务状态
    console.log('💾💾💾 第三步：更新数据库中的任务状态 💾💾💾');

    try {
      // 更新所有执行中的小红书任务状态为失败
      console.log('💾 更新执行中的任务状态为失败...');
      const [result] = await pool.execute(`
        UPDATE xiaohongshu_execution_logs
        SET execution_status = 'failed',
            progress_percentage = 0,
            error_message = '服务器关闭',
            completed_at = NOW()
        WHERE execution_status IN ('pending', 'running')
      `);

      console.log(`💾 ✅ 已更新 ${result.affectedRows} 条执行记录状态为失败`);

      // 更新所有忙碌设备状态为在线
      const deviceIds = Array.from(devices.values()).map(device => device.deviceId);
      console.log(`💾 需要更新状态的设备: ${deviceIds.length} 个`);

      if (deviceIds.length > 0) {
        for (const deviceId of deviceIds) {
          try {
            await updateDeviceStatus(deviceId, 'online');
            console.log(`💾 ✅ 设备 ${deviceId} 状态已更新为在线`);
          } catch (error) {
            console.error(`💾 ❌ 更新设备 ${deviceId} 状态失败:`, error);
          }
        }
      }

      console.log('💾 ✅ 数据库状态更新完成');

    } catch (error) {
      console.error('💾 ❌ 更新数据库状态失败:', error);
    }

    // 第四步：通知前端清理状态
    console.log('📢📢📢 第四步：通知前端清理状态 📢📢📢');
    console.log(`📢 当前Web客户端数量: ${webClients.size}`);

    if (webClients.size > 0) {
      // 向所有Web客户端发送清理状态事件
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket && clientSocket.connected) {
          console.log(`📢 ✅ 发送清理状态事件到客户端: ${clientSocketId}`);

          const prepareShutdownEvent = {
            message: '服务器准备关闭，正在清理前端状态...',
            timestamp: new Date().toISOString()
          };

          try {
            clientSocket.emit('server_prepare_shutdown', prepareShutdownEvent);
            console.log(`📢 ✅ 已发送清理状态事件到客户端 ${clientSocketId}`);
          } catch (error) {
            console.error(`📢 ❌ 发送清理状态事件失败:`, error);
          }

        } else {
          console.log(`📢 ❌ 客户端 ${clientSocketId} Socket连接异常`);
        }
      }

      // 等待前端清理状态
      console.log('📢 等待前端清理状态完成（5秒）...');
      for (let i = 5; i > 0; i--) {
        console.log(`📢 倒计时: ${i} 秒`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('📢 ✅ 前端清理状态等待完成');
    } else {
      console.log('📢 没有Web客户端需要清理状态');
    }

    // 第五步：设备状态变更为离线
    console.log('📱📱📱 第五步：设备状态变更为离线 📱📱📱');

    const deviceIds = Array.from(devices.values()).map(device => device.deviceId);
    console.log(`📱 需要变更为离线的设备: ${deviceIds.length} 个`);

    if (deviceIds.length > 0) {
      for (const deviceId of deviceIds) {
        try {
          await updateDeviceStatus(deviceId, 'offline');
          console.log(`📱 ✅ 设备 ${deviceId} 状态已更新为离线`);

          // 通知前端设备离线
          io.emit('device_status_changed', {
            type: 'device_disconnected',
            deviceId: deviceId,
            timestamp: new Date()
          });
          console.log(`📱 ✅ 已通知前端设备 ${deviceId} 离线`);

        } catch (error) {
          console.error(`📱 ❌ 更新设备 ${deviceId} 状态失败:`, error);
        }
      }
      console.log('📱 ✅ 设备状态变更完成');
    } else {
      console.log('📱 没有设备需要变更状态');
    }

    // 第六步：通知前端服务器即将关闭
    console.log('📢📢📢 第六步：通知前端服务器即将关闭 📢📢📢');

    if (webClients.size > 0) {
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket && clientSocket.connected) {
          console.log(`📢 ✅ 发送服务器关闭事件到客户端: ${clientSocketId}`);

          try {
            clientSocket.emit('server_shutdown', {
              message: '服务器即将关闭',
              timestamp: new Date().toISOString()
            });
            console.log(`📢 ✅ 已发送关闭事件到客户端 ${clientSocketId}`);
          } catch (error) {
            console.error(`📢 ❌ 发送关闭事件失败:`, error);
          }
        }
      }

      // 等待前端显示关闭消息
      console.log('📢 等待前端显示关闭消息（3秒）...');
      for (let i = 3; i > 0; i--) {
        console.log(`📢 倒计时: ${i} 秒`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      console.log('📢 ✅ 前端关闭消息等待完成');
    }

    // 第七步：断开前端连接
    console.log('🔌🔌🔌 第七步：断开前端连接 🔌🔌🔌');

    if (webClients.size > 0) {
      for (const [clientSocketId] of webClients) {
        const clientSocket = io.sockets.sockets.get(clientSocketId);
        if (clientSocket && clientSocket.connected) {
          console.log(`🔌 ✅ 断开Web客户端: ${clientSocketId}`);
          try {
            clientSocket.disconnect(true);
            console.log(`🔌 ✅ 已断开客户端 ${clientSocketId}`);
          } catch (error) {
            console.error(`🔌 ❌ 断开客户端失败:`, error);
          }
        }
      }

      console.log('🔌 等待前端连接断开（2秒）...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('🔌 ✅ 前端连接断开完成');
    }

    // 第八步：断开设备连接
    console.log('📱📱📱 第八步：断开设备连接 📱📱📱');

    if (devices.size > 0) {
      for (const [socketId, device] of devices) {
        const deviceSocket = io.sockets.sockets.get(socketId);
        if (deviceSocket && deviceSocket.connected) {
          console.log(`📱 ✅ 断开设备: ${device.deviceName} (${device.deviceId})`);
          try {
            deviceSocket.disconnect(true);
            console.log(`📱 ✅ 已断开设备 ${device.deviceId}`);
          } catch (error) {
            console.error(`📱 ❌ 断开设备失败:`, error);
          }
        }
      }

      console.log('📱 等待设备连接断开（2秒）...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('📱 ✅ 设备连接断开完成');
    }

    // 第九步：最终清理和关闭服务器
    console.log('🔚🔚🔚 第九步：最终清理和关闭服务器 🔚🔚🔚');

    // 清理所有连接
    devices.clear();
    webClients.clear();
    pendingCommands.clear();
    // 长轮询相关代码已移除
    console.log('🔚 ✅ 所有连接已清理');

    // 关闭服务器
    console.log('🔚 正在关闭HTTP服务器...');
    server.close(() => {
      console.log('🔚 ✅ HTTP服务器已关闭');
      console.log('🎉🎉🎉 服务器已优雅关闭 🎉🎉🎉');
      process.exit(0);
    });

    // 强制退出（如果10秒内没有正常关闭）
    setTimeout(() => {
      console.log('⚠️⚠️⚠️ 强制退出服务器 ⚠️⚠️⚠️');
      process.exit(1);
    }, 10000);

  } catch (error) {
    console.error('🚨🚨🚨 服务器关闭过程中发生错误 🚨🚨🚨');
    console.error('错误详情:', error);
    console.error('错误堆栈:', error.stack);

    // 即使出错也要尝试关闭服务器
    try {
      console.log('🚨 尝试强制关闭服务器...');
      devices.clear();
      webClients.clear();
      pendingCommands.clear();
      // 长轮询相关代码已移除

      server.close(() => {
        console.log('🚨 服务器已强制关闭');
        process.exit(1);
      });

      setTimeout(() => {
        console.log('🚨 最终强制退出');
        process.exit(1);
      }, 5000);

    } catch (finalError) {
      console.error('🚨 最终关闭也失败:', finalError);
      process.exit(1);
    }
  }
}

// 停止设备任务API
app.post('/api/xiaohongshu/stop-device-tasks', authenticateToken, async (req, res) => {
  const { deviceId, reason } = req.body;

  console.log(`📋 [停止设备任务] 收到请求: deviceId=${deviceId}, reason=${reason}`);

  try {
    let updatedCount = 0;

    // 首先查询该设备当前的执行记录
    console.log(`📋 [停止设备任务] 查询设备 "${deviceId}" 的当前执行记录`);
    const [currentRecords] = await pool.execute(`
      SELECT task_id, execution_status, progress_percentage, started_at
      FROM xiaohongshu_execution_logs
      WHERE device_id = ?
      ORDER BY started_at DESC
      LIMIT 5
    `, [deviceId]);

    console.log(`📋 [停止设备任务] 找到 ${currentRecords.length} 条记录:`);
    currentRecords.forEach(record => {
      console.log(`📋   - ${record.task_id}: ${record.execution_status} (${record.progress_percentage}%)`);
    });

    // 更新该设备所有正在执行的小红书任务状态
    console.log(`📋 [停止设备任务] 更新执行中的任务状态`);

    const [result] = await pool.execute(`
      UPDATE xiaohongshu_execution_logs
      SET execution_status = 'failed',
          progress_percentage = 0,
          error_message = ?,
          completed_at = NOW()
      WHERE device_id = ?
        AND execution_status IN ('pending', 'running')
    `, [`设备离线: ${reason}`, deviceId]);

    updatedCount = result.affectedRows;
    console.log(`📋 [停止设备任务] 更新了 ${updatedCount} 条执行日志记录`);

    // 通知手机端停止脚本
    console.log(`📋 [停止设备任务] 通知设备 ${deviceId} 停止脚本`);
    io.to(deviceId).emit('script_command', {
      type: 'stop_script',
      deviceId: deviceId,
      reason: reason
    });

    // 更新设备状态为在线（从忙碌状态恢复）
    try {
      await updateDeviceStatus(deviceId, 'online');
      console.log(`📋 [停止设备任务] 设备 ${deviceId} 状态已更新为在线`);
    } catch (error) {
      console.error(`📋 [停止设备任务] 更新设备状态失败:`, error);
    }

    res.json({
      success: true,
      message: `已停止设备 ${deviceId} 的所有任务`,
      updatedCount: updatedCount
    });

  } catch (error) {
    console.error(`📋 [停止设备任务] 失败:`, error);
    res.status(500).json({
      success: false,
      message: '停止设备任务失败: ' + error.message
    });
  }
});

// 接收设备脚本状态API
app.post('/api/device/:deviceId/script-status', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;
  const { status, reason, message } = req.body;

  console.log(`🛑 [脚本状态] 收到设备 ${deviceId} 的脚本状态: ${status}`);
  console.log(`🛑 [脚本状态] 原因: ${reason}`);
  console.log(`🛑 [脚本状态] 消息: ${message}`);

  try {
    // 通知所有Web客户端设备脚本状态变化
    io.emit('device_script_status', {
      deviceId: deviceId,
      status: status,
      reason: reason,
      message: message,
      timestamp: new Date().toISOString()
    });

    console.log(`🛑 [脚本状态] 已通知前端设备 ${deviceId} 脚本状态: ${status}`);

    res.json({
      success: true,
      message: '脚本状态已接收'
    });

  } catch (error) {
    console.error(`🛑 [脚本状态] 处理失败:`, error);
    res.status(500).json({
      success: false,
      message: '处理脚本状态失败: ' + error.message
    });
  }
});

// 接收设备应用状态API
app.post('/api/device/:deviceId/app-status', authenticateToken, async (req, res) => {
  const { deviceId } = req.params;
  const { status, reason, message } = req.body;

  console.log(`📱 [应用状态] 收到设备 ${deviceId} 的应用状态: ${status}`);
  console.log(`📱 [应用状态] 原因: ${reason}`);
  console.log(`📱 [应用状态] 消息: ${message}`);

  try {
    // 通知所有Web客户端设备应用状态变化
    io.emit('device_app_status', {
      deviceId: deviceId,
      status: status,
      reason: reason,
      message: message,
      timestamp: new Date().toISOString()
    });

    console.log(`📱 [应用状态] 已通知前端设备 ${deviceId} 应用状态: ${status}`);

    res.json({
      success: true,
      message: '应用状态已接收'
    });

  } catch (error) {
    console.error(`📱 [应用状态] 处理失败:`, error);
    res.status(500).json({
      success: false,
      message: '处理应用状态失败: ' + error.message
    });
  }
});

// 监听进程信号
process.on('SIGTERM', () => {
  console.log('🚨 收到SIGTERM信号');
  gracefulShutdown('SIGTERM').catch(err => {
    console.error('🚨 SIGTERM处理失败:', err);
    process.exit(1);
  });
});

process.on('SIGINT', () => {
  console.log('🚨 收到SIGINT信号');
  gracefulShutdown('SIGINT').catch(err => {
    console.error('🚨 SIGINT处理失败:', err);
    process.exit(1);
  });
});

process.on('SIGUSR2', () => {
  console.log('🚨 收到SIGUSR2信号');
  gracefulShutdown('SIGUSR2').catch(err => {
    console.error('🚨 SIGUSR2处理失败:', err);
    process.exit(1);
  });
});

// 生成轻量级搜索加群脚本，保持原始逻辑但减少复杂度
function generateLightweightGroupChatScript(params) {
  console.log('开始生成轻量级搜索加群脚本');

  // 从参数中提取配置
  const searchKeyword = params.searchKeyword || '私域';
  const targetJoinCount = params.targetJoinCount || 5;

  const lightweightScript = `
// ===== 轻量级搜索加群脚本 - 双向.js兼容版本 =====
// 基于原始群聊.js脚本的核心逻辑，但大幅简化以防止Auto.js崩溃
// 原始脚本：1950行，轻量级版本：约300行

// 全局变量
var searchKeyword = "${searchKeyword}";
var targetJoinCount = ${targetJoinCount};
var successfulJoinCount = 0;
var clickedButtons = [];
var isRunning = true;
var isPaused = false;

// 日志函数 - 兼容双向.js环境
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const fullMessage = '[' + timestamp + '] ' + message;
    console.log(fullMessage);

    // 使用双向.js的状态上报机制
    try {
        if (typeof sendStatusUpdate === 'function') {
            sendStatusUpdate('executing', '脚本执行中', 50, fullMessage);
        }
    } catch (e) {
        console.log('状态上报失败: ' + e.message);
    }
}

// 安全的toast函数
function toast(message) {
    addLog('Toast: ' + message);
}

// 检查暂停状态
function checkPause() {
    if (isPaused) {
        addLog('脚本已暂停');
        while (isPaused && isRunning) {
            sleep(1000);
        }
    }
    if (!isRunning) {
        addLog('脚本被终止');
        throw new Error('脚本被用户终止');
    }
}

// 更新成功计数
function updateSuccessCount() {
    successfulJoinCount++;
    addLog('成功加入群聊数: ' + successfulJoinCount + '/' + targetJoinCount);

    // 发送进度更新
    try {
        if (typeof sendStatusUpdate === 'function') {
            const progress = Math.round((successfulJoinCount / targetJoinCount) * 100);
            sendStatusUpdate('executing', '脚本执行中', progress,
                '已成功加入 ' + successfulJoinCount + ' 个群聊');
        }
    } catch (e) {
        console.log('进度更新失败: ' + e.message);
    }
}

// 启动小红书应用（简化版，按原始逻辑）
function launchXiaohongshu() {
    addLog("正在启动小红书...");

    try {
        // 按原始脚本逻辑：先按home键回到桌面
        home();
        sleep(2000);
        home();
        sleep(2000);

        // 按原始脚本逻辑：获取屏幕尺寸并计算点击坐标
        let screenWidth = device.width;
        let screenHeight = device.height;
        let clickX = screenWidth * 0.15;
        let clickY = screenHeight * 0.25;

        addLog("屏幕尺寸: " + screenWidth + " x " + screenHeight);
        addLog("点击坐标启动小红书: (" + Math.round(clickX) + ", " + Math.round(clickY) + ")");

        // 按原始脚本逻辑：点击指定坐标启动小红书
        click(clickX, clickY);
        sleep(5000);

        addLog("小红书启动成功");
        return true;

    } catch (error) {
        addLog("启动小红书失败: " + error.message);
        return false;
    }
}

// 确保在主页（简化版，按原始逻辑）
function ensureInHomePage() {
    addLog("确保在主页");

    try {
        // 按原始脚本逻辑：尝试返回主页
        for (let i = 0; i < 3; i++) {
            // 点击底部首页按钮
            let screenWidth = device.width;
            let screenHeight = device.height;
            click(screenWidth * 0.1, screenHeight * 0.95);
            sleep(2000);

            addLog("已尝试返回主页，第 " + (i + 1) + " 次");
        }

        addLog("主页确认完成");
        return true;

    } catch (error) {
        addLog("确保在主页失败: " + error.message);
        return false;
    }
}

// 打开消息页面（简化版，按原始逻辑）
function openMessagePage() {
    addLog("正在打开消息页面");

    try {
        // 按原始脚本逻辑：点击底部消息按钮
        let screenWidth = device.width;
        let screenHeight = device.height;
        click(screenWidth * 0.9, screenHeight * 0.95);
        sleep(3000);

        addLog("消息页面打开成功");
        return true;

    } catch (error) {
        addLog("打开消息页面失败: " + error.message);
        return false;
    }
}

// 打开发现群聊（简化版，按原始逻辑）
function openDiscoverGroupChat() {
    addLog("正在打开发现群聊");

    try {
        // 按原始脚本逻辑：查找并点击发现群聊按钮
        let discoverBtn = text("发现群聊").findOne(5000);
        if (discoverBtn && discoverBtn.visibleToUser()) {
            discoverBtn.click();
            sleep(3000);
            addLog("发现群聊打开成功");
            return true;
        }

        // 按原始脚本逻辑：如果找不到文本，尝试坐标点击
        let screenWidth = device.width;
        let screenHeight = device.height;
        click(screenWidth * 0.5, screenHeight * 0.3);
        sleep(3000);

        addLog("发现群聊打开成功（坐标方式）");
        return true;

    } catch (error) {
        addLog("打开发现群聊失败: " + error.message);
        return false;
    }
}

// 搜索群聊（简化版，按原始逻辑）
function searchGroupChat() {
    addLog("开始搜索群聊: " + searchKeyword);

    try {
        // 按原始脚本逻辑：点击右上角进入群聊广场
        let screenWidth = device.width;
        let screenHeight = device.height;
        click(screenWidth * 0.75, screenHeight * 0.20);
        sleep(3000);

        // 按原始脚本逻辑：查找搜索框并输入关键词
        let searchBox = id("search_view").findOne(5000) ||
                       className("android.widget.EditText").findOne(5000);

        if (searchBox) {
            searchBox.click();
            sleep(1000);
            setText(searchKeyword);
            addLog("已输入搜索关键词: " + searchKeyword);
            sleep(2000);

            // 按原始脚本逻辑：点击搜索按钮
            click(screenWidth * 0.95, screenHeight * 0.93);
            sleep(3000);

            addLog("搜索完成，开始查找加入按钮");
            return true;
        } else {
            addLog("未找到搜索框");
            return false;
        }

    } catch (error) {
        addLog("搜索群聊失败: " + error.message);
        return false;
    }
}

// 点击加入按钮（简化版，按原始逻辑）
function clickAllJoinButtons() {
    addLog("开始查找并点击加入按钮");

    try {
        let joinCount = 0;
        let maxAttempts = 10; // 限制尝试次数，防止无限循环

        for (let attempt = 0; attempt < maxAttempts && successfulJoinCount < targetJoinCount; attempt++) {
            checkPause();

            // 按原始脚本逻辑：查找加入按钮
            let joinButtons = text("加入").find();
            let availableButtons = [];

            joinButtons.forEach(btn => {
                if (btn.visibleToUser()) {
                    availableButtons.push(btn);
                }
            });

            addLog("找到 " + availableButtons.length + " 个可见的加入按钮");

            if (availableButtons.length === 0) {
                // 按原始脚本逻辑：尝试滚动查找更多
                addLog("当前页面无加入按钮，尝试滚动");
                let screenWidth = device.width;
                let screenHeight = device.height;
                swipe(screenWidth / 2, screenHeight * 0.7, screenWidth / 2, screenHeight * 0.3, 800);
                sleep(2000);
                continue;
            }

            // 按原始脚本逻辑：点击第一个可用的加入按钮
            let button = availableButtons[0];
            button.click();
            sleep(2000);

            addLog("已点击加入按钮");

            // 简化的成功检测（原始脚本有复杂的验证逻辑）
            updateSuccessCount();
            joinCount++;

            // 等待页面响应
            sleep(3000);

            if (successfulJoinCount >= targetJoinCount) {
                addLog("已达到目标加入次数");
                break;
            }
        }

        addLog("加入按钮点击完成，成功次数: " + successfulJoinCount);
        return true;

    } catch (error) {
        addLog("点击加入按钮失败: " + error.message);
        return false;
    }
}

// 主执行函数（简化版，按原始逻辑）
function executeScript() {
    addLog("=== 开始执行轻量级搜索加群脚本 ===");
    addLog("搜索关键词: " + searchKeyword);
    addLog("目标加入次数: " + targetJoinCount);

    try {
        // 发送开始状态
        if (typeof sendStatusUpdate === 'function') {
            sendStatusUpdate('executing', '脚本执行中', 10, '正在启动小红书...');
        }

        // 按原始脚本逻辑：保持屏幕常亮
        device.keepScreenOn();
        checkPause();

        // 按原始脚本逻辑：检查无障碍服务
        if (!auto.service) {
            addLog("无障碍服务未开启");
            throw new Error("无障碍服务未开启");
        }

        // 按原始脚本逻辑：启动小红书
        if (!launchXiaohongshu()) {
            throw new Error("启动小红书失败");
        }
        checkPause();

        // 按原始脚本逻辑：确保在主页
        ensureInHomePage();
        checkPause();

        // 按原始脚本逻辑：打开消息页面
        if (!openMessagePage()) {
            throw new Error("打开消息页面失败");
        }
        checkPause();

        // 按原始脚本逻辑：打开发现群聊
        if (!openDiscoverGroupChat()) {
            throw new Error("打开发现群聊失败");
        }
        checkPause();

        // 按原始脚本逻辑：搜索群聊
        if (!searchGroupChat()) {
            throw new Error("搜索群聊失败");
        }
        checkPause();

        // 按原始脚本逻辑：点击加入按钮
        clickAllJoinButtons();

        // 发送完成状态
        if (typeof sendStatusUpdate === 'function') {
            sendStatusUpdate('completed', '脚本执行完成', 100,
                '搜索加群完成，成功加入 ' + successfulJoinCount + ' 个群聊');
        }

        addLog("=== 轻量级搜索加群脚本执行完成 ===");
        addLog("最终成功次数: " + successfulJoinCount + "/" + targetJoinCount);

    } catch (error) {
        addLog("脚本执行失败: " + error.message);

        // 发送错误状态
        if (typeof sendStatusUpdate === 'function') {
            sendStatusUpdate('error', '脚本执行失败', 0, error.message);
        }

        throw error;
    }
}

// 自动执行
addLog("轻量级搜索加群脚本已加载，开始执行");
executeScript();
`;

  console.log('轻量级搜索加群脚本生成完成，长度:', lightweightScript.length);

  // 保存调试版本
  try {
    const fs = require('fs');
    fs.writeFileSync('generated-lightweight-groupchat-debug.js', lightweightScript);
    console.log('轻量级群聊调试脚本已保存');
  } catch (error) {
    console.log('保存轻量级调试脚本失败:', error.message);
  }

  return lightweightScript;
}

// 专门用于转换群聊.js脚本为无UI版本的函数
function convertGroupChatScriptToNonUI(originalScript, params) {
  console.log('开始转换群聊.js脚本为无UI版本');

  // 移除UI相关的代码
  let convertedScript = originalScript;

  // 1. 移除UI声明和布局
  convertedScript = convertedScript.replace(/"ui";[\s\S]*?ui\.layout\([\s\S]*?\);/g, '');

  // 2. 移除UI相关的导入和设置
  convertedScript = convertedScript.replace(/importClass\(android\.view\.KeyEvent\);/g, '');
  convertedScript = convertedScript.replace(/ui\.statusBarColor\([^)]*\);/g, '');

  // 3. 移除存储器相关代码（在无UI模式下不需要）
  convertedScript = convertedScript.replace(/const storage = storages\.create\([^)]*\);/g, '');
  convertedScript = convertedScript.replace(/storage\.[^;]*;/g, '');

  // 4. 移除所有UI操作
  convertedScript = convertedScript.replace(/ui\.[^;]*;/g, '');
  convertedScript = convertedScript.replace(/ui\.run\s*\([^)]*\)\s*=>\s*\{[^}]*\}\s*\);/g, '');

  // 5. 移除按钮点击事件监听
  convertedScript = convertedScript.replace(/ui\.\w+\.click\s*\([^)]*\)\s*=>\s*\{[\s\S]*?\}\s*\);/g, '');

  // 6. 移除UI更新函数调用
  convertedScript = convertedScript.replace(/updateButtonStates\(\);/g, '');
  convertedScript = convertedScript.replace(/updateServiceStatus\(\);/g, '');
  convertedScript = convertedScript.replace(/updateSuccessCount\(\);/g, '');

  // 7. 移除UI相关的全局变量初始化
  convertedScript = convertedScript.replace(/ui\.run\s*\([^)]*\)\s*=>\s*\{[\s\S]*?\}\s*\);/g, '');

  // 8. 清理孤立的括号和分号（避免语法错误）
  convertedScript = cleanOrphanedBrackets(convertedScript);

  // 8. 移除重复的全局变量声明（避免redeclaration错误）
  convertedScript = removeGlobalVariableDeclarations(convertedScript);

  // 9. 保留核心功能函数，但清理其中的UI调用
  convertedScript = cleanUICallsFromFunctions(convertedScript);

  // 10. 移除最后的UI初始化代码
  convertedScript = convertedScript.replace(/\/\/ 初始化状态[\s\S]*$/g, '');

  console.log('群聊.js脚本转换完成，转换后长度:', convertedScript.length);

  return convertedScript;
}

// 清理孤立的括号和分号（更精确的清理）
function cleanOrphanedBrackets(script) {
  console.log('开始清理孤立的括号和分号');

  let cleanedScript = script;

  // 只移除明确孤立的UI相关代码片段
  // 移除ui.run相关的残留代码
  cleanedScript = cleanedScript.replace(/\s+\}\s*\)\s*;\s*\n\s*console\.log/g, '\n    console.log');

  // 修复addLog函数中可能的语法问题
  cleanedScript = cleanedScript.replace(/logMessages\.shift\(\);\s*\n\s*\}\s*\)\s*;\s*\n\s*console\.log/g,
    'logMessages.shift();\n    }\n    console.log');

  // 修复函数定义中缺少的大括号
  cleanedScript = cleanedScript.replace(/function\s+(\w+)\s*\(\s*\)\s*\n/g, 'function $1() {\n');

  // 修复函数定义后的孤立括号
  cleanedScript = cleanedScript.replace(/(function\s+updateServiceStatus\(\)\s*\{\s*\/\/[^\n]*\n\s*\})\s*\}\s*\)\s*;\s*\n\s*\}/g, '$1');
  cleanedScript = cleanedScript.replace(/(function\s+updateSuccessCount\(\)\s*\{\s*\/\/[^\n]*\n\s*\})\s*\}\s*\)\s*;\s*\n\s*\}/g, '$1');

  // 移除函数定义后的孤立代码块
  cleanedScript = cleanedScript.replace(/(\}\s*)\s*\}\s*\)\s*;\s*\n\s*\}/g, '$1');

  // 移除单独行的孤立括号（更强力的清理）
  cleanedScript = cleanedScript.replace(/^\s*\}\s*\)\s*;\s*$/gm, '');
  cleanedScript = cleanedScript.replace(/^\s*\}\s*$/gm, '');
  cleanedScript = cleanedScript.replace(/^\s*\)\s*;\s*$/gm, '');

  // 特别处理函数定义后的孤立代码
  cleanedScript = cleanedScript.replace(/(function\s+\w+\(\)\s*\{\s*\/\/[^\n]*\n\s*\})\s*\n\s*\}\s*\)\s*;\s*\n\s*\}/g, '$1');

  // 清理连续的孤立括号
  cleanedScript = cleanedScript.replace(/\n\s*\}\s*\)\s*;\s*\n\s*\}/g, '');
  cleanedScript = cleanedScript.replace(/\n\s*\}\s*\n/g, '\n');
  cleanedScript = cleanedScript.replace(/\n\s*\)\s*;\s*\n/g, '\n');

  // 移除多余的空行（连续超过3个空行的情况）
  cleanedScript = cleanedScript.replace(/\n\s*\n\s*\n\s*\n/g, '\n\n');

  console.log('孤立括号和分号清理完成');
  return cleanedScript;
}

// 移除重复的全局变量声明
function removeGlobalVariableDeclarations(script) {
  console.log('开始移除重复的全局变量声明');

  let cleanedScript = script;

  // 移除原始脚本中的全局变量声明块（因为我们在参数注入代码中已经声明了）
  cleanedScript = cleanedScript.replace(/\/\/ 全局变量[\s\S]*?let clickedButtons = \[\]; \/\/ 记录已点击过的按钮/g, '');

  // 移除单独的变量声明行
  const variablesToRemove = [
    'isRunning',
    'isPaused',
    'isStopped',
    'logMessages',
    'currentThread',
    'successfulJoinCount',
    'targetJoinCount',
    'hasExecuted',
    'clickedButtons'
  ];

  variablesToRemove.forEach(varName => {
    // 移除 let/var/const 声明
    const letPattern = new RegExp(`let\\s+${varName}\\s*=.*?;`, 'g');
    const varPattern = new RegExp(`var\\s+${varName}\\s*=.*?;`, 'g');
    const constPattern = new RegExp(`const\\s+${varName}\\s*=.*?;`, 'g');

    cleanedScript = cleanedScript.replace(letPattern, '');
    cleanedScript = cleanedScript.replace(varPattern, '');
    cleanedScript = cleanedScript.replace(constPattern, '');
  });

  console.log('全局变量声明清理完成');
  return cleanedScript;
}

// 清理函数中的UI调用（更安全的方式）
function cleanUICallsFromFunctions(script) {
  console.log('开始清理函数中的UI调用');

  let cleanedScript = script;

  // 只清理明确的UI操作，不破坏函数结构
  // 清理ui.run相关的代码
  cleanedScript = cleanedScript.replace(/ui\.run\s*\([^)]*\)\s*=>\s*\{[^}]*\}\s*\);?/g, '');

  // 清理ui.xxx相关的调用
  cleanedScript = cleanedScript.replace(/ui\.[a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\);?/g, '');
  cleanedScript = cleanedScript.replace(/ui\.[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\);?/g, '');

  // 保持函数结构完整，只替换函数体内容
  cleanedScript = cleanedScript.replace(
    /(function updateButtonStates\(\)\s*\{)[\s\S]*?(\n\})/g,
    '$1\n    // 无UI模式下不执行任何操作$2'
  );

  cleanedScript = cleanedScript.replace(
    /(function updateServiceStatus\(\)\s*\{)[\s\S]*?(\n\})/g,
    '$1\n    // 无UI模式下不执行任何操作$2'
  );

  cleanedScript = cleanedScript.replace(
    /(function updateSuccessCount\(\)\s*\{)[\s\S]*?(\n\})/g,
    '$1\n    // 无UI模式下不执行任何操作$2'
  );

  console.log('函数UI调用清理完成');
  return cleanedScript;
}

// Vue Router History模式支持 - 必须放在所有路由的最后
app.get('*', (req, res, next) => {
  // 如果请求的是API路径，跳过
  if (req.path.startsWith('/api/') || req.path.startsWith('/socket.io/')) {
    return next();
  }

  // 如果请求的是静态文件（有文件扩展名），跳过
  if (path.extname(req.path)) {
    return next();
  }

  // 返回Vue应用的index.html
  const indexPath = path.join(__dirname, 'web/dist/index.html');
  if (fs.existsSync(indexPath)) {
    console.log(`Vue Router: 返回index.html for路径: ${req.path}`);
    res.sendFile(indexPath);
  } else {
    res.status(404).send('Vue应用未找到，请先构建前端应用');
  }
});

module.exports = { app, io };
