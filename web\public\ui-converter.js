/**
 * Auto.js UI转换器
 * 将Auto.js的UI XML布局转换为HTML界面
 * 用于在Web端预览和配置Auto.js脚本的UI界面
 */

class AutoJsUIConverter {
  constructor() {
    this.config = {};
    this.eventHandlers = {};
  }

  /**
   * 将Auto.js UI XML转换为HTML
   * @param {string} xmlString - Auto.js UI XML字符串
   * @returns {Object} 包含html、css、js和config的对象
   */
  convertXMLToHTML(xmlString) {
    try {
      console.log('开始转换UI XML:', xmlString.substring(0, 200) + '...');
      
      // 解析XML
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
      
      // 检查解析错误
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        throw new Error('XML解析失败: ' + parseError.textContent);
      }
      
      // 转换为HTML
      const htmlResult = this.convertElement(xmlDoc.documentElement);
      
      // 生成CSS
      const css = this.generateCSS();
      
      // 生成JavaScript
      const js = this.generateJS();
      
      console.log('UI转换完成，配置项数量:', Object.keys(this.config).length);
      
      return {
        html: htmlResult,
        css: css,
        js: js,
        config: this.config
      };
    } catch (error) {
      console.error('UI转换失败:', error);
      return {
        html: `<div class="ui-error">UI转换失败: ${error.message}</div>`,
        css: '.ui-error { color: red; padding: 20px; border: 1px solid red; }',
        js: '',
        config: {}
      };
    }
  }

  /**
   * 转换XML元素为HTML
   * @param {Element} element - XML元素
   * @returns {string} HTML字符串
   */
  convertElement(element) {
    if (!element || element.nodeType !== 1) {
      return '';
    }

    const tagName = element.tagName.toLowerCase();
    let html = '';

    switch (tagName) {
      case 'frame':
      case 'vertical':
        html = this.convertContainer(element, 'div', 'vertical-container');
        break;
      case 'horizontal':
        html = this.convertContainer(element, 'div', 'horizontal-container');
        break;
      case 'linear':
        html = this.convertContainer(element, 'div', 'linear-container');
        break;
      case 'appbar':
        html = this.convertAppBar(element);
        break;
      case 'toolbar':
        html = this.convertToolbar(element);
        break;
      case 'text':
        html = this.convertText(element);
        break;
      case 'input':
        html = this.convertInput(element);
        break;
      case 'button':
        html = this.convertButton(element);
        break;
      case 'card':
        html = this.convertCard(element);
        break;
      case 'scroll':
        html = this.convertScroll(element);
        break;
      default:
        html = this.convertGeneric(element);
        break;
    }

    return html;
  }

  /**
   * 转换容器元素
   */
  convertContainer(element, htmlTag, className) {
    const id = element.getAttribute('id') || '';
    const children = Array.from(element.children).map(child => this.convertElement(child)).join('');
    
    return `<${htmlTag} class="${className}" ${id ? `id="${id}"` : ''}>${children}</${htmlTag}>`;
  }

  /**
   * 转换AppBar
   */
  convertAppBar(element) {
    const children = Array.from(element.children).map(child => this.convertElement(child)).join('');
    return `<div class="app-bar">${children}</div>`;
  }

  /**
   * 转换Toolbar
   */
  convertToolbar(element) {
    const title = element.getAttribute('title') || '';
    const bg = element.getAttribute('bg') || '#409EFF';
    const id = element.getAttribute('id') || '';
    
    return `<div class="toolbar" ${id ? `id="${id}"` : ''} style="background-color: ${bg};">
      <h3 class="toolbar-title">${title}</h3>
    </div>`;
  }

  /**
   * 转换文本元素
   */
  convertText(element) {
    const text = element.getAttribute('text') || element.textContent || '';
    const textSize = element.getAttribute('textSize') || '14sp';
    const textColor = element.getAttribute('textColor') || '#333';
    const id = element.getAttribute('id') || '';
    
    const fontSize = this.convertSize(textSize);
    
    return `<div class="text-element" ${id ? `id="${id}"` : ''} style="font-size: ${fontSize}; color: ${textColor};">${text}</div>`;
  }

  /**
   * 转换输入框
   */
  convertInput(element) {
    const id = element.getAttribute('id') || '';
    const hint = element.getAttribute('hint') || '';
    const text = element.getAttribute('text') || '';
    const inputType = element.getAttribute('inputType') || 'text';
    
    // 保存配置
    if (id) {
      this.config[id] = {
        type: 'input',
        value: text,
        placeholder: hint,
        inputType: inputType
      };
    }
    
    let htmlInputType = 'text';
    if (inputType === 'number') htmlInputType = 'number';
    if (inputType === 'textPassword') htmlInputType = 'password';
    
    return `<div class="input-group">
      ${hint ? `<label class="input-label">${hint}</label>` : ''}
      <input type="${htmlInputType}" class="input-element" ${id ? `id="${id}"` : ''} 
             value="${text}" placeholder="${hint}" 
             onchange="window.uiConverter && window.uiConverter.updateConfig('${id}', this.value)">
    </div>`;
  }

  /**
   * 转换按钮
   */
  convertButton(element) {
    const id = element.getAttribute('id') || '';
    const text = element.getAttribute('text') || element.textContent || '按钮';
    const style = element.getAttribute('style') || '';
    
    // 保存配置
    if (id) {
      this.config[id] = {
        type: 'button',
        text: text,
        style: style
      };
    }
    
    return `<button class="button-element ${style}" ${id ? `id="${id}"` : ''} 
            onclick="window.uiConverter && window.uiConverter.handleClick('${id}')">${text}</button>`;
  }

  /**
   * 转换卡片
   */
  convertCard(element) {
    const id = element.getAttribute('id') || '';
    const children = Array.from(element.children).map(child => this.convertElement(child)).join('');
    
    return `<div class="card-element" ${id ? `id="${id}"` : ''}>${children}</div>`;
  }

  /**
   * 转换滚动容器
   */
  convertScroll(element) {
    const id = element.getAttribute('id') || '';
    const children = Array.from(element.children).map(child => this.convertElement(child)).join('');
    
    return `<div class="scroll-container" ${id ? `id="${id}"` : ''}>${children}</div>`;
  }

  /**
   * 转换通用元素
   */
  convertGeneric(element) {
    const id = element.getAttribute('id') || '';
    const children = Array.from(element.children).map(child => this.convertElement(child)).join('');
    
    return `<div class="generic-element" ${id ? `id="${id}"` : ''}>${children}</div>`;
  }

  /**
   * 转换尺寸单位
   */
  convertSize(size) {
    if (typeof size !== 'string') return '14px';
    
    if (size.endsWith('sp')) {
      return parseInt(size) + 'px';
    }
    if (size.endsWith('dp')) {
      return parseInt(size) + 'px';
    }
    if (size.endsWith('px')) {
      return size;
    }
    
    return size + 'px';
  }

  /**
   * 生成CSS样式
   */
  generateCSS() {
    return `
      .vertical-container { display: flex; flex-direction: column; }
      .horizontal-container { display: flex; flex-direction: row; }
      .linear-container { display: flex; }
      .app-bar { background-color: #409EFF; color: white; }
      .toolbar { padding: 10px; background-color: #409EFF; color: white; }
      .toolbar-title { margin: 0; font-size: 18px; }
      .text-element { margin: 5px 0; }
      .input-group { margin: 10px 0; }
      .input-label { display: block; margin-bottom: 5px; font-weight: bold; }
      .input-element { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
      .button-element { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; background-color: #409EFF; color: white; cursor: pointer; }
      .button-element:hover { background-color: #66b1ff; }
      .card-element { border: 1px solid #eee; border-radius: 4px; padding: 15px; margin: 10px 0; }
      .scroll-container { max-height: 300px; overflow-y: auto; }
      .generic-element { margin: 5px 0; }
      .ui-error { color: red; padding: 20px; border: 1px solid red; border-radius: 4px; }
    `;
  }

  /**
   * 生成JavaScript代码
   */
  generateJS() {
    return `
      // 设置全局UI转换器引用
      window.uiConverter = window.AutoJsUIConverter ? new window.AutoJsUIConverter() : null;
      
      // 更新配置函数
      if (window.uiConverter) {
        window.uiConverter.updateConfig = function(id, value) {
          if (this.config[id]) {
            this.config[id].value = value;
            console.log('配置更新:', id, '=', value);
          }
        };
        
        // 处理按钮点击
        window.uiConverter.handleClick = function(id) {
          console.log('按钮点击:', id);
          if (this.eventHandlers[id]) {
            this.eventHandlers[id]();
          }
        };
      }
    `;
  }

  /**
   * 更新配置
   */
  updateConfig(id, value) {
    if (this.config[id]) {
      this.config[id].value = value;
      console.log('配置更新:', id, '=', value);
    }
  }

  /**
   * 处理点击事件
   */
  handleClick(id) {
    console.log('按钮点击:', id);
    if (this.eventHandlers[id]) {
      this.eventHandlers[id]();
    }
  }

  /**
   * 获取配置
   */
  getConfig() {
    return this.config;
  }
}

// 将类暴露到全局
window.AutoJsUIConverter = AutoJsUIConverter;

console.log('✅ Auto.js UI转换器已加载');
