/**
 * 创建主站数据库脚本
 * 用于在本地创建测试主站数据库
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'zhuzhan',
  password: 'root',
  database: 'zhuzhan',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 主站数据库名称
const MAIN_DB_NAME = 'zhuzhan';

async function createMainDatabase() {
  let connection = null;
  
  try {
    console.log('🚀 开始创建主站数据库...');
    
    // 1. 连接到MySQL服务器
    console.log('📡 连接到MySQL服务器...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ MySQL连接成功');
    
    // 2. 连接到主站数据库
    console.log(`📊 连接到主站数据库 ${MAIN_DB_NAME}...`);
    console.log('✅ 主站数据库连接成功');
    
    // 3. 创建主站用户表
    console.log('👥 创建主站用户表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS main_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
        email VARCHAR(100) COMMENT '邮箱地址',
        phone VARCHAR(20) COMMENT '手机号码',
        real_name VARCHAR(50) COMMENT '真实姓名',
        status ENUM('active', 'disabled', 'suspended') DEFAULT 'active' COMMENT '账号状态',
        role ENUM('user', 'admin', 'super_admin') DEFAULT 'user' COMMENT '用户角色',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
        login_count INT DEFAULT 0 COMMENT '登录次数',
        
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主站用户表'
    `);
    console.log('✅ 主站用户表创建成功');
    
    // 4. 创建卡密/激活码表
    console.log('🎫 创建卡密/激活码表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS activation_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(100) UNIQUE NOT NULL COMMENT '卡密/激活码',
        type ENUM('card', 'activation', 'trial') DEFAULT 'card' COMMENT '类型：卡密/激活码/试用',
        duration_days INT NOT NULL COMMENT '时效天数',
        max_uses INT DEFAULT 1 COMMENT '最大使用次数',
        used_count INT DEFAULT 0 COMMENT '已使用次数',
        status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active' COMMENT '状态',
        description TEXT COMMENT '描述信息',
        batch_id VARCHAR(50) COMMENT '批次ID',
        created_by INT COMMENT '创建者用户ID',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        expires_at TIMESTAMP NULL COMMENT '卡密过期时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_code (code),
        INDEX idx_type (type),
        INDEX idx_status (status),
        INDEX idx_batch_id (batch_id),
        INDEX idx_created_by (created_by),
        INDEX idx_expires_at (expires_at),
        INDEX idx_created_at (created_at),
        
        FOREIGN KEY (created_by) REFERENCES main_users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密激活码表'
    `);
    console.log('✅ 卡密/激活码表创建成功');
    
    // 5. 创建用户激活记录表
    console.log('📝 创建用户激活记录表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_activations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL COMMENT '用户ID',
        activation_code_id INT NOT NULL COMMENT '激活码ID',
        activation_code VARCHAR(100) NOT NULL COMMENT '激活码（冗余存储）',
        activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '激活时间',
        expires_at TIMESTAMP NOT NULL COMMENT '用户时效过期时间',
        duration_days INT NOT NULL COMMENT '激活天数',
        ip_address VARCHAR(45) COMMENT '激活IP地址',
        user_agent TEXT COMMENT '用户代理信息',
        notes TEXT COMMENT '备注信息',
        
        INDEX idx_user_id (user_id),
        INDEX idx_activation_code_id (activation_code_id),
        INDEX idx_activation_code (activation_code),
        INDEX idx_activated_at (activated_at),
        INDEX idx_expires_at (expires_at),
        
        FOREIGN KEY (user_id) REFERENCES main_users(id) ON DELETE CASCADE,
        FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户激活记录表'
    `);
    console.log('✅ 用户激活记录表创建成功');
    
    // 6. 创建系统配置表
    console.log('⚙️ 创建系统配置表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
        config_value TEXT COMMENT '配置值',
        config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
        description TEXT COMMENT '配置描述',
        is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开配置',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        
        INDEX idx_config_key (config_key),
        INDEX idx_is_public (is_public)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
    `);
    console.log('✅ 系统配置表创建成功');
    
    // 7. 插入初始数据
    console.log('📊 插入初始数据...');
    
    // 创建默认超级管理员账号
    const hashedPassword = await bcrypt.hash('password', 10);
    await connection.execute(`
      INSERT IGNORE INTO main_users (username, password, email, role, status) VALUES 
      ('admin', ?, '<EMAIL>', 'super_admin', 'active'),
      ('testuser', ?, '<EMAIL>', 'user', 'active')
    `, [hashedPassword, hashedPassword]);
    
    // 创建测试卡密
    await connection.execute(`
      INSERT IGNORE INTO activation_codes (code, type, duration_days, max_uses, description, batch_id, created_by) VALUES
      ('TEST-30DAY-001', 'card', 30, 1, '测试卡密-30天', 'TEST_BATCH_001', 1),
      ('TEST-7DAY-001', 'trial', 7, 1, '测试试用卡密-7天', 'TEST_BATCH_001', 1),
      ('TEST-365DAY-001', 'card', 365, 1, '测试卡密-365天', 'TEST_BATCH_001', 1),
      ('MULTI-USE-001', 'card', 30, 5, '多次使用测试卡密-30天', 'TEST_BATCH_002', 1),
      ('EXPIRED-001', 'card', 30, 1, '已过期测试卡密', 'TEST_BATCH_003', 1)
    `);
    
    // 设置一个卡密为过期状态（用于测试）
    await connection.execute(`
      UPDATE activation_codes SET 
        expires_at = DATE_SUB(NOW(), INTERVAL 1 DAY),
        status = 'expired'
      WHERE code = 'EXPIRED-001'
    `);
    
    // 插入系统默认配置
    await connection.execute(`
      INSERT IGNORE INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
      ('system_name', 'Auto.js云群控系统', 'string', '系统名称', TRUE),
      ('system_version', '2.0.0', 'string', '系统版本', TRUE),
      ('default_trial_days', '7', 'number', '默认试用天数', FALSE),
      ('max_devices_per_user', '50', 'number', '每用户最大设备数', FALSE),
      ('enable_registration', 'false', 'boolean', '是否开放注册', FALSE),
      ('maintenance_mode', 'false', 'boolean', '维护模式', TRUE)
    `);
    
    console.log('✅ 初始数据插入成功');
    
    // 8. 创建视图
    console.log('👁️ 创建统计视图...');
    
    // 用户激活统计视图
    await connection.execute(`
      CREATE OR REPLACE VIEW user_activation_stats AS
      SELECT 
        u.id as user_id,
        u.username,
        u.email,
        u.status as user_status,
        COUNT(ua.id) as activation_count,
        MAX(ua.expires_at) as latest_expires_at,
        MIN(ua.activated_at) as first_activated_at,
        SUM(ua.duration_days) as total_duration_days
      FROM main_users u
      LEFT JOIN user_activations ua ON u.id = ua.user_id
      GROUP BY u.id, u.username, u.email, u.status
    `);
    
    // 卡密使用统计视图
    await connection.execute(`
      CREATE OR REPLACE VIEW activation_code_stats AS
      SELECT 
        ac.id,
        ac.code,
        ac.type,
        ac.duration_days,
        ac.max_uses,
        ac.used_count,
        ac.status,
        ac.batch_id,
        COUNT(ua.id) as actual_uses,
        GROUP_CONCAT(DISTINCT u.username) as used_by_users
      FROM activation_codes ac
      LEFT JOIN user_activations ua ON ac.id = ua.activation_code_id
      LEFT JOIN main_users u ON ua.user_id = u.id
      GROUP BY ac.id
    `);
    
    console.log('✅ 统计视图创建成功');
    
    // 9. 显示创建结果
    console.log('\n🎉 主站数据库表创建完成！');
    console.log('=====================================');
    console.log(`📊 数据库名称: ${MAIN_DB_NAME}`);
    console.log('📋 创建的表:');
    console.log('  - main_users (主站用户表)');
    console.log('  - activation_codes (卡密激活码表)');
    console.log('  - user_activations (用户激活记录表)');
    console.log('  - system_config (系统配置表)');
    console.log('');
    console.log('👤 测试账号:');
    console.log('  - 管理员: admin / password');
    console.log('  - 普通用户: testuser / password');
    console.log('');
    console.log('🎫 测试卡密:');
    console.log('  - TEST-30DAY-001 (30天有效期)');
    console.log('  - TEST-7DAY-001 (7天试用期)');
    console.log('  - TEST-365DAY-001 (365天有效期)');
    console.log('  - MULTI-USE-001 (30天，可用5次)');
    console.log('  - EXPIRED-001 (已过期，用于测试)');
    console.log('');
    console.log('🔧 下一步:');
    console.log('  1. 更新 .env 文件中的主站数据库配置');
    console.log('  2. 执行数据迁移: node database/migration-script.js migrate');
    console.log('  3. 启动服务器进行测试');
    console.log('=====================================');
    
  } catch (error) {
    console.error('❌ 创建主站数据库失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('📡 数据库连接已关闭');
    }
  }
}

// 删除主站数据库（用于重置测试）
async function dropMainDatabase() {
  let connection = null;
  
  try {
    console.log('🗑️ 开始删除主站数据库...');
    
    connection = await mysql.createConnection(dbConfig);
    await connection.execute(`DROP DATABASE IF EXISTS ${MAIN_DB_NAME}`);
    
    console.log(`✅ 数据库 ${MAIN_DB_NAME} 删除成功`);
    
  } catch (error) {
    console.error('❌ 删除主站数据库失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 检查主站数据库状态
async function checkMainDatabase() {
  let connection = null;
  
  try {
    console.log('🔍 检查主站数据库状态...');
    
    connection = await mysql.createConnection(dbConfig);
    
    // 检查数据库是否存在
    const [databases] = await connection.execute(`SHOW DATABASES LIKE '${MAIN_DB_NAME}'`);
    
    if (databases.length === 0) {
      console.log(`❌ 数据库 ${MAIN_DB_NAME} 不存在`);
      return false;
    }
    
    // 切换到数据库
    await connection.execute(`USE ${MAIN_DB_NAME}`);
    
    // 检查表是否存在
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 数据库 ${MAIN_DB_NAME} 存在，包含 ${tables.length} 个表:`);
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    // 检查用户数量
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM main_users');
    console.log(`👥 用户数量: ${userCount[0].count}`);
    
    // 检查卡密数量
    const [codeCount] = await connection.execute('SELECT COUNT(*) as count FROM activation_codes');
    console.log(`🎫 卡密数量: ${codeCount[0].count}`);
    
    console.log('✅ 主站数据库状态正常');
    return true;
    
  } catch (error) {
    console.error('❌ 检查主站数据库失败:', error);
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 命令行执行
async function main() {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'create':
        await createMainDatabase();
        break;
      case 'drop':
        await dropMainDatabase();
        break;
      case 'check':
        await checkMainDatabase();
        break;
      case 'reset':
        await dropMainDatabase();
        await createMainDatabase();
        break;
      default:
        console.log('使用方法:');
        console.log('  node create-main-database.js create  - 创建主站数据库');
        console.log('  node create-main-database.js drop    - 删除主站数据库');
        console.log('  node create-main-database.js check   - 检查数据库状态');
        console.log('  node create-main-database.js reset   - 重置数据库（删除后重新创建）');
        break;
    }
  } catch (error) {
    console.error('执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  createMainDatabase,
  dropMainDatabase,
  checkMainDatabase
};
