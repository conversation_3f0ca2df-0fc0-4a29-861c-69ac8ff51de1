-- Auto.js云群控系统数据库初始化脚本
-- 使用方法: mysql -u root -p < 初始化数据库.sql

-- 创建数据库
CREATE DATABASE IF NOT EXISTS autojs_control CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE autojs_control;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 设备表
CREATE TABLE IF NOT EXISTS devices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) UNIQUE NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  device_info JSON,
  status ENUM('online', 'offline', 'busy') DEFAULT 'offline',
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  user_id INT DEFAULT NULL COMMENT '所属用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 设备应用信息表
CREATE TABLE IF NOT EXISTS device_apps (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  app_type ENUM('xiaohongshu', 'xianyu') NOT NULL,
  app_name VARCHAR(200) NOT NULL,
  app_text VARCHAR(500) NOT NULL,
  app_bounds VARCHAR(200),
  is_clickable BOOLEAN DEFAULT FALSE,
  detection_method VARCHAR(50) NOT NULL,
  detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (device_id) REFERENCES devices(device_id) ON DELETE CASCADE,
  INDEX idx_device_id (device_id),
  INDEX idx_app_type (app_type),
  INDEX idx_detected_at (detected_at)
);

-- 脚本表
CREATE TABLE IF NOT EXISTS scripts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  content LONGTEXT NOT NULL,
  version VARCHAR(20) DEFAULT '1.0.0',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 执行日志表
CREATE TABLE IF NOT EXISTS execution_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  script_id INT,
  command TEXT NOT NULL,
  result TEXT,
  status ENUM('pending', 'running', 'success', 'error') DEFAULT 'pending',
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  INDEX idx_device_id (device_id),
  INDEX idx_script_id (script_id),
  FOREIGN KEY (script_id) REFERENCES scripts(id) ON DELETE SET NULL
);

-- 文件传输记录表
CREATE TABLE IF NOT EXISTS file_transfers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT DEFAULT 0,
  transfer_type ENUM('upload', 'download') NOT NULL,
  status ENUM('pending', 'transferring', 'completed', 'failed') DEFAULT 'pending',
  video_filename VARCHAR(255) DEFAULT '' COMMENT '视频文件名',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  INDEX idx_device_id (device_id),
  INDEX idx_video_filename (video_filename)
);

-- 小红书自动化执行日志表
CREATE TABLE IF NOT EXISTS xiaohongshu_execution_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(100) UNIQUE NOT NULL,
  function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish') NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  function_name VARCHAR(100),
  config_params JSON NOT NULL,
  schedule_config JSON,
  execution_status ENUM('pending', 'running', 'completed', 'failed', 'stopped') DEFAULT 'pending',
  progress_percentage INT DEFAULT 0,
  status_message VARCHAR(500),
  execution_result TEXT,
  execution_logs LONGTEXT,
  error_message TEXT,
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  execution_duration INT DEFAULT 0,
  INDEX idx_task_id (task_id),
  INDEX idx_device_id (device_id),
  INDEX idx_function_type (function_type),
  INDEX idx_execution_status (execution_status),
  INDEX idx_started_at (started_at)
);

-- 小红书UID存储表
CREATE TABLE IF NOT EXISTS xiaohongshu_uids (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uid VARCHAR(50) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_used BOOLEAN DEFAULT FALSE,
  used_time TIMESTAMP NULL,
  used_device_id VARCHAR(100) NULL,
  notes TEXT,
  INDEX idx_uid (uid),
  INDEX idx_file_name (file_name),
  INDEX idx_is_used (is_used),
  INDEX idx_upload_time (upload_time)
);

-- 小红书手动输入UID私信记录表
CREATE TABLE IF NOT EXISTS xiaohongshu_manual_uid_messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(100) NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  uid VARCHAR(50) NOT NULL,
  message_content TEXT NOT NULL,
  send_status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
  error_message TEXT,
  send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_task_id (task_id),
  INDEX idx_device_id (device_id),
  INDEX idx_uid (uid),
  INDEX idx_send_status (send_status),
  INDEX idx_send_time (send_time)
);

-- 小红书文件上传UID私信记录表
CREATE TABLE IF NOT EXISTS xiaohongshu_file_uid_messages (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(100) NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  uid VARCHAR(50) NOT NULL,
  message_content TEXT NOT NULL,
  send_status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
  error_message TEXT,
  send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  file_id INT,
  INDEX idx_task_id (task_id),
  INDEX idx_device_id (device_id),
  INDEX idx_uid (uid),
  INDEX idx_send_status (send_status),
  INDEX idx_send_time (send_time),
  INDEX idx_file_id (file_id)
);

-- 闲鱼自动化执行日志表
CREATE TABLE IF NOT EXISTS xianyu_execution_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(100) UNIQUE NOT NULL,
  function_type ENUM('keywordMessage') NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  function_name VARCHAR(100) NOT NULL,
  selected_app VARCHAR(200),
  config_params JSON NOT NULL,
  schedule_config JSON,
  execution_status ENUM('pending', 'running', 'completed', 'failed', 'stopped') DEFAULT 'pending',
  progress_percentage INT DEFAULT 0,
  status_message VARCHAR(500),
  execution_result TEXT,
  execution_logs LONGTEXT,
  error_message TEXT,
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  execution_duration INT DEFAULT 0,
  INDEX idx_task_id (task_id),
  INDEX idx_device_id (device_id),
  INDEX idx_function_type (function_type),
  INDEX idx_execution_status (execution_status),
  INDEX idx_started_at (started_at)
);

-- 闲鱼私聊记录表
CREATE TABLE IF NOT EXISTS xianyu_chat_records (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  keyword VARCHAR(200) NOT NULL,
  post_title VARCHAR(500),
  post_price VARCHAR(100),
  post_location VARCHAR(200),
  seller_name VARCHAR(200),
  message_content TEXT NOT NULL,
  chat_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  post_url VARCHAR(1000),
  post_id VARCHAR(200),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_device_id (device_id),
  INDEX idx_keyword (keyword),
  INDEX idx_chat_time (chat_time),
  INDEX idx_created_at (created_at)
);

-- UID文件管理表
CREATE TABLE IF NOT EXISTS uid_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT DEFAULT 0,
  total_uid_count INT DEFAULT 0,
  uploaded_by VARCHAR(100) NOT NULL,
  upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('active', 'deleted') DEFAULT 'active',
  INDEX idx_file_name (file_name),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_upload_time (upload_time),
  INDEX idx_status (status)
);

-- UID数据表
CREATE TABLE IF NOT EXISTS uid_data (
  id INT AUTO_INCREMENT PRIMARY KEY,
  file_id INT NOT NULL,
  uid VARCHAR(100) NOT NULL,
  is_used BOOLEAN DEFAULT FALSE,
  used_time TIMESTAMP NULL,
  used_device_id VARCHAR(100) NULL,
  used_device_name VARCHAR(100) NULL,
  task_id VARCHAR(100) NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_file_id (file_id),
  INDEX idx_uid (uid),
  INDEX idx_is_used (is_used),
  INDEX idx_used_device_id (used_device_id),
  INDEX idx_task_id (task_id),
  FOREIGN KEY (file_id) REFERENCES uid_files(id) ON DELETE CASCADE
);

-- 小红书视频文件管理表
CREATE TABLE IF NOT EXISTS xiaohongshu_video_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size BIGINT DEFAULT 0,
  file_hash VARCHAR(32) DEFAULT '' COMMENT '文件MD5哈希值',
  video_duration INT DEFAULT 0 COMMENT '视频时长(秒)',
  video_format VARCHAR(20) DEFAULT '' COMMENT '视频格式(mp4, avi等)',
  video_resolution VARCHAR(20) DEFAULT '' COMMENT '视频分辨率',
  thumbnail_path VARCHAR(500) DEFAULT '' COMMENT '缩略图路径',
  uploaded_by VARCHAR(100) NOT NULL,
  upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('active', 'deleted') DEFAULT 'active',
  description TEXT COMMENT '视频描述',
  tags VARCHAR(500) DEFAULT '' COMMENT '视频标签',
  transfer_count INT DEFAULT 0 COMMENT '传输次数',
  transferred_devices JSON COMMENT '已传输的设备列表',
  last_transfer_time TIMESTAMP NULL COMMENT '最后传输时间',
  INDEX idx_file_name (file_name),
  INDEX idx_uploaded_by (uploaded_by),
  INDEX idx_upload_time (upload_time),
  INDEX idx_status (status),
  INDEX idx_video_format (video_format),
  INDEX idx_file_hash (file_hash),
  INDEX idx_transfer_count (transfer_count),
  UNIQUE KEY unique_file_hash (file_hash, file_size)
);

-- 小红书视频传输记录表
CREATE TABLE IF NOT EXISTS xiaohongshu_video_transfers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  video_id INT NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  transfer_type ENUM('manual', 'script_execution') DEFAULT 'manual' COMMENT '传输类型：手动传输或脚本执行传输',
  task_id VARCHAR(100) DEFAULT '' COMMENT '关联的任务ID',
  transfer_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('pending', 'transferring', 'completed', 'failed') DEFAULT 'pending',
  transfer_progress INT DEFAULT 0 COMMENT '传输进度百分比',
  error_message TEXT COMMENT '错误信息',
  completed_time TIMESTAMP NULL,
  file_size BIGINT DEFAULT 0 COMMENT '传输文件大小',
  transfer_speed DECIMAL(10,2) DEFAULT 0 COMMENT '传输速度(KB/s)',
  video_filename VARCHAR(255) DEFAULT '' COMMENT '视频文件名',
  INDEX idx_video_id (video_id),
  INDEX idx_device_id (device_id),
  INDEX idx_task_id (task_id),
  INDEX idx_status (status),
  INDEX idx_transfer_time (transfer_time),
  INDEX idx_transfer_type (transfer_type),
  INDEX idx_video_filename (video_filename),
  FOREIGN KEY (video_id) REFERENCES xiaohongshu_video_files(id) ON DELETE CASCADE
);

-- 小红书视频分配记录表
CREATE TABLE IF NOT EXISTS xiaohongshu_video_assignments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  video_id INT NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  task_id VARCHAR(100) NOT NULL,
  assignment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('assigned', 'uploading', 'completed', 'failed') DEFAULT 'assigned',
  upload_progress INT DEFAULT 0 COMMENT '上传进度百分比',
  error_message TEXT COMMENT '错误信息',
  completed_time TIMESTAMP NULL,
  video_title VARCHAR(200) DEFAULT '' COMMENT '发布时的视频标题',
  video_description TEXT COMMENT '发布时的视频描述',
  publish_result JSON COMMENT '发布结果详情',
  INDEX idx_video_id (video_id),
  INDEX idx_device_id (device_id),
  INDEX idx_task_id (task_id),
  INDEX idx_status (status),
  INDEX idx_assignment_time (assignment_time),
  FOREIGN KEY (video_id) REFERENCES xiaohongshu_video_files(id) ON DELETE CASCADE
);

-- 小红书视频发布执行日志表
CREATE TABLE IF NOT EXISTS xiaohongshu_video_execution_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_id VARCHAR(100) NOT NULL,
  device_id VARCHAR(100) NOT NULL,
  device_name VARCHAR(100) NOT NULL,
  function_type VARCHAR(50) DEFAULT 'videoPublish',
  execution_status ENUM('waiting', 'running', 'completed', 'failed', 'stopped') DEFAULT 'waiting',
  config_data JSON COMMENT '执行配置数据',
  started_at TIMESTAMP NULL,
  completed_at TIMESTAMP NULL,
  execution_duration INT DEFAULT 0 COMMENT '执行时长(秒)',
  progress_percentage INT DEFAULT 0,
  current_step VARCHAR(200) DEFAULT '',
  result_data JSON COMMENT '执行结果数据',
  error_message TEXT,
  video_count INT DEFAULT 0 COMMENT '分配的视频数量',
  published_count INT DEFAULT 0 COMMENT '成功发布的视频数量',
  failed_count INT DEFAULT 0 COMMENT '发布失败的视频数量',
  INDEX idx_task_id (task_id),
  INDEX idx_device_id (device_id),
  INDEX idx_execution_status (execution_status),
  INDEX idx_started_at (started_at)
);

-- 插入默认管理员用户（密码: admin123）
INSERT IGNORE INTO users (username, password, email)
VALUES ('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- 显示创建的表
SHOW TABLES;

-- 显示用户信息
SELECT username, email, created_at FROM users;

-- 设备连接码表
CREATE TABLE IF NOT EXISTS device_connection_codes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(20) UNIQUE NOT NULL COMMENT '连接码',
  user_id INT NOT NULL COMMENT '用户ID',
  username VARCHAR(50) NOT NULL COMMENT '用户名',
  description VARCHAR(200) DEFAULT '' COMMENT '连接码描述',
  max_devices INT DEFAULT 1 COMMENT '最大可连接设备数',
  used_count INT DEFAULT 0 COMMENT '已使用次数',
  expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at),
  INDEX idx_is_active (is_active),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 设备连接记录表
CREATE TABLE IF NOT EXISTS device_connections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  connection_code VARCHAR(20) NOT NULL COMMENT '使用的连接码',
  user_id INT NOT NULL COMMENT '分配的用户ID',
  device_name VARCHAR(100) NOT NULL,
  device_info JSON COMMENT '设备信息',
  connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_device_id (device_id),
  INDEX idx_connection_code (connection_code),
  INDEX idx_user_id (user_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (connection_code) REFERENCES device_connection_codes(code) ON DELETE CASCADE
);

-- 完成提示
SELECT '数据库初始化完成！' AS message;
