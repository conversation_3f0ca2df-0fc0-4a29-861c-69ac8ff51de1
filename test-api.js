/**
 * 简单的API测试脚本
 * 测试连接码相关的API是否正常工作
 */

const http = require('http');

// 测试API请求
function testAPI(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runTests() {
  console.log('🚀 开始API测试...\n');

  try {
    // 1. 测试登录获取token
    console.log('1. 测试登录...');
    const loginResult = await testAPI('/api/auth/login', 'POST', {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('登录结果:', loginResult.statusCode, loginResult.data.message);
    
    if (loginResult.statusCode === 200 && loginResult.data.success) {
      const token = loginResult.data.token;
      console.log('✅ 登录成功，获取到token\n');
      
      // 2. 测试获取连接码列表
      console.log('2. 测试获取连接码列表...');
      const codesResult = await testAPI('/api/device/connection-codes', 'GET', null, token);
      console.log('连接码列表结果:', codesResult.statusCode);
      
      if (codesResult.statusCode === 200) {
        console.log('✅ 连接码API正常工作');
        console.log('当前连接码数量:', codesResult.data.data?.length || 0);
      } else {
        console.log('❌ 连接码API异常:', codesResult.data);
      }
      
      // 3. 测试创建连接码
      console.log('\n3. 测试创建连接码...');
      const createResult = await testAPI('/api/device/connection-codes', 'POST', {
        description: 'API测试连接码',
        maxDevices: 3,
        expiresInHours: 24
      }, token);
      
      console.log('创建连接码结果:', createResult.statusCode);
      
      if (createResult.statusCode === 200 && createResult.data.success) {
        const newCode = createResult.data.data.code;
        console.log('✅ 连接码创建成功:', newCode);
        
        // 4. 测试连接码验证
        console.log('\n4. 测试连接码验证...');
        const verifyResult = await testAPI('/api/device/verify-connection-code', 'POST', {
          code: newCode,
          deviceId: 'test_device_api',
          deviceName: 'API测试设备',
          deviceInfo: {
            brand: 'Test',
            model: 'API_Test',
            ipAddress: '127.0.0.1'
          }
        });
        
        console.log('连接码验证结果:', verifyResult.statusCode);
        
        if (verifyResult.statusCode === 200 && verifyResult.data.success) {
          console.log('✅ 连接码验证成功');
          console.log('分配用户:', verifyResult.data.data.username);
        } else {
          console.log('❌ 连接码验证失败:', verifyResult.data);
        }
        
        // 5. 测试设备注册（使用连接码）
        console.log('\n5. 测试设备注册（使用连接码）...');
        const registerResult = await testAPI('/api/device/register', 'POST', {
          deviceId: 'test_device_api_2',
          deviceName: 'API测试设备2',
          deviceInfo: {
            brand: 'Test',
            model: 'API_Test_2',
            ipAddress: '127.0.0.1'
          },
          connectionCode: newCode
        });
        
        console.log('设备注册结果:', registerResult.statusCode);
        
        if (registerResult.statusCode === 200 && registerResult.data.success) {
          console.log('✅ 设备注册成功');
        } else {
          console.log('❌ 设备注册失败:', registerResult.data);
        }
        
      } else {
        console.log('❌ 连接码创建失败:', createResult.data);
      }
      
    } else {
      console.log('❌ 登录失败:', loginResult.data);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
  
  console.log('\n✅ API测试完成');
}

// 运行测试
runTests();
