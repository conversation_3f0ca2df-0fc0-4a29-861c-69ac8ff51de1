-- =====================================================
-- Auto.js云群控系统 - 主站数据库测试脚本
-- 仅用于测试环境，创建主站数据库表结构
-- 生产环境请连接真实的主站数据库
-- =====================================================

-- 创建测试主站数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS autojs_main_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE autojs_main_test;

-- =====================================================
-- 1. 主站用户表（测试用）
-- =====================================================
CREATE TABLE IF NOT EXISTS main_users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码（bcrypt加密）',
  email VARCHAR(100) COMMENT '邮箱地址',
  phone VARCHAR(20) COMMENT '手机号码',
  real_name VARCHAR(50) COMMENT '真实姓名',
  status ENUM('active', 'disabled', 'suspended') DEFAULT 'active' COMMENT '账号状态',
  role ENUM('user', 'admin', 'super_admin') DEFAULT 'user' COMMENT '用户角色',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  login_count INT DEFAULT 0 COMMENT '登录次数',
  
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='主站用户表';

-- =====================================================
-- 2. 卡密/激活码表（测试用）
-- =====================================================
CREATE TABLE IF NOT EXISTS activation_codes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(100) UNIQUE NOT NULL COMMENT '卡密/激活码',
  type ENUM('card', 'activation', 'trial') DEFAULT 'card' COMMENT '类型：卡密/激活码/试用',
  duration_days INT NOT NULL COMMENT '时效天数',
  max_uses INT DEFAULT 1 COMMENT '最大使用次数',
  used_count INT DEFAULT 0 COMMENT '已使用次数',
  status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active' COMMENT '状态',
  description TEXT COMMENT '描述信息',
  batch_id VARCHAR(50) COMMENT '批次ID',
  created_by INT COMMENT '创建者用户ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  expires_at TIMESTAMP NULL COMMENT '卡密过期时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX idx_code (code),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_batch_id (batch_id),
  INDEX idx_created_by (created_by),
  INDEX idx_expires_at (expires_at),
  INDEX idx_created_at (created_at),
  
  FOREIGN KEY (created_by) REFERENCES main_users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密激活码表';

-- =====================================================
-- 3. 用户激活记录表（测试用）
-- =====================================================
CREATE TABLE IF NOT EXISTS user_activations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  activation_code_id INT NOT NULL COMMENT '激活码ID',
  activation_code VARCHAR(100) NOT NULL COMMENT '激活码（冗余存储）',
  activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '激活时间',
  expires_at TIMESTAMP NOT NULL COMMENT '用户时效过期时间',
  duration_days INT NOT NULL COMMENT '激活天数',
  ip_address VARCHAR(45) COMMENT '激活IP地址',
  user_agent TEXT COMMENT '用户代理信息',
  notes TEXT COMMENT '备注信息',
  
  INDEX idx_user_id (user_id),
  INDEX idx_activation_code_id (activation_code_id),
  INDEX idx_activation_code (activation_code),
  INDEX idx_activated_at (activated_at),
  INDEX idx_expires_at (expires_at),
  
  FOREIGN KEY (user_id) REFERENCES main_users(id) ON DELETE CASCADE,
  FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户激活记录表';

-- =====================================================
-- 初始化测试数据
-- =====================================================

-- 创建测试超级管理员账号
INSERT IGNORE INTO main_users (username, password, email, role, status) VALUES 
('admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'super_admin', 'active'),
('testuser', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'user', 'active');

-- 创建测试卡密
INSERT IGNORE INTO activation_codes (code, type, duration_days, max_uses, description, batch_id, created_by) VALUES
('TEST-30DAY-001', 'card', 30, 1, '测试卡密-30天', 'TEST_BATCH_001', 1),
('TEST-7DAY-001', 'trial', 7, 1, '测试试用卡密-7天', 'TEST_BATCH_001', 1),
('TEST-365DAY-001', 'card', 365, 1, '测试卡密-365天', 'TEST_BATCH_001', 1),
('MULTI-USE-001', 'card', 30, 5, '多次使用测试卡密-30天', 'TEST_BATCH_002', 1),
('EXPIRED-001', 'card', 30, 1, '已过期测试卡密', 'TEST_BATCH_003', 1);

-- 设置一个卡密为过期状态（用于测试）
UPDATE activation_codes SET 
  expires_at = DATE_SUB(NOW(), INTERVAL 1 DAY),
  status = 'expired'
WHERE code = 'EXPIRED-001';

-- =====================================================
-- 创建视图（测试用）
-- =====================================================

-- 用户激活统计视图
CREATE OR REPLACE VIEW user_activation_stats AS
SELECT 
  u.id as user_id,
  u.username,
  u.email,
  u.status as user_status,
  COUNT(ua.id) as activation_count,
  MAX(ua.expires_at) as latest_expires_at,
  MIN(ua.activated_at) as first_activated_at,
  SUM(ua.duration_days) as total_duration_days
FROM main_users u
LEFT JOIN user_activations ua ON u.id = ua.user_id
GROUP BY u.id, u.username, u.email, u.status;

-- 卡密使用统计视图
CREATE OR REPLACE VIEW activation_code_stats AS
SELECT 
  ac.id,
  ac.code,
  ac.type,
  ac.duration_days,
  ac.max_uses,
  ac.used_count,
  ac.status,
  ac.batch_id,
  COUNT(ua.id) as actual_uses,
  GROUP_CONCAT(DISTINCT u.username) as used_by_users
FROM activation_codes ac
LEFT JOIN user_activations ua ON ac.id = ua.activation_code_id
LEFT JOIN main_users u ON ua.user_id = u.id
GROUP BY ac.id;

-- =====================================================
-- 显示测试信息
-- =====================================================

SELECT '主站数据库测试环境初始化完成！' as message;
SELECT '⚠️  这是测试数据库，生产环境请连接真实主站数据库' as warning;

-- 显示测试账号信息
SELECT 'Test Users:' as info;
SELECT username, email, role, status FROM main_users;

-- 显示测试卡密信息
SELECT 'Test Activation Codes:' as info;
SELECT code, type, duration_days, max_uses, used_count, status, description FROM activation_codes;

COMMIT;
