import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘' }
      },
      {
        path: 'devices',
        name: 'Devices',
        component: () => import('@/views/Devices.vue'),
        meta: { title: '设备管理' }
      },
      {
        path: 'scripts',
        name: 'Scripts',
        component: () => import('@/views/Scripts.vue'),
        meta: { title: '脚本管理' }
      },
      {
        path: 'script-config',
        name: 'ScriptConfig',
        component: () => import('@/views/ScriptConfig.vue'),
        meta: { title: '脚本配置执行' }
      },
      {
        path: 'files',
        name: 'Files',
        component: () => import('@/views/Files.vue'),
        meta: { title: '文件管理' }
      },
      {
        path: 'logs',
        name: 'Logs',
        component: () => import('@/views/Logs.vue'),
        meta: { title: '执行日志' }
      },
      {
        path: 'xiaohongshu',
        name: 'XiaohongshuAutomation',
        component: () => import('@/views/XiaohongshuAutomation.vue'),
        meta: { title: '小红书自动化' }
      },
      {
        path: 'xiaohongshu-logs',
        name: 'XiaohongshuLogs',
        component: () => import('@/views/XiaohongshuLogs.vue'),
        meta: { title: '小红书执行日志' }
      },
      {
        path: 'xianyu',
        name: 'XianyuAutomation',
        component: () => import('@/views/XianyuAutomation.vue'),
        meta: { title: '闲鱼自动化' }
      },
      {
        path: 'xianyu-logs',
        name: 'XianyuLogs',
        component: () => import('@/views/XianyuLogs.vue'),
        meta: { title: '闲鱼执行日志' }
      },
      {
        path: 'xianyu-chat-records/:deviceId',
        name: 'XianyuChatRecords',
        component: () => import('@/views/XianyuChatRecords.vue'),
        meta: { title: '闲鱼私聊记录' }
      },
      // 管理员页面
      {
        path: 'admin/activation-codes',
        name: 'AdminActivationCodes',
        component: () => import('@/views/admin/ActivationCodes.vue'),
        meta: { title: '卡密管理', requiresAdmin: true }
      },
      {
        path: 'admin/users',
        name: 'AdminUserManagement',
        component: () => import('@/views/admin/UserManagement.vue'),
        meta: { title: '用户管理', requiresAdmin: true }
      }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated']
  const user = store.getters['auth/user']

  if (to.matched.some(record => record.meta.requiresAuth !== false)) {
    if (!isAuthenticated) {
      next('/login')
    } else {
      // 检查管理员权限
      if (to.matched.some(record => record.meta.requiresAdmin)) {
        if (user && user.role === 'admin') {
          next()
        } else {
          next('/dashboard')
        }
      } else {
        next()
      }
    }
  } else {
    if (to.path === '/login' && isAuthenticated) {
      next('/')
    } else {
      next()
    }
  }
})

export default router
