# 🚀 Auto.js云群控系统 - 完整账号登录功能部署指南

## 📋 部署概述

本指南将帮助您部署基于主站数据库验证的完整账号登录功能，包括卡密/激活码系统和管理员功能。

## ✅ 部署前检查

### 系统要求
- Node.js 14.0+
- MySQL 5.7+ 或 8.0+
- 现有的Auto.js云群控系统

### 数据库要求
- **本地数据库**: 现有的 `autojs_control` 数据库
- **主站数据库**: 可选，用于账号验证和卡密管理

## 🔧 部署步骤

### 第一步：备份现有数据

```bash
# 备份现有数据库
mysqldump -u autojs_control -p autojs_control > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份现有代码
cp -r server server_backup_$(date +%Y%m%d_%H%M%S)
cp -r web web_backup_$(date +%Y%m%d_%H%M%S)
```

### 第二步：配置环境变量

创建或更新 `.env` 文件：

```bash
# 主站数据库配置（可选）
MAIN_DB_HOST=your_main_db_host
MAIN_DB_USER=your_main_db_user
MAIN_DB_PASSWORD=your_main_db_password
MAIN_DB_NAME=your_main_db_name

# JWT密钥
JWT_SECRET=your-super-secret-jwt-key

# 服务器配置
NODE_ENV=production
SERVER_PORT=3002
```

### 第三步：安装依赖

```bash
# 后端依赖
cd server
npm install

# 前端依赖
cd ../web
npm install
```

### 第四步：数据库迁移

#### 4.1 升级本地数据库结构

```bash
cd server
node database/migration-script.js migrate
```

#### 4.2 创建测试主站数据库（可选）

如果您没有现成的主站数据库，可以创建测试数据库：

```bash
# 创建测试主站数据库
mysql -u root -p < database/main-database-test.sql

# 或使用迁移脚本
node database/migration-script.js test-main-db
```

### 第五步：验证迁移结果

```bash
node database/migration-script.js validate
```

### 第六步：启动服务

```bash
# 启动后端服务
cd server
npm start

# 或使用PM2
pm2 start server-main.js --name "autojs-control"

# 构建并启动前端
cd ../web
npm run build
# 将dist目录部署到Web服务器
```

## 🔑 功能说明

### 登录流程

1. **首次登录**: 需要主站账号 + 卡密/激活码
2. **正常登录**: 主站账号验证 + 本地时效检查
3. **续期登录**: 账号过期时需要新的卡密/激活码
4. **本地模式**: 主站不可用时自动切换到本地认证

### 管理员功能

#### 卡密管理 (`/admin/activation-codes`)
- 批量生成卡密/激活码
- 设置时效天数和使用次数
- 查看使用统计和状态管理

#### 用户管理 (`/admin/users`)
- 查看所有用户信息和统计
- 管理用户状态和权限
- 延长用户时效
- 删除用户账号

### 数据隔离

- 所有用户数据完全隔离
- 设备、执行日志、文件等按用户分离
- 管理员可查看所有用户数据

## 🧪 测试验证

### 测试账号

**管理员账号**:
- 用户名: `admin`
- 密码: `password` (测试主站数据库)

**测试卡密**:
- `TEST-30DAY-001` - 30天有效期
- `TEST-7DAY-001` - 7天试用期
- `TEST-365DAY-001` - 365天有效期

### 测试流程

1. **登录测试**
   ```bash
   # 访问登录页面
   http://localhost:3002/login
   
   # 测试首次登录（需要卡密）
   # 测试正常登录
   # 测试过期续期
   ```

2. **管理员功能测试**
   ```bash
   # 访问管理员页面
   http://localhost:3002/admin/activation-codes
   http://localhost:3002/admin/users
   ```

3. **API测试**
   ```bash
   # 测试登录API
   curl -X POST http://localhost:3002/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"password","activationCode":"TEST-30DAY-001"}'
   ```

## 🔧 配置选项

### 主站数据库配置

如果您有现成的主站数据库，请确保包含以下表结构：

- `main_users` - 主站用户表
- `activation_codes` - 卡密/激活码表
- `user_activations` - 用户激活记录表

### 系统配置

在 `server/config/database.js` 中可以调整：

- 数据库连接池大小
- 连接超时设置
- 主站数据库连接参数

## 🚨 故障排除

### 常见问题

1. **主站数据库连接失败**
   - 检查网络连接和数据库配置
   - 系统会自动切换到本地认证模式

2. **数据迁移失败**
   ```bash
   # 回滚迁移
   node database/migration-script.js rollback
   
   # 重新执行迁移
   node database/migration-script.js migrate
   ```

3. **权限问题**
   - 确保数据库用户有足够权限
   - 检查文件系统权限

4. **前端页面无法访问**
   - 检查路由配置
   - 确保管理员权限正确设置

### 日志查看

```bash
# 查看服务器日志
pm2 logs autojs-control

# 查看数据库连接状态
tail -f server/logs/database.log
```

## 📊 监控和维护

### 定期维护

1. **清理过期数据**
   ```sql
   -- 清理过期的会话
   DELETE FROM user_sessions WHERE expires_at <= NOW();
   
   -- 清理过期的通知
   DELETE FROM system_notifications WHERE expires_at <= NOW();
   ```

2. **检查账号过期**
   ```sql
   -- 查看即将过期的账号
   SELECT username, expires_at, DATEDIFF(expires_at, NOW()) as days_remaining
   FROM users 
   WHERE expires_at <= DATE_ADD(NOW(), INTERVAL 7 DAY)
   AND account_status = 'active';
   ```

3. **统计信息**
   ```sql
   -- 用户统计
   SELECT * FROM user_status_stats;
   
   -- 设备统计
   SELECT * FROM user_device_stats;
   ```

### 性能优化

1. **数据库索引优化**
   - 定期分析慢查询
   - 优化索引结构

2. **缓存策略**
   - 考虑使用Redis缓存用户信息
   - 缓存卡密验证结果

## 🔒 安全建议

1. **密码安全**
   - 使用强JWT密钥
   - 定期更换密钥

2. **数据库安全**
   - 使用SSL连接
   - 限制数据库访问权限

3. **网络安全**
   - 使用HTTPS
   - 配置防火墙规则

## 📞 技术支持

如遇到问题，请检查：

1. 系统日志文件
2. 数据库连接状态
3. 网络连接情况
4. 权限配置

---

**部署完成后，您的系统将具备：**
- ✅ 完整的账号登录验证
- ✅ 卡密/激活码管理
- ✅ 多用户数据隔离
- ✅ 管理员功能界面
- ✅ 向后兼容性保证
