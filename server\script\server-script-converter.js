/**
 * 脚本转换模块
 * 负责将UI脚本转换为无UI脚本并注入参数
 * 从原始server.js文件中提取的完整转换逻辑
 */

const fs = require('fs');
const path = require('path');

// 将UI界面脚本转换为无UI界面脚本 (原始文件第2137行)
function convertUIScriptToNonUI(originalScript, functionType, params, taskId) {
  console.log('开始转换脚本:', functionType);
  console.log('传入参数:', JSON.stringify(params, null, 2));
  console.log('任务ID:', taskId);

  // 文章评论功能现在直接使用无UI脚本，不需要转换
  if (functionType === 'articleComment') {
    console.log('文章评论功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }

  // 循环群发功能现在直接使用无UI脚本，不需要转换
  if (functionType === 'groupMessage') {
    console.log('循环群发功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }

  // 视频发布功能直接使用无UI脚本，不需要转换
  if (functionType === 'videoPublish') {
    console.log('视频发布功能已改为直接使用无UI脚本，跳过转换逻辑');
    return originalScript; // 直接返回原始脚本，参数注入在executeXiaohongshuTask中处理
  }

  // UID私信功能直接使用无UI脚本，不需要转换
  if (functionType === 'uidMessage') {
    console.log('UID私信功能已改为直接使用无UI脚本，跳过转换逻辑');
    
    // 为UID私信功能注入参数
    const paramInjectionCode = `
// Web端传递的配置参数
const webConfig = ${JSON.stringify(params, null, 2)};

console.log('收到Web端配置参数:', JSON.stringify(webConfig, null, 2));

// 提取UID私信参数
const uidList = webConfig.uidList || [];
const messageContent = webConfig.messageContent || '';
const operationDelay = webConfig.operationDelay || 3;
const randomDelay = webConfig.randomDelay || true;
const autoSave = webConfig.autoSave || false;

console.log('UID列表长度:', uidList.length);
console.log('私信内容:', messageContent);
console.log('操作延迟:', operationDelay);
`;

    return paramInjectionCode + '\n' + originalScript;
  }

  // 对于其他功能，使用原有的转换逻辑
  console.log('使用原有的脚本转换逻辑');

  // 对于searchGroupChat功能，使用手动优化的模板
  if (functionType === 'searchGroupChat') {
    console.log('使用searchGroupChat专用的模板转换逻辑');

    // 读取手动优化的模板
    const templatePath = './searchgroupchat-template.js';
    let templateScript = '';

    try {
      templateScript = fs.readFileSync(templatePath, 'utf8');
      console.log('成功读取搜索加群模板文件，长度:', templateScript.length);
    } catch (error) {
      console.log('读取模板文件失败:', error.message);
      throw new Error('无法读取搜索加群模板文件');
    }

    // 使用模板替换参数
    const finalScript = templateScript
      .replace(/\{\{SEARCH_KEYWORD\}\}/g, params.searchKeyword || '私域')
      .replace(/\{\{TARGET_JOIN_COUNT\}\}/g, params.targetJoinCount || 5)
      .replace(/\{\{OPERATION_INTERVAL\}\}/g, params.operationInterval || 10)
      .replace(/\{\{AUTO_SAVE\}\}/g, params.autoSave || true)
      .replace(/\{\{SEND_MESSAGE\}\}/g, params.sendMessage || false)
      .replace(/\{\{JOIN_MESSAGE\}\}/g, params.joinMessage || '')
      .replace(/\{\{AUTO_SCROLL\}\}/g, params.autoScroll || true)
      .replace(/\{\{SKIP_JOINED\}\}/g, params.skipJoined || true)
      .replace(/\{\{RANDOM_DELAY\}\}/g, params.randomDelay || true);

    console.log('searchGroupChat模板转换完成，最终脚本长度:', finalScript.length);

    return finalScript;
  }

  // 对于修改资料功能，使用专门的转换函数
  if (functionType === 'profile') {
    return convertProfileScriptToNonUI(originalScript, params);
  }

  // 对于其他功能，使用通用转换逻辑
  return convertGenericScriptToNonUI(originalScript, functionType, params, taskId);
}

// 专门用于转换修改资料脚本为无UI版本的函数 (原始文件第2302行)
function convertProfileScriptToNonUI(originalScript, params) {
  console.log('开始转换修改资料脚本为无UI版本');
  console.log('参数:', JSON.stringify(params, null, 2));

  // 移除UI相关的代码
  let cleanedScript = originalScript;

  // 1. 移除UI声明和布局
  cleanedScript = cleanedScript.replace(/"ui";\s*\n/, '');
  cleanedScript = cleanedScript.replace(/ui\.layout\([\s\S]*?\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.statusBarColor\([^)]*\);\s*\n/g, '');

  // 2. 替换存储器相关代码（在无UI模式下使用默认值）
  cleanedScript = cleanedScript.replace(/const storage = storages\.create\([^)]*\);\s*\n/g, '// 存储器已移除\n');

  // 替换存储器获取操作为默认值，而不是完全移除
  cleanedScript = cleanedScript.replace(/const savedNickname = storage\.get\([^)]*\);/g, 'const savedNickname = "";');
  cleanedScript = cleanedScript.replace(/const savedProfile = storage\.get\([^)]*\);/g, 'const savedProfile = "";');
  cleanedScript = cleanedScript.replace(/const (saved\w+) = storage\.get\([^)]*\);/g, 'const $1 = "";');

  // 3. 移除UI相关的变量声明
  cleanedScript = cleanedScript.replace(/let isRunning = false;\s*\n/g, 'let isRunning = true;\n');
  cleanedScript = cleanedScript.replace(/let shouldStop = false;\s*\n/g, 'let shouldStop = false;\n');

  // 4. 移除UI事件监听器
  cleanedScript = cleanedScript.replace(/ui\.\w+\.on\([^}]*\}\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/ui\.\w+\.click\([^}]*\}\);\s*\n/g, '');

  // 5. 移除UI相关的函数调用
  cleanedScript = cleanedScript.replace(/updateButtonStates\(\);\s*\n/g, '');
  cleanedScript = cleanedScript.replace(/updateServiceStatus\(\);\s*\n/g, '');

  // 6. 注入参数
  const paramInjection = `
// Web端传递的配置参数
const webConfig = ${JSON.stringify(params, null, 2)};

console.log('收到Web端配置参数:', JSON.stringify(webConfig, null, 2));

// 提取具体参数
const nickname = webConfig.nickname || '';
const profile = webConfig.profile || '';
const onlyNickname = webConfig.onlyNickname || false;
const onlyProfile = webConfig.onlyProfile || false;
const autoSave = webConfig.autoSave || false;

// 强制初始化实时状态统计变量（修复NaN问题）
if (typeof operationCount === 'undefined') {
    var operationCount = 0;
}
if (typeof processedStepCount === 'undefined') {
    var processedStepCount = 0;
}
console.log('实时状态变量初始化完成: operationCount=' + operationCount + ', processedStepCount=' + processedStepCount);
const operationDelay = webConfig.operationDelay || 2;

console.log('昵称:', nickname);
console.log('简介:', profile);
console.log('仅修改昵称:', onlyNickname);
console.log('仅修改简介:', onlyProfile);
console.log('自动保存:', autoSave);
console.log('操作延迟:', operationDelay);

// 定义脚本可能需要的变量
const savedNickname = "";
const savedProfile = "";
let isRunning = true;
let shouldStop = false;

// 自动执行脚本
setTimeout(() => {
  console.log('开始自动执行修改资料脚本');
  executeScript();
}, 1000);

`;

  // 替换占位符
  cleanedScript = cleanedScript.replace(/DEVICE_ID_PLACEHOLDER/g, params.deviceId || 'unknown');
  cleanedScript = cleanedScript.replace(/TASK_ID_PLACEHOLDER/g, params.taskId || 'unknown');
  cleanedScript = cleanedScript.replace(/SERVER_HOST_PLACEHOLDER/g, params.serverHost || '************:3002');

  // 组合最终脚本
  const finalScript = paramInjection + '\n' + cleanedScript;

  console.log('修改资料脚本转换完成，最终长度:', finalScript.length);
  console.log('已替换占位符: deviceId=' + (params.deviceId || 'unknown') + ', taskId=' + (params.taskId || 'unknown'));
  return finalScript;
}

// 通用脚本转换函数
function convertGenericScriptToNonUI(originalScript, functionType, params, taskId) {
  console.log('使用通用脚本转换逻辑');

  // 替换占位符
  let processedScript = originalScript;
  processedScript = processedScript.replace(/DEVICE_ID_PLACEHOLDER/g, params.deviceId || 'unknown');
  processedScript = processedScript.replace(/TASK_ID_PLACEHOLDER/g, params.taskId || taskId || 'unknown');
  processedScript = processedScript.replace(/SERVER_HOST_PLACEHOLDER/g, params.serverHost || '************:3002');

  // 简单的参数注入
  const paramInjection = `
// Web端传递的配置参数
const webConfig = ${JSON.stringify(params, null, 2)};

console.log('收到Web端配置参数:', JSON.stringify(webConfig, null, 2));

// 任务ID
const taskId = '${taskId}';

`;

  console.log('通用脚本转换完成，已替换占位符: deviceId=' + (params.deviceId || 'unknown') + ', taskId=' + (params.taskId || taskId || 'unknown'));
  return paramInjection + '\n' + processedScript;
}

module.exports = {
  convertUIScriptToNonUI,
  convertProfileScriptToNonUI,
  convertGenericScriptToNonUI
};
