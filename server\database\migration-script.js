/**
 * 数据迁移脚本
 * 将现有用户数据迁移到新的账号系统，确保向后兼容
 */

const { localPool, mainPool, testDatabaseConnections } = require('../config/database');
const fs = require('fs').promises;
const path = require('path');

class DataMigration {
  constructor() {
    this.localPool = localPool;
    this.mainPool = mainPool;
  }

  /**
   * 执行完整的数据迁移
   */
  async migrate() {
    console.log('🚀 开始数据迁移...');
    
    try {
      // 1. 测试数据库连接
      await this.testConnections();
      
      // 2. 执行本地数据库结构升级
      await this.upgradeLocalDatabase();
      
      // 3. 迁移现有用户数据
      await this.migrateExistingUsers();
      
      // 4. 验证迁移结果
      await this.validateMigration();
      
      console.log('✅ 数据迁移完成！');
      
    } catch (error) {
      console.error('❌ 数据迁移失败:', error);
      throw error;
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnections() {
    console.log('🔍 测试数据库连接...');
    await testDatabaseConnections();
  }

  /**
   * 执行本地数据库结构升级
   */
  async upgradeLocalDatabase() {
    console.log('📊 升级本地数据库结构...');
    
    try {
      const sqlFile = path.join(__dirname, 'local-database-upgrade.sql');
      const sqlContent = await fs.readFile(sqlFile, 'utf8');
      
      // 分割SQL语句并执行
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.toLowerCase().includes('select')) {
          // 跳过SELECT语句（用于显示信息）
          continue;
        }
        
        try {
          await this.localPool.execute(statement);
        } catch (error) {
          // 忽略已存在的表/列错误
          if (!error.message.includes('already exists') && 
              !error.message.includes('Duplicate column name')) {
            console.warn('SQL执行警告:', error.message);
          }
        }
      }
      
      console.log('✅ 本地数据库结构升级完成');
      
    } catch (error) {
      console.error('❌ 本地数据库结构升级失败:', error);
      throw error;
    }
  }

  /**
   * 迁移现有用户数据
   */
  async migrateExistingUsers() {
    console.log('👥 迁移现有用户数据...');
    
    try {
      // 获取所有现有用户
      const [users] = await this.localPool.execute(`
        SELECT id, username, password, email, role, is_active, created_at, last_login_time, login_count
        FROM users
        WHERE main_user_id IS NULL OR main_user_id = 0
      `);
      
      console.log(`发现 ${users.length} 个需要迁移的用户`);
      
      for (const user of users) {
        await this.migrateUser(user);
      }
      
      console.log('✅ 用户数据迁移完成');
      
    } catch (error) {
      console.error('❌ 用户数据迁移失败:', error);
      throw error;
    }
  }

  /**
   * 迁移单个用户
   */
  async migrateUser(user) {
    const connection = await this.localPool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      // 设置默认的账号时效（1年）
      const expiresAt = new Date();
      expiresAt.setFullYear(expiresAt.getFullYear() + 1);
      
      // 为管理员设置更长的时效（10年）
      if (user.role === 'admin') {
        expiresAt.setFullYear(expiresAt.getFullYear() + 9); // 总共10年
      }
      
      // 更新用户信息
      await connection.execute(`
        UPDATE users SET 
          main_user_id = CASE 
            WHEN role = 'admin' THEN 1 
            ELSE NULL 
          END,
          expires_at = ?,
          last_activation_at = COALESCE(last_login_time, created_at, NOW()),
          activation_count = 1,
          total_duration_days = CASE 
            WHEN role = 'admin' THEN 3650 
            ELSE 365 
          END,
          account_status = CASE 
            WHEN is_active = 1 THEN 'active' 
            ELSE 'disabled' 
          END,
          is_main_verified = CASE 
            WHEN role = 'admin' THEN TRUE 
            ELSE FALSE 
          END,
          last_main_sync_at = NOW()
        WHERE id = ?
      `, [expiresAt, user.id]);
      
      // 创建激活记录
      await connection.execute(`
        INSERT INTO local_activations 
        (user_id, main_user_id, activation_code, activation_type, duration_days, 
         activated_at, expires_at, notes)
        VALUES (?, ?, 'MIGRATION_INIT', 'first_login', ?, ?, ?, '数据迁移初始化')
      `, [
        user.id,
        user.role === 'admin' ? 1 : null,
        user.role === 'admin' ? 3650 : 365,
        user.last_login_time || user.created_at,
        expiresAt,
      ]);
      
      await connection.commit();
      
      console.log(`✅ 用户 ${user.username} 迁移完成`);
      
    } catch (error) {
      await connection.rollback();
      console.error(`❌ 用户 ${user.username} 迁移失败:`, error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    console.log('🔍 验证迁移结果...');
    
    try {
      // 检查用户表结构
      const [columns] = await this.localPool.execute(`
        SHOW COLUMNS FROM users LIKE 'main_user_id'
      `);
      
      if (columns.length === 0) {
        throw new Error('用户表结构升级失败：缺少 main_user_id 字段');
      }
      
      // 检查迁移的用户数量
      const [userStats] = await this.localPool.execute(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN expires_at IS NOT NULL THEN 1 END) as users_with_expiry,
          COUNT(CASE WHEN account_status = 'active' THEN 1 END) as active_users,
          COUNT(CASE WHEN is_main_verified = 1 THEN 1 END) as verified_users
        FROM users
      `);
      
      const stats = userStats[0];
      
      console.log('📊 迁移统计:');
      console.log(`  - 总用户数: ${stats.total_users}`);
      console.log(`  - 设置时效的用户: ${stats.users_with_expiry}`);
      console.log(`  - 活跃用户: ${stats.active_users}`);
      console.log(`  - 已验证用户: ${stats.verified_users}`);
      
      // 检查激活记录
      const [activationStats] = await this.localPool.execute(`
        SELECT COUNT(*) as total_activations FROM local_activations
      `);
      
      console.log(`  - 激活记录数: ${activationStats[0].total_activations}`);
      
      // 检查新表是否创建成功
      const [tables] = await this.localPool.execute(`
        SHOW TABLES LIKE 'local_activations'
      `);
      
      if (tables.length === 0) {
        throw new Error('新表创建失败：local_activations 表不存在');
      }
      
      console.log('✅ 迁移结果验证通过');
      
    } catch (error) {
      console.error('❌ 迁移结果验证失败:', error);
      throw error;
    }
  }

  /**
   * 回滚迁移（仅用于测试）
   */
  async rollback() {
    console.log('⚠️  开始回滚迁移...');
    
    try {
      // 重置用户表的新字段
      await this.localPool.execute(`
        UPDATE users SET 
          main_user_id = NULL,
          expires_at = NULL,
          last_activation_at = NULL,
          activation_count = 0,
          total_duration_days = 0,
          account_status = CASE WHEN is_active = 1 THEN 'active' ELSE 'disabled' END,
          is_main_verified = FALSE,
          last_main_sync_at = NULL
      `);
      
      // 清空激活记录
      await this.localPool.execute('DELETE FROM local_activations');
      
      console.log('✅ 迁移回滚完成');
      
    } catch (error) {
      console.error('❌ 迁移回滚失败:', error);
      throw error;
    }
  }

  /**
   * 创建测试主站数据库（仅用于测试）
   */
  async createTestMainDatabase() {
    console.log('🧪 创建测试主站数据库...');
    
    try {
      const sqlFile = path.join(__dirname, 'main-database-test.sql');
      const sqlContent = await fs.readFile(sqlFile, 'utf8');
      
      // 分割SQL语句并执行
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.toLowerCase().includes('select')) {
          // 跳过SELECT语句（用于显示信息）
          continue;
        }
        
        try {
          await this.mainPool.execute(statement);
        } catch (error) {
          // 忽略已存在的表/数据库错误
          if (!error.message.includes('already exists') && 
              !error.message.includes('Duplicate entry')) {
            console.warn('SQL执行警告:', error.message);
          }
        }
      }
      
      console.log('✅ 测试主站数据库创建完成');
      
    } catch (error) {
      console.error('❌ 测试主站数据库创建失败:', error);
      throw error;
    }
  }
}

// 命令行执行
async function main() {
  const migration = new DataMigration();
  
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'migrate':
        await migration.migrate();
        break;
      case 'rollback':
        await migration.rollback();
        break;
      case 'test-main-db':
        await migration.createTestMainDatabase();
        break;
      case 'validate':
        await migration.validateMigration();
        break;
      default:
        console.log('使用方法:');
        console.log('  node migration-script.js migrate     - 执行完整迁移');
        console.log('  node migration-script.js rollback    - 回滚迁移');
        console.log('  node migration-script.js test-main-db - 创建测试主站数据库');
        console.log('  node migration-script.js validate    - 验证迁移结果');
        break;
    }
  } catch (error) {
    console.error('执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    if (localPool) {
      await localPool.end();
    }
    if (mainPool) {
      await mainPool.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = DataMigration;
