/**
 * 数据库查询增强器
 * 自动为SQL查询添加用户隔离条件，确保数据安全
 */

class DatabaseQueryEnhancer {
  constructor(pool) {
    this.pool = pool;
    
    // 需要用户隔离的表列表
    this.isolatedTables = [
      'devices', // 设备表需要数据隔离
      'device_apps',
      'xiaohongshu_execution_logs',
      'xianyu_execution_logs',
      'uid_files',
      'uid_data',
      'xiaohongshu_uids',
      'xiaohongshu_manual_uid_messages',
      'xiaohongshu_file_uid_messages',
      'xianyu_chat_records',
      'xiaohongshu_video_files',
      'xiaohongshu_video_transfers',
      'xiaohongshu_video_assignments',
      'xiaohongshu_video_execution_logs',
      'scripts',
      'execution_logs',
      'file_transfers'
    ];
    
    // 表别名映射（处理JOIN查询中的表别名）
    this.tableAliases = new Map();
  }
  
  /**
   * 检查SQL是否需要用户过滤
   * @param {string} sql - SQL语句
   * @returns {boolean} 是否需要过滤
   */
  needsUserFilter(sql) {
    const upperSql = sql.toUpperCase();
    return this.isolatedTables.some(table => {
      const upperTable = table.toUpperCase();
      return upperSql.includes(`FROM ${upperTable}`) || 
             upperSql.includes(`JOIN ${upperTable}`) ||
             upperSql.includes(`UPDATE ${upperTable}`) ||
             upperSql.includes(`DELETE FROM ${upperTable}`) ||
             upperSql.includes(`INSERT INTO ${upperTable}`);
    });
  }
  
  /**
   * 解析SQL中的表别名
   * @param {string} sql - SQL语句
   * @returns {Map} 表别名映射
   */
  parseTableAliases(sql) {
    const aliases = new Map();
    const upperSql = sql.toUpperCase();

    // 匹配 "FROM table_name alias" 或 "JOIN table_name alias"
    // 但排除ORDER BY, GROUP BY等关键字
    const aliasRegex = /(?:FROM|JOIN)\s+(\w+)\s+(?:AS\s+)?(\w+)(?=\s|$|,|\)|WHERE|ORDER|GROUP|HAVING|LIMIT)/gi;
    let match;

    while ((match = aliasRegex.exec(sql)) !== null) {
      const tableName = match[1].toLowerCase();
      const alias = match[2].toLowerCase();

      // 排除SQL关键字作为别名
      const sqlKeywords = ['where', 'order', 'group', 'having', 'limit', 'by', 'asc', 'desc'];
      if (sqlKeywords.includes(alias)) {
        continue;
      }

      if (this.isolatedTables.includes(tableName)) {
        aliases.set(alias, tableName);
      }
    }

    return aliases;
  }
  
  /**
   * 为SELECT查询添加用户过滤
   * @param {string} sql - 原始SQL
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Object} 增强后的SQL和参数
   */
  enhanceSelectQuery(sql, params, userId) {
    const aliases = this.parseTableAliases(sql);
    const upperSql = sql.toUpperCase();
    
    // 构建用户过滤条件
    const userFilters = [];
    
    // 处理主表过滤
    for (const table of this.isolatedTables) {
      const upperTable = table.toUpperCase();
      if (upperSql.includes(`FROM ${upperTable}`)) {
        userFilters.push(`${table}.user_id = ?`);
        params.unshift(userId);
        break;
      }
    }
    
    // 处理JOIN表过滤
    aliases.forEach((tableName, alias) => {
      userFilters.push(`${alias}.user_id = ?`);
      params.unshift(userId);
    });
    
    if (userFilters.length === 0) {
      return { sql, params };
    }
    
    // 添加WHERE条件
    const filterCondition = userFilters.join(' AND ');
    
    if (upperSql.includes('WHERE')) {
      sql = sql.replace(/WHERE/i, `WHERE ${filterCondition} AND`);
    } else {
      // 找到合适的位置插入WHERE子句
      const insertPosition = this.findWhereInsertPosition(sql);
      const beforeWhere = sql.slice(0, insertPosition).trimEnd();
      const afterWhere = sql.slice(insertPosition);
      sql = beforeWhere + ` WHERE ${filterCondition} ` + afterWhere;
    }
    
    return { sql, params };
  }
  
  /**
   * 为UPDATE查询添加用户过滤
   * @param {string} sql - 原始SQL
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Object} 增强后的SQL和参数
   */
  enhanceUpdateQuery(sql, params, userId) {
    const upperSql = sql.toUpperCase();
    
    if (upperSql.includes('WHERE')) {
      sql = sql.replace(/WHERE/i, 'WHERE user_id = ? AND');
      params.unshift(userId);
    } else {
      sql += ' WHERE user_id = ?';
      params.push(userId);
    }
    
    return { sql, params };
  }
  
  /**
   * 为DELETE查询添加用户过滤
   * @param {string} sql - 原始SQL
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Object} 增强后的SQL和参数
   */
  enhanceDeleteQuery(sql, params, userId) {
    const upperSql = sql.toUpperCase();
    
    if (upperSql.includes('WHERE')) {
      sql = sql.replace(/WHERE/i, 'WHERE user_id = ? AND');
      params.unshift(userId);
    } else {
      sql += ' WHERE user_id = ?';
      params.push(userId);
    }
    
    return { sql, params };
  }
  
  /**
   * 为INSERT查询添加用户ID
   * @param {string} sql - 原始SQL
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Object} 增强后的SQL和参数
   */
  enhanceInsertQuery(sql, params, userId) {
    const upperSql = sql.toUpperCase();
    
    // 检查是否是需要隔离的表
    const targetTable = this.extractTableFromInsert(sql);
    if (!targetTable || !this.isolatedTables.includes(targetTable.toLowerCase())) {
      return { sql, params };
    }
    
    // 检查是否已经包含user_id
    if (upperSql.includes('USER_ID')) {
      return { sql, params };
    }
    
    // 为INSERT语句添加user_id字段
    const insertMatch = sql.match(/INSERT\s+INTO\s+\w+\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)/i);
    if (insertMatch) {
      const fields = insertMatch[1];
      const values = insertMatch[2];
      
      const newFields = fields + ', user_id';
      const newValues = values + ', ?';
      
      sql = sql.replace(/\([^)]+\)\s*VALUES\s*\([^)]+\)/i, `(${newFields}) VALUES (${newValues})`);
      params.push(userId);
    }
    
    return { sql, params };
  }
  
  /**
   * 从INSERT语句中提取表名
   * @param {string} sql - INSERT SQL语句
   * @returns {string|null} 表名
   */
  extractTableFromInsert(sql) {
    const match = sql.match(/INSERT\s+INTO\s+(\w+)/i);
    return match ? match[1] : null;
  }
  
  /**
   * 找到WHERE子句的插入位置
   * @param {string} sql - SQL语句
   * @returns {number} 插入位置
   */
  findWhereInsertPosition(sql) {
    const upperSql = sql.toUpperCase();

    // 查找ORDER BY, GROUP BY, HAVING, LIMIT等子句
    const clauses = ['ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT', 'OFFSET'];

    for (const clause of clauses) {
      const index = upperSql.indexOf(clause);
      if (index !== -1) {
        // 找到子句的开始位置，需要向前查找合适的插入点
        // 确保在子句前有适当的空格
        let insertPos = index;
        while (insertPos > 0 && sql[insertPos - 1] === ' ') {
          insertPos--;
        }
        return insertPos;
      }
    }

    // 如果没有找到这些子句，在SQL末尾插入
    return sql.length;
  }
  
  /**
   * 主要的SQL增强方法
   * @param {string} sql - 原始SQL
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Object} 增强后的SQL和参数
   */
  enhanceQuery(sql, params = [], userId) {
    // 如果不需要用户过滤，直接返回
    if (!this.needsUserFilter(sql)) {
      return { sql, params };
    }
    
    // 如果已经包含user_id条件，直接返回
    if (sql.toLowerCase().includes('user_id')) {
      return { sql, params };
    }
    
    const upperSql = sql.toUpperCase().trim();
    
    try {
      if (upperSql.startsWith('SELECT')) {
        return this.enhanceSelectQuery(sql, [...params], userId);
      } else if (upperSql.startsWith('UPDATE')) {
        return this.enhanceUpdateQuery(sql, [...params], userId);
      } else if (upperSql.startsWith('DELETE')) {
        return this.enhanceDeleteQuery(sql, [...params], userId);
      } else if (upperSql.startsWith('INSERT')) {
        return this.enhanceInsertQuery(sql, [...params], userId);
      }
    } catch (error) {
      console.error('[SQL增强] 处理失败:', error);
      console.error('原始SQL:', sql);
      // 安全降级：返回原始SQL
      return { sql, params };
    }
    
    return { sql, params };
  }
  
  /**
   * 执行带用户过滤的查询
   * @param {string} sql - SQL语句
   * @param {Array} params - 参数数组
   * @param {number} userId - 用户ID
   * @returns {Promise} 查询结果
   */
  async executeWithUserFilter(sql, params, userId) {
    const { sql: enhancedSql, params: enhancedParams } = this.enhanceQuery(sql, params, userId);

    // 注释掉频繁的数据库查询日志，减少日志噪音
    // console.log(`[数据库查询] 用户${userId}执行查询:`);
    // console.log(`SQL: ${enhancedSql}`);
    // console.log(`参数: [${enhancedParams.join(', ')}]`);

    return await this.pool.execute(enhancedSql, enhancedParams);
  }
  
  /**
   * 执行带用户ID的插入
   * @param {string} tableName - 表名
   * @param {Object} data - 数据对象
   * @param {number} userId - 用户ID
   * @returns {Promise} 插入结果
   */
  async insertWithUserId(tableName, data, userId) {
    // 如果是需要隔离的表，自动添加user_id
    if (this.isolatedTables.includes(tableName.toLowerCase())) {
      data = { ...data, user_id: userId };
    }
    
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');
    
    const sql = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
    
    // 注释掉频繁的数据库插入日志，减少日志噪音
    // console.log(`[数据库插入] 用户${userId}插入数据到${tableName}:`);
    // console.log(`SQL: ${sql}`);
    // console.log(`数据:`, data);
    
    return await this.pool.execute(sql, values);
  }
  
  /**
   * 批量插入带用户ID的数据
   * @param {string} tableName - 表名
   * @param {Array} dataArray - 数据数组
   * @param {number} userId - 用户ID
   * @returns {Promise} 插入结果
   */
  async batchInsertWithUserId(tableName, dataArray, userId) {
    if (!dataArray || dataArray.length === 0) {
      return { affectedRows: 0 };
    }
    
    // 为每条数据添加user_id
    const enhancedData = dataArray.map(data => {
      if (this.isolatedTables.includes(tableName.toLowerCase())) {
        return { ...data, user_id: userId };
      }
      return data;
    });
    
    const fields = Object.keys(enhancedData[0]);
    const placeholders = fields.map(() => '?').join(', ');
    const valuesSql = enhancedData.map(() => `(${placeholders})`).join(', ');
    
    const sql = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES ${valuesSql}`;
    const params = enhancedData.flatMap(data => Object.values(data));
    
    console.log(`[数据库批量插入] 用户${userId}批量插入${dataArray.length}条数据到${tableName}`);
    
    return await this.pool.execute(sql, params);
  }
}

module.exports = DatabaseQueryEnhancer;
