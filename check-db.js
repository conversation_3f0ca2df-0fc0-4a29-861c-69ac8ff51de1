const mysql = require('mysql2/promise');

async function checkDatabase() {
  const pool = mysql.createPool({
    host: 'localhost',
    user: 'autojs_control',
    password: 'root',
    database: 'autojs_control',
    charset: 'utf8mb4'
  });

  try {
    console.log('检查数据库表...');
    
    // 检查表是否存在
    const [tables] = await pool.execute("SHOW TABLES LIKE 'device_connection_codes'");
    if (tables.length > 0) {
      console.log('✅ device_connection_codes 表存在');
    } else {
      console.log('❌ device_connection_codes 表不存在');
    }

    const [tables2] = await pool.execute("SHOW TABLES LIKE 'device_connections'");
    if (tables2.length > 0) {
      console.log('✅ device_connections 表存在');
    } else {
      console.log('❌ device_connections 表不存在');
    }

    // 检查devices表是否有user_id字段
    const [columns] = await pool.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'autojs_control' 
        AND TABLE_NAME = 'devices' 
        AND COLUMN_NAME = 'user_id'
    `);
    
    if (columns.length > 0) {
      console.log('✅ devices表有user_id字段');
    } else {
      console.log('❌ devices表缺少user_id字段');
    }

  } catch (error) {
    console.error('数据库检查失败:', error.message);
  } finally {
    await pool.end();
  }
}

checkDatabase();
