-- 为xiaohongshu_video_files表添加user_id字段以支持数据隔离
-- 执行时间：2025-08-07
-- 目的：修复视频统计功能的数据隔离问题

USE autojs_control;

-- 1. 为 xiaohongshu_video_files 表添加 user_id 字段
ALTER TABLE xiaohongshu_video_files 
ADD COLUMN user_id INT(11) NOT NULL DEFAULT 1 COMMENT '用户ID，用于数据隔离' AFTER id,
ADD INDEX idx_xiaohongshu_video_files_user_id (user_id);

-- 2. 为现有数据设置正确的user_id值
-- 首先尝试通过 uploaded_by 字段匹配用户名
UPDATE xiaohongshu_video_files xvf 
JOIN users u ON u.username = xvf.uploaded_by 
SET xvf.user_id = u.id 
WHERE xvf.user_id = 1;

-- 3. 为 xiaohongshu_video_transfers 表添加 user_id 字段（如果还没有）
-- 检查表是否存在user_id字段
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = 'autojs_control' 
    AND TABLE_NAME = 'xiaohongshu_video_transfers' 
    AND COLUMN_NAME = 'user_id'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE xiaohongshu_video_transfers ADD COLUMN user_id INT(11) NOT NULL DEFAULT 1 COMMENT ''用户ID，用于数据隔离'' AFTER id, ADD INDEX idx_xiaohongshu_video_transfers_user_id (user_id)',
  'SELECT ''user_id字段已存在于xiaohongshu_video_transfers表'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为 xiaohongshu_video_assignments 表添加 user_id 字段（如果还没有）
-- 检查表是否存在user_id字段
SET @column_exists = (
  SELECT COUNT(*) 
  FROM INFORMATION_SCHEMA.COLUMNS 
  WHERE TABLE_SCHEMA = 'autojs_control' 
    AND TABLE_NAME = 'xiaohongshu_video_assignments' 
    AND COLUMN_NAME = 'user_id'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0, 
  'ALTER TABLE xiaohongshu_video_assignments ADD COLUMN user_id INT(11) NOT NULL DEFAULT 1 COMMENT ''用户ID，用于数据隔离'' AFTER id, ADD INDEX idx_xiaohongshu_video_assignments_user_id (user_id)',
  'SELECT ''user_id字段已存在于xiaohongshu_video_assignments表'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 更新相关表的user_id值
-- 为xiaohongshu_video_transfers表设置user_id
UPDATE xiaohongshu_video_transfers xvt 
JOIN xiaohongshu_video_files xvf ON xvt.video_id = xvf.id 
SET xvt.user_id = xvf.user_id 
WHERE xvt.user_id = 1;

-- 为xiaohongshu_video_assignments表设置user_id
UPDATE xiaohongshu_video_assignments xva 
JOIN xiaohongshu_video_files xvf ON xva.video_id = xvf.id 
SET xva.user_id = xvf.user_id 
WHERE xva.user_id = 1;

-- 6. 验证数据完整性
SELECT 
  'xiaohongshu_video_files' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN user_id > 0 THEN 1 END) as records_with_user_id,
  COUNT(CASE WHEN user_id IS NULL OR user_id = 0 THEN 1 END) as records_without_user_id
FROM xiaohongshu_video_files

UNION ALL

SELECT 
  'xiaohongshu_video_transfers' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN user_id > 0 THEN 1 END) as records_with_user_id,
  COUNT(CASE WHEN user_id IS NULL OR user_id = 0 THEN 1 END) as records_without_user_id
FROM xiaohongshu_video_transfers

UNION ALL

SELECT 
  'xiaohongshu_video_assignments' as table_name,
  COUNT(*) as total_records,
  COUNT(CASE WHEN user_id > 0 THEN 1 END) as records_with_user_id,
  COUNT(CASE WHEN user_id IS NULL OR user_id = 0 THEN 1 END) as records_without_user_id
FROM xiaohongshu_video_assignments;

-- 7. 显示完成信息
SELECT '✅ xiaohongshu_video_files表user_id字段添加完成，视频统计功能数据隔离修复完成！' as status;
