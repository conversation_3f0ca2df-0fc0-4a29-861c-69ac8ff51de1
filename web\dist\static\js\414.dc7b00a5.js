"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[414],{1414:function(t,e,a){a.r(e),a.d(e,{default:function(){return d}});var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"activation-codes-container"},[e("el-card",{staticClass:"page-header",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",{staticClass:"page-title"},[t._v("卡密/激活码管理")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[t._v(" 刷新 ")])],1),e("div",{staticClass:"stats-row"},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.basic?.total_codes||0))]),e("div",{staticClass:"stat-label"},[t._v("总卡密数")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.basic?.active_codes||0))]),e("div",{staticClass:"stat-label"},[t._v("可用卡密")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.basic?.used_codes||0))]),e("div",{staticClass:"stat-label"},[t._v("已使用")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.stats.basic?.total_uses||0))]),e("div",{staticClass:"stat-label"},[t._v("总使用次数")])])])],1)],1)]),e("el-card",{staticClass:"generate-card",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("生成新卡密")])]),e("el-form",{ref:"generateForm",attrs:{model:t.generateForm,rules:t.generateRules,"label-width":"120px"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"生成数量",prop:"count"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:1e3},model:{value:t.generateForm.count,callback:function(e){t.$set(t.generateForm,"count",e)},expression:"generateForm.count"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"卡密类型",prop:"type"}},[e("el-select",{staticStyle:{width:"100%"},model:{value:t.generateForm.type,callback:function(e){t.$set(t.generateForm,"type",e)},expression:"generateForm.type"}},[e("el-option",{attrs:{label:"正式卡密",value:"card"}}),e("el-option",{attrs:{label:"激活码",value:"activation"}}),e("el-option",{attrs:{label:"试用卡密",value:"trial"}})],1)],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"有效天数",prop:"durationDays"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:3650},model:{value:t.generateForm.durationDays,callback:function(e){t.$set(t.generateForm,"durationDays",e)},expression:"generateForm.durationDays"}})],1)],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"最大使用次数",prop:"maxUses"}},[e("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:100},model:{value:t.generateForm.maxUses,callback:function(e){t.$set(t.generateForm,"maxUses",e)},expression:"generateForm.maxUses"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"批次ID",prop:"batchId"}},[e("el-input",{attrs:{placeholder:"可选，用于批量管理"},model:{value:t.generateForm.batchId,callback:function(e){t.$set(t.generateForm,"batchId",e)},expression:"generateForm.batchId"}})],1)],1),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"卡密过期时间"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"datetime",placeholder:"选择过期时间","picker-options":{disabledDate(t){return t.getTime()<Date.now()}}},model:{value:t.generateForm.expiresAt,callback:function(e){t.$set(t.generateForm,"expiresAt",e)},expression:"generateForm.expiresAt"}})],1)],1)],1),e("el-form-item",{attrs:{label:"描述信息"}},[e("el-input",{attrs:{type:"textarea",rows:2,placeholder:"可选，描述这批卡密的用途"},model:{value:t.generateForm.description,callback:function(e){t.$set(t.generateForm,"description",e)},expression:"generateForm.description"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",loading:t.generating},on:{click:t.generateCodes}},[t._v(" "+t._s(t.generating?"生成中...":"生成卡密")+" ")]),e("el-button",{on:{click:t.resetGenerateForm}},[t._v("重置")])],1)],1)],1),e("el-card",{staticClass:"list-card",attrs:{shadow:"never"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("卡密列表")]),e("div",{staticStyle:{float:"right"}},[e("el-input",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"搜索卡密或描述","prefix-icon":"el-icon-search"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadCodes.apply(null,arguments)}},model:{value:t.searchForm.search,callback:function(e){t.$set(t.searchForm,"search",e)},expression:"searchForm.search"}}),e("el-select",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{placeholder:"类型"},on:{change:t.loadCodes},model:{value:t.searchForm.type,callback:function(e){t.$set(t.searchForm,"type",e)},expression:"searchForm.type"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"正式卡密",value:"card"}}),e("el-option",{attrs:{label:"激活码",value:"activation"}}),e("el-option",{attrs:{label:"试用卡密",value:"trial"}})],1),e("el-select",{staticStyle:{width:"120px","margin-right":"10px"},attrs:{placeholder:"状态"},on:{change:t.loadCodes},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"可用",value:"active"}}),e("el-option",{attrs:{label:"已用完",value:"used"}}),e("el-option",{attrs:{label:"已过期",value:"expired"}}),e("el-option",{attrs:{label:"已禁用",value:"disabled"}})],1),e("el-button",{attrs:{type:"primary"},on:{click:t.loadCodes}},[t._v("搜索")])],1)]),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.codes,"default-sort":{prop:"created_at",order:"descending"}}},[e("el-table-column",{attrs:{prop:"code",label:"卡密/激活码",width:"200"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tooltip",{attrs:{content:a.row.code,placement:"top"}},[e("span",{staticClass:"code-text",on:{click:function(e){return t.copyCode(a.row.code)}}},[t._v(" "+t._s(a.row.code)+" ")])])]}}])}),e("el-table-column",{attrs:{prop:"type",label:"类型",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"card"===a.row.type?"primary":"trial"===a.row.type?"warning":"success",size:"small"}},[t._v(" "+t._s("card"===a.row.type?"正式":"trial"===a.row.type?"试用":"激活码")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"duration_days",label:"有效天数",width:"100"}}),e("el-table-column",{attrs:{label:"使用情况",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.used_count)+"/"+t._s(a.row.max_uses))]),e("el-progress",{staticStyle:{"margin-top":"2px"},attrs:{percentage:Math.round(a.row.used_count/a.row.max_uses*100),"stroke-width":6,"show-text":!1}})]}}])}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:t.getStatusType(a.row),size:"small"}},[t._v(" "+t._s(t.getStatusText(a.row))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"batch_id",label:"批次ID",width:"150","show-overflow-tooltip":""}}),e("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.created_at))+" ")]}}])}),e("el-table-column",{attrs:{prop:"expires_at",label:"过期时间",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.expires_at?t.formatDate(e.row.expires_at):"永不过期")+" ")]}}])}),e("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"mini",type:"active"===a.row.status?"warning":"success",disabled:a.row.used_count>0},on:{click:function(e){return t.toggleStatus(a.row)}}},[t._v(" "+t._s("active"===a.row.status?"禁用":"启用")+" ")]),e("el-button",{attrs:{size:"mini",type:"danger",disabled:a.row.used_count>0},on:{click:function(e){return t.deleteCode(a.row)}}},[t._v(" 删除 ")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50,100],"page-size":t.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-dialog",{attrs:{title:"卡密生成成功",visible:t.showGenerateResult,width:"60%","close-on-click-modal":!1},on:{"update:visible":function(e){t.showGenerateResult=e}}},[t.generateResult?e("div",[e("p",[t._v("成功生成 "),e("strong",[t._v(t._s(t.generateResult.count))]),t._v(" 个卡密，批次ID: "),e("strong",[t._v(t._s(t.generateResult.batchId))])]),e("el-input",{attrs:{type:"textarea",rows:10,value:t.generateResult.codes.join("\n"),readonly:"",placeholder:"生成的卡密列表"}}),e("div",{staticStyle:{"margin-top":"10px"}},[e("el-button",{attrs:{type:"primary"},on:{click:t.copyAllCodes}},[t._v("复制所有卡密")]),e("el-button",{on:{click:t.downloadCodes}},[t._v("下载为文件")])],1)],1):t._e(),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showGenerateResult=!1}}},[t._v("关闭")])],1)])],1)},r=[],o=a(4335),l={name:"ActivationCodes",data(){return{loading:!1,generating:!1,codes:[],stats:{},showGenerateResult:!1,generateResult:null,generateForm:{count:10,type:"card",durationDays:30,maxUses:1,batchId:"",description:"",expiresAt:null},generateRules:{count:[{required:!0,message:"请输入生成数量",trigger:"blur"},{type:"number",min:1,max:1e3,message:"数量必须在1-1000之间",trigger:"blur"}],type:[{required:!0,message:"请选择卡密类型",trigger:"change"}],durationDays:[{required:!0,message:"请输入有效天数",trigger:"blur"},{type:"number",min:1,max:3650,message:"天数必须在1-3650之间",trigger:"blur"}],maxUses:[{required:!0,message:"请输入最大使用次数",trigger:"blur"},{type:"number",min:1,max:100,message:"次数必须在1-100之间",trigger:"blur"}]},searchForm:{search:"",type:"",status:"",batchId:""},pagination:{page:1,limit:20,total:0}}},mounted(){this.loadStats(),this.loadCodes()},methods:{async loadStats(){try{const t=await o.A.get("/api/admin/activation-codes/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});t.data.success&&(this.stats=t.data.data)}catch(t){console.error("加载统计信息失败:",t)}},async loadCodes(){try{this.loading=!0;const t={page:this.pagination.page,limit:this.pagination.limit,...this.searchForm},e=await o.A.get("/api/admin/activation-codes",{params:t,headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});e.data.success&&(this.codes=e.data.data.codes,this.pagination=e.data.data.pagination)}catch(t){console.error("加载卡密列表失败:",t),this.$message.error("加载卡密列表失败")}finally{this.loading=!1}},async generateCodes(){try{await this.$refs.generateForm.validate(),this.generating=!0;const t=await o.A.post("/api/admin/activation-codes/generate",this.generateForm,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});t.data.success&&(this.generateResult=t.data.data,this.showGenerateResult=!0,this.$message.success("卡密生成成功"),this.loadStats(),this.loadCodes(),this.resetGenerateForm())}catch(t){console.error("生成卡密失败:",t),this.$message.error(t.response?.data?.message||"生成卡密失败")}finally{this.generating=!1}},resetGenerateForm(){this.generateForm={count:10,type:"card",durationDays:30,maxUses:1,batchId:"",description:"",expiresAt:null},this.$refs.generateForm?.resetFields()},async toggleStatus(t){try{const e="active"===t.status?"disabled":"active";await o.A.put(`/api/admin/activation-codes/${t.id}/status`,{status:e},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("状态更新成功"),this.loadCodes(),this.loadStats()}catch(e){console.error("更新状态失败:",e),this.$message.error("更新状态失败")}},async deleteCode(t){try{await this.$confirm("确定要删除这个卡密吗？删除后无法恢复。","确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await o.A.delete(`/api/admin/activation-codes/${t.id}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),this.$message.success("删除成功"),this.loadCodes(),this.loadStats()}catch(e){"cancel"!==e&&(console.error("删除失败:",e),this.$message.error(e.response?.data?.message||"删除失败"))}},copyCode(t){navigator.clipboard.writeText(t).then(()=>{this.$message.success("卡密已复制到剪贴板")}).catch(()=>{this.$message.error("复制失败")})},copyAllCodes(){const t=this.generateResult.codes.join("\n");navigator.clipboard.writeText(t).then(()=>{this.$message.success("所有卡密已复制到剪贴板")}).catch(()=>{this.$message.error("复制失败")})},downloadCodes(){const t=this.generateResult.codes.join("\n"),e=new Blob([t],{type:"text/plain"}),a=window.URL.createObjectURL(e),s=document.createElement("a");s.href=a,s.download=`activation_codes_${this.generateResult.batchId}_${(new Date).getTime()}.txt`,s.click(),window.URL.revokeObjectURL(a)},refreshData(){this.loadStats(),this.loadCodes()},handleSizeChange(t){this.pagination.limit=t,this.pagination.page=1,this.loadCodes()},handleCurrentChange(t){this.pagination.page=t,this.loadCodes()},getStatusType(t){return t.isAvailable?"active"===t.status?"success":"used"===t.status?"info":"expired"===t.status?"warning":"danger":"danger"},getStatusText(t){return t.isExpired?"已过期":t.used_count>=t.max_uses?"已用完":"active"===t.status?"可用":"disabled"===t.status?"已禁用":t.status},formatDate(t){return t?new Date(t).toLocaleString("zh-CN"):""}}},i=l,n=a(1656),c=(0,n.A)(i,s,r,!1,null,"3e2681fd",null),d=c.exports}}]);