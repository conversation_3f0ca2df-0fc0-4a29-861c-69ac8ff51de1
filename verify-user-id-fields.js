const mysql = require('mysql2/promise');

async function verifyUserIdFields() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'autojs_control',
      password: 'root',
      database: 'autojs_control'
    });

    console.log('🔗 连接数据库成功');

    // 检查需要验证的表
    const tablesToCheck = [
      'activation_codes',
      'users_backup_isolation', 
      'xiaohongshu_videos'
    ];

    console.log('\n📋 === 验证user_id字段添加情况 ===');

    for (const tableName of tablesToCheck) {
      try {
        // 获取表结构
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
        
        // 检查是否有user_id字段
        const userIdColumn = columns.find(col => col.Field === 'user_id');
        
        if (userIdColumn) {
          console.log(`✅ ${tableName}: user_id字段已存在`);
          console.log(`   类型: ${userIdColumn.Type}`);
          console.log(`   允许NULL: ${userIdColumn.Null}`);
          console.log(`   默认值: ${userIdColumn.Default}`);
          console.log(`   键: ${userIdColumn.Key}`);
          
          // 检查数据情况
          const [countResult] = await connection.execute(`
            SELECT 
              COUNT(*) as total_records,
              COUNT(user_id) as records_with_user_id,
              COUNT(*) - COUNT(user_id) as records_without_user_id
            FROM ${tableName}
          `);
          
          const stats = countResult[0];
          console.log(`   数据统计: 总记录${stats.total_records}条, 有user_id的${stats.records_with_user_id}条, 缺少user_id的${stats.records_without_user_id}条`);
          
        } else {
          console.log(`❌ ${tableName}: user_id字段不存在`);
        }
        
        console.log('');
      } catch (error) {
        console.error(`❌ 检查表 ${tableName} 失败:`, error.message);
      }
    }

    // 检查所有表的user_id字段情况
    console.log('\n📊 === 所有表的user_id字段统计 ===');
    
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'autojs_control' 
      AND TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);

    let tablesWithUserId = 0;
    let tablesWithoutUserId = 0;

    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      
      const [columns] = await connection.execute(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'autojs_control' 
        AND TABLE_NAME = ? 
        AND COLUMN_NAME = 'user_id'
      `, [tableName]);

      if (columns.length > 0) {
        tablesWithUserId++;
        console.log(`✅ ${tableName}`);
      } else {
        tablesWithoutUserId++;
        console.log(`❌ ${tableName}`);
      }
    }

    console.log(`\n📈 统计结果:`);
    console.log(`   有user_id字段的表: ${tablesWithUserId}个`);
    console.log(`   没有user_id字段的表: ${tablesWithoutUserId}个`);
    console.log(`   总表数: ${tables.length}个`);

    if (tablesWithoutUserId === 0) {
      console.log('\n🎉 所有业务表都已支持用户数据隔离！');
    } else {
      console.log('\n⚠️  还有部分表需要添加user_id字段');
    }

  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔚 数据库连接已关闭');
    }
  }
}

// 执行验证
verifyUserIdFields();
