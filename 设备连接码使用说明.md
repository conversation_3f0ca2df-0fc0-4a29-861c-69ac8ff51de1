# 设备连接码使用说明

## 概述

设备连接码功能允许用户为自己的账号生成专用的连接码，设备使用连接码连接后会自动分配给对应的用户，无需手动分配。

## 功能特点

1. **用户隔离**：每个用户只能管理自己的连接码和设备
2. **灵活配置**：支持设置连接码描述、最大设备数、有效期
3. **安全可控**：连接码可以随时删除，支持使用次数限制
4. **向下兼容**：设备仍可使用传统的admin登录方式

## 使用步骤

### 1. 创建连接码（Web端）

1. 登录Web管理界面
2. 进入"设备管理"页面
3. 点击"连接码管理"按钮
4. 点击"创建连接码"
5. 配置连接码参数：
   - **描述**：连接码用途说明（可选）
   - **最大设备数**：此连接码最多可连接的设备数量
   - **有效期**：连接码过期时间（可选择永不过期）
6. 点击"创建"生成8位连接码

### 2. 设备连接（手机端）

1. 运行设备脚本（如：双向.js）
2. 在"连接码(可选)"输入框中输入8位连接码
3. 点击"连接"按钮
4. 设备会自动分配给连接码对应的用户

### 3. 管理连接码

在连接码管理界面可以：
- **查看状态**：显示连接码使用情况、过期状态
- **复制连接码**：一键复制到剪贴板
- **删除连接码**：删除不需要的连接码

## 连接码状态说明

- **可用**：连接码正常，可以使用
- **已满**：已达到最大设备数限制
- **已过期**：连接码已过期，无法使用

## 使用场景

### 场景1：多用户环境
- 管理员为每个用户创建专用连接码
- 用户使用自己的连接码连接设备
- 实现设备自动分配，无需手动管理

### 场景2：临时设备接入
- 创建短期有效的连接码
- 临时设备使用连接码快速接入
- 过期后自动失效，提高安全性

### 场景3：设备数量控制
- 设置最大设备数限制
- 防止单个用户连接过多设备
- 合理分配系统资源

## 兼容性说明

### 新连接方式（推荐）
```
设备脚本 -> 输入连接码 -> 自动分配给用户
```

### 传统连接方式（仍支持）
```
设备脚本 -> admin登录 -> 分配给admin
```

## 注意事项

1. **连接码安全**：请妥善保管连接码，避免泄露
2. **设备唯一性**：同一设备使用相同连接码重复连接不会重复计数
3. **权限控制**：只有连接码创建者可以管理该连接码
4. **数据隔离**：不同用户的设备和数据完全隔离

## 故障排除

### 连接码验证失败
- 检查连接码是否正确（8位大写字母和数字）
- 确认连接码未过期
- 检查是否已达到最大设备数限制

### 设备连接失败
- 确认服务器地址正确
- 检查网络连接
- 查看服务器日志获取详细错误信息

### 设备未正确分配
- 确认连接码属于正确的用户
- 检查设备是否已在其他用户下注册
- 联系管理员重新分配设备

## API接口说明

### 创建连接码
```
POST /api/device/connection-codes
{
  "description": "测试连接码",
  "maxDevices": 5,
  "expiresInHours": 24
}
```

### 验证连接码
```
POST /api/device/verify-connection-code
{
  "code": "ABC12345",
  "deviceId": "device_001",
  "deviceName": "测试设备",
  "deviceInfo": {...}
}
```

### 设备注册（支持连接码）
```
POST /api/device/register
{
  "deviceId": "device_001",
  "deviceName": "测试设备",
  "deviceInfo": {...},
  "connectionCode": "ABC12345"  // 可选
}
```

## 总结

设备连接码功能提供了一种安全、便捷的设备分配机制，特别适合多用户环境。通过合理配置连接码参数，可以实现灵活的设备管理和用户隔离。
