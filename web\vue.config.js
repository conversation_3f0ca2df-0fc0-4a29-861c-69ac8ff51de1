const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    host: '0.0.0.0', // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:3002', // 使用localhost避免跨域问题
        changeOrigin: true,
        ws: true,
        secure: false,
        logLevel: 'debug',
        onProxyReq: function(proxyReq, req, res) {
          console.log('代理API请求:', req.method, req.url, '-> http://localhost:3002' + req.url)
        },
        onError: function(err, req, res) {
          console.error('代理错误:', err.message)
        }
      },
      '/socket.io': {
        target: 'http://localhost:3002', // 使用localhost避免跨域问题
        changeOrigin: true,
        ws: true,
        secure: false,
        logLevel: 'debug',
        onProxyReq: function(proxyReq, req, res) {
          console.log('🔧 代理Socket.IO请求:', req.method, req.url, '-> http://localhost:3002' + req.url)
        },
        onError: function(err, req, res) {
          console.error('❌ Socket.IO代理错误:', err.message)
        },
        onProxyReqWs: function(proxyReq, req, socket, options, head) {
          console.log('🚀 代理WebSocket升级请求:', req.url, '-> ws://localhost:3002' + req.url)
        },
        onProxyResWs: function(proxyRes, req, socket, head) {
          console.log('✅ WebSocket代理响应成功:', req.url)
        }
      }
    }
  },
  
  // 生产环境构建配置
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  
  // 关闭生产环境的source map
  productionSourceMap: false,
  
  // 配置webpack
  configureWebpack: {
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    }
  }
})
