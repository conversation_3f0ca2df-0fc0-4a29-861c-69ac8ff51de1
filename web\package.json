{"name": "autojs-web-control", "version": "1.0.0", "description": "Auto.js云群控系统Web前端", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.4.0", "bcrypt": "^6.0.0", "element-ui": "^2.15.13", "js-cookie": "^3.0.5", "moment": "^2.29.4", "socket.io-client": "^4.7.2", "vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}