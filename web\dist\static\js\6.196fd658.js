"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[6],{6006:function(e,t,s){s.d(t,{ensureConnection:function(){return a},getWebSocketManager:function(){return h}});var n=s(4787),o=s(4310),i=s(9381);class c{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=10,this.reconnectDelay=1e3,this.maxReconnectDelay=3e4,this.eventHandlers=new Map,this.isInitialized=!1,this.reconnectTimer=null,this.heartbeatTimer=null,this.heartbeatInterval=3e4,this.connectionPromise=null}async init(){if(o.A.getters["auth/isAuthenticated"])return this.isInitialized&&this.isConnected?(console.log("WebSocket管理器已初始化且连接正常"),this.connectionPromise):(console.log("初始化WebSocket管理器..."),this.isInitialized=!0,this.connectionPromise||(this.connectionPromise=this.connect()),this.connectionPromise);console.log("用户未认证，跳过WebSocket初始化")}async connect(){try{console.log("建立WebSocket连接..."),this.cleanup();const e=(0,i.getWebSocketUrl)()||"http://localhost:3002";console.log("连接到WebSocket服务器:",e),this.socket=(0,n.Ay)(e,{transports:["websocket"],timeout:1e4,reconnection:!1,forceNew:!0,upgrade:!1});const t=setTimeout(()=>{console.error("WebSocket连接超时"),this.handleConnectionError(new Error("连接超时"))},15e3);return new Promise((e,s)=>{this.socket.on("connect",()=>{clearTimeout(t),console.log("✅ WebSocket连接成功"),this.isConnected=!0,this.reconnectAttempts=0,this.reconnectDelay=1e3,this.setupEventHandlers(),this.startHeartbeat(),o.A.dispatch("socket/connect",this.socket),this.emitEvent("connection_established",{type:"websocket"}),setTimeout(()=>{this.requestDeviceStatusSync()},1e3),e()}),this.socket.on("connect_error",e=>{clearTimeout(t),console.error("❌ WebSocket连接失败:",e.message),this.handleConnectionError(e),s(e)}),this.socket.on("disconnect",e=>{console.log("WebSocket连接断开:",e),this.isConnected=!1,this.stopHeartbeat(),"io client disconnect"!==e&&this.scheduleReconnect()})})}catch(e){throw console.error("WebSocket连接异常:",e),this.handleConnectionError(e),e}}setupEventHandlers(){this.socket&&(this.socket.on("devices_list",e=>{console.log("WebSocket收到设备列表:",e),this.emitEvent("devices_list",e)}),this.socket.on("device_status_changed",e=>{console.log("WebSocket收到设备状态变化:",e),this.emitEvent("device_status_changed",e)}),this.socket.on("device_status_update",e=>{console.log("WebSocket收到设备状态更新:",e),this.emitEvent("device_status_update",e),o.A&&e.deviceId&&(o.A.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:e.status,last_seen:e.lastSeen||(new Date).toISOString()}}),console.log(`已更新store中设备 ${e.deviceId} 状态为: ${e.status}`))}),this.socket.on("device_status_changed",e=>{if(console.log("WebSocket收到设备状态变化:",e),this.emitEvent("device_status_changed",e),o.A&&e.deviceId){const t="device_health_warning"===e.type?"offline":"online";o.A.commit("device/UPDATE_DEVICE",{deviceId:e.deviceId,updates:{status:t,last_seen:e.lastSeen||(new Date).toISOString()}}),console.log(`健康检查：已更新store中设备 ${e.deviceId} 状态为: ${t}`)}}),this.socket.on("xiaohongshu_realtime_status",e=>{console.log("WebSocket收到小红书实时状态:",e),this.emitEvent("xiaohongshu_realtime_status",e)}),this.socket.on("xianyu_realtime_status",e=>{console.log("WebSocket收到闲鱼实时状态:",e),this.emitEvent("xianyu_realtime_status",e)}),this.socket.on("server_shutdown",e=>{console.log("收到服务器关闭通知:",e),this.emitEvent("server_shutdown",e),this.isConnected=!1,this.isInitialized=!1}),this.socket.on("pong",()=>{console.log("收到心跳响应")}),this.socket.on("test_event",e=>{console.log("收到测试事件:",e),this.emitEvent("test_event",e)}))}startHeartbeat(){this.stopHeartbeat(),this.heartbeatTimer=setInterval(()=>{this.socket&&this.isConnected&&this.socket.emit("ping")},this.heartbeatInterval)}stopHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}handleConnectionError(e){console.error("WebSocket连接错误:",e),this.isConnected=!1,this.connectionPromise=null,this.scheduleReconnect()}scheduleReconnect(){if(this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectAttempts>=this.maxReconnectAttempts)return console.error("❌ WebSocket重连次数已达上限，停止重连"),void this.emitEvent("connection_failed",{reason:"重连次数超限",attempts:this.reconnectAttempts});this.reconnectAttempts++;const e=Math.min(this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1),this.maxReconnectDelay);console.log(`🔄 安排WebSocket重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${e}ms后重试`),this.reconnectTimer=setTimeout(()=>{o.A.getters["auth/isAuthenticated"]&&(this.connectionPromise=this.connect())},e)}cleanup(){this.stopHeartbeat(),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.isConnected=!1}disconnect(){console.log("主动断开WebSocket连接"),this.isInitialized=!1,this.connectionPromise=null,this.cleanup()}emit(e,t){return this.socket&&this.isConnected?(this.socket.emit(e,t),!0):(console.warn("WebSocket未连接，无法发送消息:",e),!1)}on(e,t){this.eventHandlers.has(e)||this.eventHandlers.set(e,new Set),this.eventHandlers.get(e).add(t)}off(e,t){this.eventHandlers.has(e)&&(t?this.eventHandlers.get(e).delete(t):this.eventHandlers.get(e).clear())}emitEvent(e,t){this.eventHandlers.has(e)&&this.eventHandlers.get(e).forEach(s=>{try{s(t)}catch(n){console.error(`事件处理器执行失败 [${e}]:`,n)}})}getConnectionStatus(){return{isConnected:this.isConnected,isInitialized:this.isInitialized,reconnectAttempts:this.reconnectAttempts,type:"websocket"}}forceReconnect(){return console.log("强制重连WebSocket"),this.reconnectAttempts=0,this.connectionPromise=null,this.cleanup(),this.init()}requestDeviceStatusSync(){console.log("🔄 请求设备状态同步..."),this.isConnected&&this.socket&&this.socket.emit("request_device_status_sync")}send(e,t){this.isConnected&&this.socket?this.socket.emit(e,t):console.warn("WebSocket未连接，无法发送消息:",e,t)}}const r=new c;function h(){return r}function a(){return o.A.getters["auth/isAuthenticated"]&&!r.isConnected?(console.log("检测到连接丢失，尝试重新建立连接..."),r.init()):Promise.resolve()}}}]);