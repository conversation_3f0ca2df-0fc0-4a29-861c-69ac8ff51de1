<template>
  <div class="article-comment-config">
    <el-form :model="config" label-width="120px">
      <el-form-item label="搜索关键词">
        <el-input
          v-model="config.searchKeyword"
          placeholder="请输入搜索关键词（如：美食推荐）"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          系统将搜索包含此关键词的文章进行评论
        </div>
        <div style="margin-top: 5px; color: #67C23A; font-size: 12px;">
          当前值: {{ config.searchKeyword }}
        </div>
      </el-form-item>

      <el-form-item label="评论文章数量">
        <el-input-number
          v-model="config.commentCount"
          :min="1"
          :max="50"
        />
        <span style="margin-left: 10px; color: #909399;">要评论的文章数量</span>
      </el-form-item>

      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <el-form-item label="操作间隔">
        <el-input-number
          v-model="config.operationDelay"
          :min="3"
          :max="60"
        />
        <span style="margin-left: 10px; color: #909399;">每次评论间隔时间（秒）</span>
      </el-form-item>

      <!-- 隐藏其他配置项，只保留基本配置 -->
      <!-- 评论内容类型、评论模板、自定义评论、AI评论设置、文章筛选、互动要求、评论策略、内容类型、时间范围、错误处理、备用关键词等配置项已隐藏 -->

      <!-- 调试信息 -->
      <el-form-item label="调试信息">
        <div style="background: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 12px;">
          <div>当前设备ID: <strong>{{ deviceId || '未设置' }}</strong></div>
          <div>当前搜索关键词: <strong>{{ config.searchKeyword }}</strong></div>
          <div>当前评论数量: <strong>{{ config.commentCount }}</strong></div>
          <div>当前操作延迟: <strong>{{ config.operationDelay }}</strong>秒</div>
          <div>脚本运行状态: <strong>{{ isScriptRunning ? '执行中' : '未运行' }}</strong></div>
          <div>脚本完成状态: <strong>{{ isScriptCompleted ? '已完成' : '未完成' }}</strong></div>
        </div>
        <el-button size="small" @click="debugUpdateConfig" style="margin-top: 10px;">
          手动更新配置到父组件
        </el-button>
      </el-form-item>

      <el-alert
        title="使用提醒"
        type="warning"
        :closable="false"
        show-icon
      >
        <div>
          • 请文明评论，遵守社区规范和法律法规<br>
          • 建议设置合理的评论间隔，避免被识别为机器行为<br>
          • 评论内容要与文章相关，避免无意义的刷屏<br>
          • 过度评论可能导致账号被限制，请适度使用
        </div>
      </el-alert>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section" style="margin-bottom: 20px;">
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">实时状态</span>
        </el-divider>

        <div class="status-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div class="status-item">
            <span class="status-label">已评论文章：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ commentedArticleCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">已处理步骤：</span>
            <span class="status-value" style="color: #E6A23C; font-weight: bold;">{{ processedStepCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">搜索尝试次数：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ searchAttemptCount }}</span>
          </div>
        </div>

        <div class="current-status" style="margin-bottom: 15px;">
          <span class="status-label">当前状态：</span>
          <span class="status-value" style="color: #409EFF; font-weight: bold;">{{ currentStatus || '等待开始' }}</span>
        </div>
      </div>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="primary"
          size="small"
          @click="startScript"
          :disabled="isScriptRunning || !canExecute"
          :loading="isScriptRunning"
        >
          {{ isScriptRunning ? '脚本执行中...' : '开始执行' }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
          style="margin-left: 10px;"
        >
          停止脚本
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未运行
        </span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// 移除直接导入socket.io-client，改用WebSocket管理器
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'ArticleCommentConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isScriptRunning: false,
      isScriptCompleted: false,
      // 保存执行时的真实logId和taskId
      currentLogId: null,
      currentTaskId: null,
      config: {
        searchKeyword: '美食推荐',
        commentCount: 3,
        operationDelay: 5,
        selectedApp: '' // 选择的小红书应用
        // 其他配置项已隐藏，只保留基本配置
      },

      // 实时状态变量
      commentedArticleCount: 0,
      processedStepCount: 0,
      searchAttemptCount: 0,
      currentStatus: '等待开始',

      // Socket连接
      socket: null
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && typeof newVal === 'object') {
          this.config = { ...this.config, ...newVal }
        }
      },
      immediate: true
    },
    config: {
      handler(newVal, oldVal) {
        // 避免无限循环，只在真正变化时更新
        if (oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          console.log('配置发生变化，调用updateConfig')
          console.log('新值:', newVal.searchKeyword, newVal.commentCount)
          this.$nextTick(() => {
            this.updateConfig()
          })
        }
      },
      deep: true
    },

    // 监听关键字段的变化，确保用户输入能被捕获
    'config.searchKeyword'(newVal, oldVal) {
      if (newVal !== oldVal) {
        console.log('搜索关键词变化:', oldVal, '->', newVal)
        this.updateConfig()
      }
    },

    'config.commentCount'(newVal, oldVal) {
      if (newVal !== oldVal) {
        console.log('评论数量变化:', oldVal, '->', newVal)
        this.updateConfig()
      }
    },

    'config.operationDelay'(newVal, oldVal) {
      if (newVal !== oldVal) {
        console.log('操作延迟变化:', oldVal, '->', newVal)
        this.updateConfig()
      }
    }
  },
  mounted() {
    // 组件挂载后延迟发送初始配置，避免无限循环
    console.log('ArticleCommentConfig mounted, 发送初始配置:', this.config)
    setTimeout(() => {
      this.updateConfig()
    }, 100)

    // 监听任务恢复事件
    this.$root.$on('xiaohongshu-task-restored', this.handleTaskRestored)

    // 监听任务开始和停止事件
    this.$root.$on('xiaohongshu-task-started', this.handleTaskStarted)
    this.$root.$on('xiaohongshu-task-stopped', this.handleTaskStopped)
    this.$root.$on('xiaohongshu-script-completed', this.handleScriptCompleted)
    // 监听强制重置事件
    this.$root.$on('xiaohongshu-task-stopped', this.handleForceReset)

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      if (data.deviceId === this.deviceId) {
        console.log('[ArticleCommentConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })

    // 恢复组件状态（异步）
    this.restoreComponentState().then(() => {
      console.log('[ArticleCommentConfig] 状态恢复完成，开始初始化Socket连接')
      // 初始化Socket连接
      this.initializeSocket()
    }).catch(error => {
      console.error('[ArticleCommentConfig] 状态恢复失败:', error)
      // 即使恢复失败也要初始化Socket连接
      this.initializeSocket()
    })
  },

  beforeDestroy() {
    // 保存组件状态
    this.saveComponentState()

    // 断开socket连接
    if (this.socket) {
      this.socket.disconnect()
      console.log('[ArticleCommentConfig] Socket连接已断开')
    }

    // 清理事件监听
    this.$root.$off('xiaohongshu-task-restored', this.handleTaskRestored)
    this.$root.$off('xiaohongshu-task-started', this.handleTaskStarted)
    this.$root.$off('xiaohongshu-task-stopped', this.handleTaskStopped)
    this.$root.$off('xiaohongshu-script-completed', this.handleScriptCompleted)
    this.$root.$off('device-offline')
  },
  computed: {
    // 检查是否可以执行
    canExecute() {
      return this.config.searchKeyword &&
             this.config.searchKeyword.trim().length > 0 &&
             this.config.commentCount > 0
    }
  },
  methods: {
    updateConfig() {
      console.log('ArticleCommentConfig updateConfig 被调用:', this.config)
      this.$emit('input', this.config)
      this.$emit('update', this.config)
    },

    // 开始执行脚本
    async startScript() {
      if (!this.canExecute) {
        this.$message.warning('请完善配置参数')
        return
      }

      try {
        console.log('[ArticleCommentConfig] 开始执行文章评论脚本')

        // 通过父组件的方法执行脚本
        this.$emit('execute-script', {
          functionType: 'articleComment',
          config: this.config,
          deviceId: this.deviceId
        })

        console.log('[ArticleCommentConfig] 脚本执行请求已发送')
        // 注意：不在这里更新状态，等待任务开始事件来更新状态
      } catch (error) {
        console.error('[ArticleCommentConfig] 启动脚本失败:', error)
        this.$message.error('启动脚本失败: ' + error.message)
      }
    },

    // 停止脚本执行
    async stopScript() {
      try {
        console.log('[ArticleCommentConfig] 停止文章评论脚本执行')

        // 如果有设备ID，停止特定设备；否则停止所有任务
        let stopUrl = '/api/xiaohongshu/stop'
        let stopData = {}

        if (this.deviceId) {
          console.log('[ArticleCommentConfig] 停止特定设备:', this.deviceId)
          console.log('[ArticleCommentConfig] 使用保存的taskId:', this.currentTaskId)
          console.log('[ArticleCommentConfig] 使用保存的logId:', this.currentLogId)
          stopData = {
            deviceId: this.deviceId,
            taskId: this.currentTaskId || `xiaohongshu_articleComment_${this.deviceId}`,
            logId: this.currentLogId || `xiaohongshu_articleComment_${Date.now()}_${this.deviceId}`
          }
        } else {
          console.log('[ArticleCommentConfig] 停止所有文章评论任务')
        }

        // 立即更新本地状态
        this.isScriptRunning = false
        this.isScriptCompleted = false
        this.saveComponentState()

        // 发送停止请求到服务器
        const response = await this.$http.post(stopUrl, stopData)

        if (response.data.success) {
          this.$message.success('脚本停止成功')
          
          // 清空保存的ID
          this.currentLogId = null
          this.currentTaskId = null
          console.log('[ArticleCommentConfig] 已清空保存的logId和taskId')
          
          // 强制更新组件
          this.$forceUpdate()
          console.log('[ArticleCommentConfig] 组件状态已强制更新')
        } else {
          this.$message.error('停止脚本失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('[ArticleCommentConfig] 停止脚本请求失败:', error)
        this.$message.error('停止脚本请求失败')

        // 即使请求失败，也确保本地状态正确
        this.isScriptRunning = false
        this.isScriptCompleted = false
        this.saveComponentState()
      }
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      console.log('[ArticleCommentConfig] 收到任务开始事件:', data)
      if (data.functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 文章评论任务开始，更新状态')

        this.isScriptRunning = true
        this.isScriptCompleted = false
        this.currentTaskId = data.taskId
        this.currentLogId = data.logId

        this.saveComponentState()
      }
    },

    // 处理任务停止事件
    handleTaskStopped(data) {
      console.log('[ArticleCommentConfig] 收到任务停止事件:', data)
      if (data.functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 文章评论任务停止，更新状态')

        this.isScriptRunning = false
        this.isScriptCompleted = false

        this.saveComponentState()
      }
    },

    // 处理脚本完成事件
    handleScriptCompleted(data) {
      console.log('[ArticleCommentConfig] 收到脚本完成事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 文章评论脚本完成')

        this.isScriptRunning = false
        this.isScriptCompleted = true

        // 1分钟后重置完成状态
        setTimeout(() => {
          this.isScriptCompleted = false
          this.saveComponentState()
        }, 60000)

        this.saveComponentState()
      }
    },

    // 处理任务恢复事件
    async handleTaskRestored(data) {
      console.log('[ArticleCommentConfig] 收到任务恢复事件:', data)
      if (data.functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 恢复文章评论任务状态')

        this.isScriptRunning = data.isRunning || false
        this.isScriptCompleted = data.isCompleted || false
        this.currentTaskId = data.taskId
        this.currentLogId = data.logId

        this.saveComponentState()
      }
    },

    // 恢复组件状态（旧版本，使用localStorage）
    async restoreComponentStateOld() {
      try {
        const stateKey = `articleComment_state_${this.deviceId || 'default'}`
        const savedState = localStorage.getItem(stateKey)
        if (savedState) {
          const state = JSON.parse(savedState)

          this.isScriptRunning = state.isScriptRunning || false
          this.isScriptCompleted = state.isScriptCompleted || false
          this.currentTaskId = state.currentTaskId || null
          this.currentLogId = state.currentLogId || null

          if (state.config) {
            this.config = { ...this.config, ...state.config }
          }

          console.log('[ArticleCommentConfig] 状态已恢复:', state)
        }
      } catch (error) {
        console.error('[ArticleCommentConfig] 恢复状态失败:', error)
      }
    },

    // 手动触发配置更新的调试方法
    debugUpdateConfig() {
      console.log('=== 手动触发配置更新 ===')
      console.log('当前config:', this.config)
      this.updateConfig()
    },



    // 处理任务恢复事件
    handleTaskRestored(data) {
      if (data.functionType === 'articleComment') {
        console.log('[ArticleCommentConfig] 恢复任务状态:', data.state)

        this.isScriptRunning = data.state.isScriptRunning
        this.isScriptCompleted = data.state.isScriptCompleted

        // 恢复配置
        if (data.state.config && Object.keys(data.state.config).length > 0) {
          this.config = { ...this.config, ...data.state.config }
        }

        console.log('[ArticleCommentConfig] 状态已恢复:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          config: this.config
        })
      }
    },

    // 处理强制重置事件
    handleForceReset(data) {
      console.log('[ArticleCommentConfig] 收到强制重置事件:', data)
      if (data.functionType === 'all' || data.functionType === 'articleComment') {
        console.log('[ArticleCommentConfig] 执行强制重置')

        // 重置所有状态
        this.isScriptRunning = false
        this.isScriptCompleted = false
        this.currentTaskId = null
        this.currentLogId = null

        // 保存重置后的状态
        this.saveComponentState()

        console.log('[ArticleCommentConfig] 强制重置完成')
      }
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      console.log('[ArticleCommentConfig] 收到任务开始事件:', data)
      console.log('[ArticleCommentConfig] 当前组件deviceId:', this.deviceId)
      console.log('[ArticleCommentConfig] 事件deviceId:', data.deviceId)
      console.log('[ArticleCommentConfig] 功能类型匹配:', data.functionType === 'articleComment')
      console.log('[ArticleCommentConfig] 设备ID匹配:', !this.deviceId || data.deviceId === this.deviceId)

      if (data.functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 任务开始 (设备ID匹配):', data)
        this.isScriptRunning = true
        this.isScriptCompleted = false

        // 保存真实的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[ArticleCommentConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[ArticleCommentConfig] 保存taskId:', this.currentTaskId)
        }

        // 重置实时状态
        this.resetRealtimeStatus()

        // 强制更新组件
        this.$forceUpdate()
        console.log('[ArticleCommentConfig] 组件状态已强制更新')

        this.saveComponentState()
      } else {
        console.log('[ArticleCommentConfig] 任务开始事件不匹配，忽略')
      }
    },

    // 处理任务停止事件
    handleTaskStopped(data) {
      console.log('[ArticleCommentConfig] 收到任务停止事件:', data)
      console.log('[ArticleCommentConfig] 当前组件deviceId:', this.deviceId)
      console.log('[ArticleCommentConfig] 事件deviceId:', data.deviceId)
      console.log('[ArticleCommentConfig] 功能类型匹配:', data.functionType === 'articleComment')
      console.log('[ArticleCommentConfig] 设备ID匹配:', !this.deviceId || data.deviceId === this.deviceId)

      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = data.functionType === 'articleComment' && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[ArticleCommentConfig] 文章评论任务停止，原因: ${reason}`)
        this.isScriptRunning = false
        this.isScriptCompleted = false

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[ArticleCommentConfig] 已清空logId和taskId')

        // 重置实时状态
        this.resetRealtimeStatus()

        // 强制更新组件
        this.$forceUpdate()
        console.log('[ArticleCommentConfig] 组件状态已强制更新')

        this.saveComponentState()

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('文章评论功能已被批量停止')
        }
      } else {
        console.log('[ArticleCommentConfig] 任务停止事件不匹配，忽略')
      }
    },

    // 处理脚本完成事件
    handleScriptCompleted(data) {
      if (data.functionType === 'articleComment' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[ArticleCommentConfig] 脚本完成 (设备ID匹配):', data)
        this.isScriptRunning = false
        this.isScriptCompleted = true

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[ArticleCommentConfig] 脚本完成，已清空logId和taskId')

        this.saveComponentState()

        // 1分钟后重置完成状态
        setTimeout(() => {
          this.isScriptCompleted = false
          this.saveComponentState()
        }, 60000)
      }
    },

    // 保存组件状态
    async saveComponentState() {
      const { saveComponentState } = await import('@/utils/stateManager')

      const stateData = {
        isScriptRunning: this.isScriptRunning,
        isScriptCompleted: this.isScriptCompleted,
        config: this.config,
        currentLogId: this.currentLogId,
        currentTaskId: this.currentTaskId,
        // 保存实时状态数据
        realtimeData: {
          commentedArticleCount: this.commentedArticleCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        }
      }

      await saveComponentState(this, 'articleComment', stateData)
      console.log('[ArticleCommentConfig] 组件状态已保存，currentTaskId:', this.currentTaskId)
    },

    // 恢复组件状态
    async restoreComponentState() {
      try {
        const { restoreComponentState } = await import('@/utils/stateManager')
        const functionState = await restoreComponentState(this, 'articleComment')

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[ArticleCommentConfig] 恢复组件状态:', functionState)

          this.isScriptRunning = functionState.isScriptRunning || false
          this.isScriptCompleted = functionState.isScriptCompleted || false

          // 恢复logId和taskId
          this.currentLogId = functionState.currentLogId || null
          this.currentTaskId = functionState.currentTaskId || null

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.commentedArticleCount = functionState.realtimeData.commentedArticleCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.searchAttemptCount = functionState.realtimeData.searchAttemptCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[ArticleCommentConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          console.log('[ArticleCommentConfig] 组件状态已恢复:', {
            isScriptRunning: this.isScriptRunning,
            isScriptCompleted: this.isScriptCompleted,
            currentLogId: this.currentLogId,
            currentTaskId: this.currentTaskId,
            config: this.config,
            realtimeData: {
              commentedArticleCount: this.commentedArticleCount,
              processedStepCount: this.processedStepCount,
              searchAttemptCount: this.searchAttemptCount,
              currentStatus: this.currentStatus
            }
          })
        }
      } catch (error) {
        console.error('[ArticleCommentConfig] 恢复组件状态失败:', error)
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [ArticleCommentConfig] 收到实时状态数据:', data)
      console.log('📋 [ArticleCommentConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [ArticleCommentConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [ArticleCommentConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [ArticleCommentConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.commentedArticleCount !== undefined) {
          this.commentedArticleCount = data.commentedArticleCount
          console.log('📊 [ArticleCommentConfig] 更新已评论文章数:', this.commentedArticleCount)
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
          console.log('📊 [ArticleCommentConfig] 更新已处理步骤数:', this.processedStepCount)
        }
        if (data.searchAttemptCount !== undefined) {
          this.searchAttemptCount = data.searchAttemptCount
          console.log('📊 [ArticleCommentConfig] 更新搜索尝试次数:', this.searchAttemptCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [ArticleCommentConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [ArticleCommentConfig] 实时状态已更新:', {
          commentedArticleCount: this.commentedArticleCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [ArticleCommentConfig] 已强制更新视图')

        // 保存更新后的状态
        this.saveComponentState()
      } else {
        console.log('❌ [ArticleCommentConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.commentedArticleCount = 0
      this.processedStepCount = 0
      this.searchAttemptCount = 0
      this.currentStatus = '等待开始'
      console.log('[ArticleCommentConfig] 实时状态已重置')
    },

    // 初始化WebSocket连接
    async initializeSocket() {
      try {
        // 使用WebSocket管理器
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        console.log('🔧 [ArticleCommentConfig] 开始初始化WebSocket连接')

        // 确保连接
        await this.wsManager.init()

        // 注册Web客户端 - 不需要重复注册，WebSocket管理器已经处理了用户认证
        // this.wsManager.emit('web_client_connect', { userId: 'article_comment_config' })

        console.log('✅ [ArticleCommentConfig] WebSocket连接成功')
      } catch (error) {
        console.error('❌ [ArticleCommentConfig] WebSocket连接错误:', error)
      }

      // 监听所有事件进行调试
      this.socket.onAny((eventName, data) => {
        if (eventName.includes('xiaohongshu') || eventName.includes('test')) {
          console.log(`🔍 [ArticleCommentConfig] 收到WebSocket事件: ${eventName}`, data)
        }
      })

      // 监听脚本执行完成事件
      this.socket.on('xiaohongshu_execution_completed', (data) => {
        console.log('[ArticleCommentConfig] 收到WebSocket脚本执行完成事件:', data)
        if (data.deviceId === this.deviceId || !this.deviceId) {
          console.log('[ArticleCommentConfig] 脚本执行完成，更新状态')

          this.isScriptRunning = false
          this.isScriptCompleted = data.status === 'success'

          // 如果执行成功，1分钟后重置完成状态
          if (data.status === 'success') {
            setTimeout(() => {
              this.isScriptCompleted = false
            }, 60000)
          }

          this.saveComponentState()
        }
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [ArticleCommentConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      // 监听测试广播事件
      this.socket.on('test_realtime_broadcast', (data) => {
        console.log('🧪 [ArticleCommentConfig] 收到测试广播:', data)
      })

      console.log('✅ [ArticleCommentConfig] Socket初始化完成')
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[ArticleCommentConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'articleComment',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[ArticleCommentConfig] 设备离线处理完成')
    }
  }
}
</script>

<style scoped>
.article-comment-config {
  padding: 10px 0;
}
</style>
