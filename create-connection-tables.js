/**
 * 创建设备连接码相关数据库表的脚本
 */

const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'autojs_control',
  password: 'root',
  database: 'autojs_control',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

async function createTables() {
  let pool;
  
  try {
    // 创建数据库连接
    pool = mysql.createPool(dbConfig);
    console.log('✅ 数据库连接成功');

    console.log('\n📋 开始创建设备连接码相关表...');

    // 1. 创建设备连接码表
    console.log('1. 创建 device_connection_codes 表...');
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS device_connection_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        code VARCHAR(20) UNIQUE NOT NULL COMMENT '连接码',
        user_id INT NOT NULL COMMENT '用户ID',
        username VARCHAR(50) NOT NULL COMMENT '用户名',
        description VARCHAR(200) DEFAULT '' COMMENT '连接码描述',
        max_devices INT DEFAULT 1 COMMENT '最大可连接设备数',
        used_count INT DEFAULT 0 COMMENT '已使用次数',
        expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_code (code),
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_is_active (is_active)
      )
    `);
    console.log('✅ device_connection_codes 表创建成功');

    // 2. 创建设备连接记录表
    console.log('2. 创建 device_connections 表...');
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS device_connections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        connection_code VARCHAR(20) NOT NULL COMMENT '使用的连接码',
        user_id INT NOT NULL COMMENT '分配的用户ID',
        device_name VARCHAR(100) NOT NULL,
        device_info JSON COMMENT '设备信息',
        connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_device_id (device_id),
        INDEX idx_connection_code (connection_code),
        INDEX idx_user_id (user_id)
      )
    `);
    console.log('✅ device_connections 表创建成功');

    // 3. 检查devices表是否有user_id字段，如果没有则添加
    console.log('3. 检查 devices 表的 user_id 字段...');
    const [columns] = await pool.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'autojs_control' 
        AND TABLE_NAME = 'devices' 
        AND COLUMN_NAME = 'user_id'
    `);
    
    if (columns.length === 0) {
      console.log('添加 user_id 字段到 devices 表...');
      await pool.execute(`
        ALTER TABLE devices 
        ADD COLUMN user_id INT DEFAULT NULL COMMENT '所属用户ID' AFTER last_seen,
        ADD INDEX idx_devices_user_id (user_id)
      `);
      console.log('✅ devices 表 user_id 字段添加成功');
    } else {
      console.log('✅ devices 表已有 user_id 字段');
    }

    // 4. 为admin用户创建一个测试连接码
    console.log('4. 创建测试连接码...');
    const [adminUsers] = await pool.execute('SELECT id, username FROM users WHERE username = "admin"');
    
    if (adminUsers.length > 0) {
      const adminUser = adminUsers[0];
      const testCode = 'TEST1234';
      
      // 检查测试连接码是否已存在
      const [existingCodes] = await pool.execute(
        'SELECT id FROM device_connection_codes WHERE code = ?',
        [testCode]
      );
      
      if (existingCodes.length === 0) {
        await pool.execute(`
          INSERT INTO device_connection_codes 
          (code, user_id, username, description, max_devices, expires_at)
          VALUES (?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
        `, [testCode, adminUser.id, adminUser.username, '测试连接码', 10]);
        
        console.log(`✅ 测试连接码创建成功: ${testCode} (30天有效期)`);
      } else {
        console.log(`✅ 测试连接码已存在: ${testCode}`);
      }
    } else {
      console.log('⚠️ 未找到admin用户，跳过创建测试连接码');
    }

    // 5. 验证表创建结果
    console.log('\n📋 验证表创建结果...');
    
    const tables = ['device_connection_codes', 'device_connections'];
    for (const table of tables) {
      const [rows] = await pool.execute(`SHOW TABLES LIKE '${table}'`);
      if (rows.length > 0) {
        const [count] = await pool.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ 表 ${table} 存在，记录数: ${count[0].count}`);
      } else {
        console.log(`❌ 表 ${table} 不存在`);
      }
    }

    // 6. 显示连接码列表
    console.log('\n📋 当前连接码列表...');
    const [codes] = await pool.execute(`
      SELECT code, username, description, max_devices, used_count, 
             expires_at, is_active, created_at
      FROM device_connection_codes 
      ORDER BY created_at DESC
    `);
    
    if (codes.length > 0) {
      console.log('连接码列表:');
      codes.forEach(code => {
        console.log(`- ${code.code} | 用户: ${code.username} | 描述: ${code.description} | 使用: ${code.used_count}/${code.max_devices} | 状态: ${code.is_active ? '激活' : '禁用'}`);
      });
    } else {
      console.log('暂无连接码');
    }

    console.log('\n✅ 数据库表创建完成！');
    console.log('\n📋 使用说明:');
    console.log('1. 重启服务器以加载连接码功能');
    console.log('2. 登录Web界面，进入设备管理页面');
    console.log('3. 点击"连接码管理"创建新的连接码');
    console.log('4. 在设备脚本中输入连接码进行连接');
    console.log('5. 测试连接码: TEST1234 (30天有效期)');

  } catch (error) {
    console.error('❌ 创建表失败:', error);
  } finally {
    if (pool) {
      await pool.end();
      console.log('\n📋 数据库连接已关闭');
    }
  }
}

// 运行脚本
console.log('🚀 开始创建设备连接码数据库表...');
createTables().then(() => {
  console.log('\n✅ 脚本执行完成');
}).catch(error => {
  console.error('\n❌ 脚本执行异常:', error);
});
