/**
 * 服务器脚本管理模块 - 完整拆分版本
 * 包含所有脚本管理相关的API和功能
 * 对应原始文件第829-1120行和其他脚本相关功能的完整内容，包含以下API：
 * - GET /api/script/list - 获取脚本列表
 * - POST /api/script/upload - 上传脚本
 * - GET /api/script/:id - 获取单个脚本
 * - DELETE /api/script/:id - 删除脚本
 * - POST /api/script/execute - 执行脚本
 * - GET /api/script/logs/list - 获取脚本执行日志列表
 * - GET /script-config-enhanced.html - 增强版脚本配置页面路由
 * 以及脚本转换、管理等相关功能
 */

const fs = require('fs');
const path = require('path');

// 脚本管理模块设置函数
async function setupServerScript(app, io, coreData, authData) {
  console.log('🔧 设置脚本管理模块...');

  // 引入用户隔离中间件和工具
  const { userIsolationMiddleware } = require('../middleware/userIsolation');
  const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
  const PermissionValidator = require('../utils/PermissionValidator');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    xiaohongshuLogService,
    xianyuLogService,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 创建数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 脚本存储
  const scriptsStorage = new Map();

  // 初始化一些示例脚本
  scriptsStorage.set('1', {
    id: '1',
    name: '测试脚本',
    content: 'console.log("Hello World");',
    description: '简单的测试脚本',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  scriptsStorage.set('2', {
    id: '2',
    name: '设备信息获取',
    content: `
// 获取设备信息
var deviceInfo = {
  brand: device.brand,
  model: device.model,
  androidVersion: device.release,
  screenWidth: device.width,
  screenHeight: device.height
};
console.log("设备信息:", JSON.stringify(deviceInfo, null, 2));
    `,
    description: '获取并显示设备基本信息',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  scriptsStorage.set('3', {
    id: '3',
    name: '屏幕截图',
    content: `
// 截取屏幕截图
var img = captureScreen();
if (img) {
  var path = "/sdcard/screenshot_" + new Date().getTime() + ".png";
  img.saveTo(path);
  console.log("截图已保存到:", path);
  img.recycle();
} else {
  console.log("截图失败");
}
    `,
    description: '截取设备屏幕截图并保存',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  scriptsStorage.set('4', {
    id: '4',
    name: '应用启动测试',
    content: `
// 启动应用测试
console.log("开始应用启动测试");

// 回到桌面
home();
sleep(1000);

// 尝试启动小红书
console.log("尝试启动小红书应用");
var xiaohongshuStarted = app.launchApp("小红书");
if (xiaohongshuStarted) {
  console.log("小红书启动成功");
  sleep(3000);
  
  // 检查应用是否真的启动了
  var currentApp = currentPackage();
  console.log("当前应用包名:", currentApp);
  
  if (currentApp && currentApp.includes("xiaohongshu")) {
    console.log("小红书应用确认启动成功");
  } else {
    console.log("小红书应用启动失败或未正确启动");
  }
} else {
  console.log("小红书启动失败");
}

// 回到桌面
home();
sleep(1000);

console.log("应用启动测试完成");
    `,
    description: '测试应用启动功能',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  // 获取脚本列表API (原始文件第830行) - 已添加用户隔离
  app.get('/api/script/list', authenticateToken, userIsolationMiddleware, (req, res) => {
    const userId = req.currentUserId;

    // 只返回当前用户的脚本
    const scriptList = Array.from(scriptsStorage.values())
      .filter(script => script.userId === userId)
      .map(script => ({
        id: script.id,
        name: script.name,
        description: script.description,
        createdAt: script.createdAt,
        updatedAt: script.updatedAt,
        size: script.content ? script.content.length : 0
      }));

    res.json({
      success: true,
      data: scriptList
    });
  });

  // 上传脚本API (原始文件第846行) - 已添加用户隔离
  app.post('/api/script/upload', authenticateToken, userIsolationMiddleware, (req, res) => {
    const { name, content, description } = req.body;
    const userId = req.currentUserId;

    if (!name || !content) {
      return res.status(400).json({
        success: false,
        message: '脚本名称和内容不能为空'
      });
    }

    const scriptId = Date.now().toString();
    const script = {
      id: scriptId,
      name,
      content,
      description: description || '',
      userId: userId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    scriptsStorage.set(scriptId, script);

    console.log(`脚本已上传: ${name} (ID: ${scriptId})`);

    res.json({
      success: true,
      message: '脚本上传成功',
      data: {
        id: scriptId,
        name,
        description
      }
    });
  });

  // 获取单个脚本API (原始文件第882行) - 已添加用户隔离
  app.get('/api/script/:id', authenticateToken, userIsolationMiddleware, (req, res) => {
    const { id } = req.params;
    const userId = req.currentUserId;
    const script = scriptsStorage.get(id);

    if (!script) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    // 验证脚本所属权
    if (script.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权访问此脚本'
      });
    }

    res.json({
      success: true,
      data: script
    });
  });

  // 删除脚本API (原始文件第900行) - 已添加用户隔离
  app.delete('/api/script/:id', authenticateToken, userIsolationMiddleware, (req, res) => {
    const { id } = req.params;
    const userId = req.currentUserId;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const script = scriptsStorage.get(id);

    // 验证脚本所属权
    if (script.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: '无权删除此脚本'
      });
    }

    scriptsStorage.delete(id);

    console.log(`[用户${userId}] 脚本已删除: ${script.name} (ID: ${id})`);

    res.json({
      success: true,
      message: '脚本删除成功'
    });
  });

  // 执行脚本API (原始文件第921行) - 已添加用户隔离
  app.post('/api/script/execute', authenticateToken, userIsolationMiddleware, async (req, res) => {
    const { deviceIds, script, scriptId, uiConfig, scriptName } = req.body;
    const userId = req.currentUserId;

    // 验证输入参数
    if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '设备ID列表不能为空',
        data: {
          successCount: 0,
          failCount: 0,
          totalCount: 0,
          results: [],
          scriptName: scriptName || '未命名脚本',
          timestamp: new Date()
        }
      });
    }

    // 验证所有设备的所属权
    for (const deviceId of deviceIds) {
      const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: `无权操作设备 ${deviceId}`,
          data: {
            successCount: 0,
            failCount: deviceIds.length,
            totalCount: deviceIds.length,
            results: [],
            scriptName: scriptName || '未命名脚本',
            timestamp: new Date()
          }
        });
      }
    }

    let scriptContent = script;

    // 如果传入的是scriptId，需要查找脚本内容并验证所属权
    if (scriptId && !script) {
      const foundScript = scriptsStorage.get(scriptId);
      if (!foundScript) {
        return res.status(404).json({
          success: false,
          message: '脚本不存在',
          data: {
            successCount: 0,
            failCount: 0,
            totalCount: deviceIds.length,
            results: [],
            scriptName: scriptName || '未命名脚本',
            timestamp: new Date()
          }
        });
      }

      // 验证脚本所属权
      if (foundScript.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权执行此脚本',
          data: {
            successCount: 0,
            failCount: 0,
            totalCount: deviceIds.length,
            results: [],
            scriptName: scriptName || '未命名脚本',
            timestamp: new Date()
          }
        });
      }

      scriptContent = foundScript.content;
    }

    if (!scriptContent) {
      return res.status(400).json({
        success: false,
        message: '脚本内容不能为空',
        data: {
          successCount: 0,
          failCount: 0,
          totalCount: deviceIds.length,
          results: [],
          scriptName: scriptName || '未命名脚本',
          timestamp: new Date()
        }
      });
    }

    // 注入UI配置到脚本
    if (uiConfig && Object.keys(uiConfig).length > 0) {
      const configInjection = `
// === 配置数据注入 ===
// 由Web端自动注入的配置数据
var __config = ${JSON.stringify(uiConfig, null, 2)};

console.log("=== 配置数据已注入 ===");
console.log("配置内容:", JSON.stringify(__config, null, 2));

// === 原始脚本开始 ===
`;
      scriptContent = configInjection + scriptContent;
    }

    let successCount = 0;
    let failCount = 0;
    const executionResults = [];

    deviceIds.forEach(deviceId => {
      const device = Array.from(devices.values()).find(d => d.deviceId === deviceId);
      if (device) {
        const logId = Date.now() + Math.random(); // 确保唯一性

        // 创建执行日志
        const log = {
          id: logId,
          deviceId: deviceId,
          deviceName: device.deviceName,
          script: scriptContent,
          scriptName: scriptName || '未命名脚本',
          status: 'pending',
          created_at: new Date(),
          started_at: null,
          completed_at: null,
          result: null
        };

        logs.push(log);

        // 发送脚本到设备
        const deviceSocket = io.sockets.sockets.get(device.socketId);
        if (deviceSocket) {
          deviceSocket.emit('execute_script', {
            logId: logId,
            script: scriptContent,
            scriptName: scriptName || '未命名脚本',
            timestamp: Date.now()
          });

          log.status = 'running';
          log.started_at = new Date();

          successCount++;
          executionResults.push({
            deviceId: deviceId,
            deviceName: device.deviceName,
            logId: logId,
            status: 'sent',
            message: '脚本已发送到设备'
          });

          console.log(`脚本已发送到设备: ${device.deviceName} (${deviceId})`);
        } else {
          log.status = 'failed';
          log.result = '设备连接已断开';
          log.completed_at = new Date();

          failCount++;
          executionResults.push({
            deviceId: deviceId,
            deviceName: device.deviceName,
            logId: logId,
            status: 'failed',
            message: '设备连接已断开'
          });

          console.log(`设备连接已断开: ${device.deviceName} (${deviceId})`);
        }
      } else {
        failCount++;
        executionResults.push({
          deviceId: deviceId,
          deviceName: '未知设备',
          logId: null,
          status: 'not_found',
          message: '设备不存在或已离线'
        });

        console.log(`设备不存在: ${deviceId}`);
      }
    });

    console.log(`脚本执行统计: 成功=${successCount}, 失败=${failCount}, 总计=${deviceIds.length}`);

    res.json({
      success: true,
      message: `脚本已发送，成功: ${successCount}个，失败: ${failCount}个`,
      data: {
        successCount,
        failCount,
        totalCount: deviceIds.length,
        results: executionResults,
        scriptName: scriptName || '未命名脚本',
        timestamp: new Date()
      }
    });
  });

  // 获取脚本执行日志列表API (原始文件第1099行) - 已添加用户隔离
  app.get('/api/script/logs/list', authenticateToken, userIsolationMiddleware, (req, res) => {
    const { page = 1, limit = 20 } = req.query;
    const userId = req.currentUserId;
    const start = (page - 1) * limit;
    const end = start + parseInt(limit);

    // 只返回当前用户的日志
    const userLogs = logs.filter(log => log.userId === userId);
    const paginatedLogs = userLogs.slice(start, end);

    res.json({
      success: true,
      data: {
        logs: paginatedLogs,
        total: userLogs.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  });

  // 脚本模板管理功能
  const scriptTemplates = new Map();

  // 初始化脚本模板
  scriptTemplates.set('basic_ui', {
    id: 'basic_ui',
    name: '基础UI脚本模板',
    category: 'ui',
    content: `
// 基础UI脚本模板
"ui";

ui.layout(
    <vertical padding="16">
        <text text="脚本配置" textSize="18sp" textColor="#333" gravity="center" margin="0 0 16 0"/>

        <card cardCornerRadius="8dp" cardElevation="4dp" margin="0 0 16 0">
            <vertical padding="16">
                <text text="基本设置" textSize="16sp" textColor="#666" margin="0 0 8 0"/>
                <input id="input_text" hint="请输入文本" textSize="14sp"/>
                <checkbox id="checkbox_option" text="启用选项" margin="8 0 0 0"/>
            </vertical>
        </card>

        <linear>
            <button id="btn_start" text="开始执行" style="Widget.AppCompat.Button.Colored" layout_weight="1" margin="0 8 0 0"/>
            <button id="btn_stop" text="停止" layout_weight="1" margin="8 0 0 0"/>
        </linear>
    </vertical>
);

// 获取配置
var config = {
    text: ui.input_text.text(),
    option: ui.checkbox_option.checked
};

// 开始按钮事件
ui.btn_start.click(() => {
    console.log("开始执行，配置:", JSON.stringify(config));
    // 在这里添加脚本逻辑
});

// 停止按钮事件
ui.btn_stop.click(() => {
    console.log("停止执行");
    exit();
});
    `,
    description: '包含基础UI元素的脚本模板',
    createdAt: new Date()
  });

  scriptTemplates.set('automation_base', {
    id: 'automation_base',
    name: '自动化脚本基础模板',
    category: 'automation',
    content: `
// 自动化脚本基础模板

// 请求屏幕捕获权限
if (!requestScreenCapture()) {
    toast("请授予屏幕捕获权限");
    exit();
}

// 基础配置
var config = {
    maxRetries: 3,
    waitTimeout: 5000,
    debugMode: true
};

// 日志函数
function log(message) {
    if (config.debugMode) {
        console.log("[" + new Date().toLocaleTimeString() + "] " + message);
    }
}

// 等待元素出现
function waitForElement(selector, timeout) {
    timeout = timeout || config.waitTimeout;
    var startTime = Date.now();

    while (Date.now() - startTime < timeout) {
        var element = selector();
        if (element) {
            return element;
        }
        sleep(100);
    }

    return null;
}

// 安全点击
function safeClick(element, description) {
    if (element && element.clickable()) {
        log("点击: " + description);
        element.click();
        return true;
    } else {
        log("无法点击: " + description);
        return false;
    }
}

// 主要逻辑
function main() {
    log("开始执行自动化脚本");

    try {
        // 在这里添加具体的自动化逻辑

        log("脚本执行完成");
    } catch (error) {
        log("脚本执行出错: " + error.message);
        throw error;
    }
}

// 执行主函数
main();
    `,
    description: '包含常用自动化功能的脚本模板',
    createdAt: new Date()
  });

  scriptTemplates.set('app_launcher', {
    id: 'app_launcher',
    name: '应用启动器模板',
    category: 'utility',
    content: `
// 应用启动器模板

// 应用配置
var apps = {
    "xiaohongshu": {
        name: "小红书",
        package: "com.xingin.xhs",
        activity: "com.xingin.xhs.index.v2.IndexActivityV2"
    },
    "xianyu": {
        name: "闲鱼",
        package: "com.taobao.idlefish",
        activity: "com.taobao.idlefish.maincontainer.activity.MainActivity"
    }
};

// 启动应用函数
function launchApp(appKey) {
    var appInfo = apps[appKey];
    if (!appInfo) {
        console.log("不支持的应用: " + appKey);
        return false;
    }

    console.log("启动应用: " + appInfo.name);

    // 方法1: 使用应用名称启动
    var success = app.launchApp(appInfo.name);
    if (success) {
        console.log("应用启动成功: " + appInfo.name);
        sleep(3000);
        return true;
    }

    // 方法2: 使用包名启动
    console.log("尝试使用包名启动: " + appInfo.package);
    success = app.launchPackage(appInfo.package);
    if (success) {
        console.log("应用启动成功: " + appInfo.name);
        sleep(3000);
        return true;
    }

    // 方法3: 使用Intent启动
    console.log("尝试使用Intent启动");
    try {
        app.startActivity({
            packageName: appInfo.package,
            className: appInfo.activity
        });
        console.log("应用启动成功: " + appInfo.name);
        sleep(3000);
        return true;
    } catch (error) {
        console.log("Intent启动失败: " + error.message);
    }

    console.log("应用启动失败: " + appInfo.name);
    return false;
}

// 检查应用是否已启动
function isAppRunning(appKey) {
    var appInfo = apps[appKey];
    if (!appInfo) {
        return false;
    }

    var currentApp = currentPackage();
    return currentApp && currentApp.includes(appInfo.package);
}

// 关闭应用
function closeApp(appKey) {
    var appInfo = apps[appKey];
    if (!appInfo) {
        console.log("不支持的应用: " + appKey);
        return false;
    }

    console.log("关闭应用: " + appInfo.name);

    // 强制停止应用
    shell("am force-stop " + appInfo.package, true);
    sleep(1000);

    // 验证应用是否已关闭
    if (!isAppRunning(appKey)) {
        console.log("应用已关闭: " + appInfo.name);
        return true;
    } else {
        console.log("应用关闭失败: " + appInfo.name);
        return false;
    }
}

// 示例使用
console.log("应用启动器模板加载完成");
console.log("可用应用:", Object.keys(apps).join(", "));

// 取消注释以下行来测试启动小红书
// launchApp("xiaohongshu");
    `,
    description: '用于启动和管理应用的脚本模板',
    createdAt: new Date()
  });

  // 获取脚本模板列表API
  app.get('/api/script/templates', authenticateToken, (req, res) => {
    const { category } = req.query;

    let templates = Array.from(scriptTemplates.values());

    if (category) {
      templates = templates.filter(t => t.category === category);
    }

    const templateList = templates.map(template => ({
      id: template.id,
      name: template.name,
      category: template.category,
      description: template.description,
      createdAt: template.createdAt,
      size: template.content ? template.content.length : 0
    }));

    res.json({
      success: true,
      data: templateList
    });
  });

  // 获取脚本模板内容API
  app.get('/api/script/templates/:id', authenticateToken, (req, res) => {
    const { id } = req.params;
    const template = scriptTemplates.get(id);

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '脚本模板不存在'
      });
    }

    res.json({
      success: true,
      data: template
    });
  });

  // 脚本版本控制功能
  const scriptVersions = new Map(); // 存储脚本版本历史

  function saveScriptVersion(scriptId, content, description = '') {
    if (!scriptVersions.has(scriptId)) {
      scriptVersions.set(scriptId, []);
    }

    const version = {
      id: Date.now() + Math.random(),
      version: scriptVersions.get(scriptId).length + 1,
      content,
      description,
      createdAt: new Date(),
      size: content.length
    };

    scriptVersions.get(scriptId).push(version);

    // 保持最近20个版本
    const versions = scriptVersions.get(scriptId);
    if (versions.length > 20) {
      versions.shift();
    }

    console.log(`脚本版本已保存: ${scriptId} v${version.version}`);
    return version;
  }

  // 更新脚本API（支持版本控制）
  app.put('/api/script/:id', authenticateToken, (req, res) => {
    const { id } = req.params;
    const { name, content, description, versionDescription } = req.body;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const script = scriptsStorage.get(id);

    // 保存当前版本到版本历史
    if (script.content !== content) {
      saveScriptVersion(id, script.content, versionDescription || '自动保存的版本');
    }

    // 更新脚本
    script.name = name || script.name;
    script.content = content || script.content;
    script.description = description || script.description;
    script.updatedAt = new Date();

    scriptsStorage.set(id, script);

    console.log(`脚本已更新: ${script.name} (ID: ${id})`);

    res.json({
      success: true,
      message: '脚本更新成功',
      data: script
    });
  });

  // 获取脚本版本历史API
  app.get('/api/script/:id/versions', authenticateToken, (req, res) => {
    const { id } = req.params;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const versions = scriptVersions.get(id) || [];

    res.json({
      success: true,
      data: {
        scriptId: id,
        versions: versions.map(v => ({
          id: v.id,
          version: v.version,
          description: v.description,
          createdAt: v.createdAt,
          size: v.size
        }))
      }
    });
  });

  // 获取特定版本的脚本内容API
  app.get('/api/script/:id/versions/:versionId', authenticateToken, (req, res) => {
    const { id, versionId } = req.params;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const versions = scriptVersions.get(id) || [];
    const version = versions.find(v => v.id == versionId);

    if (!version) {
      return res.status(404).json({
        success: false,
        message: '脚本版本不存在'
      });
    }

    res.json({
      success: true,
      data: version
    });
  });

  // 恢复脚本版本API
  app.post('/api/script/:id/restore/:versionId', authenticateToken, (req, res) => {
    const { id, versionId } = req.params;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const versions = scriptVersions.get(id) || [];
    const version = versions.find(v => v.id == versionId);

    if (!version) {
      return res.status(404).json({
        success: false,
        message: '脚本版本不存在'
      });
    }

    const script = scriptsStorage.get(id);

    // 保存当前版本
    saveScriptVersion(id, script.content, '恢复前的版本');

    // 恢复到指定版本
    script.content = version.content;
    script.updatedAt = new Date();

    scriptsStorage.set(id, script);

    console.log(`脚本已恢复到版本 ${version.version}: ${script.name} (ID: ${id})`);

    res.json({
      success: true,
      message: `脚本已恢复到版本 ${version.version}`,
      data: script
    });
  });

  // 脚本分析功能
  function analyzeScript(content) {
    const analysis = {
      lineCount: 0,
      characterCount: content.length,
      hasUI: false,
      hasScreenCapture: false,
      hasFileOperations: false,
      hasNetworkOperations: false,
      hasShellCommands: false,
      functions: [],
      variables: [],
      imports: [],
      warnings: [],
      suggestions: []
    };

    const lines = content.split('\n');
    analysis.lineCount = lines.length;

    // 分析脚本内容
    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      // 检查UI相关
      if (trimmedLine.includes('"ui"') || trimmedLine.includes("'ui'") || trimmedLine.includes('ui.layout')) {
        analysis.hasUI = true;
      }

      // 检查屏幕捕获
      if (trimmedLine.includes('requestScreenCapture') || trimmedLine.includes('captureScreen')) {
        analysis.hasScreenCapture = true;
      }

      // 检查文件操作
      if (trimmedLine.includes('files.') || trimmedLine.includes('open(') || trimmedLine.includes('readFile') || trimmedLine.includes('writeFile')) {
        analysis.hasFileOperations = true;
      }

      // 检查网络操作
      if (trimmedLine.includes('http.') || trimmedLine.includes('fetch(') || trimmedLine.includes('request(')) {
        analysis.hasNetworkOperations = true;
      }

      // 检查Shell命令
      if (trimmedLine.includes('shell(') || trimmedLine.includes('exec(')) {
        analysis.hasShellCommands = true;
      }

      // 提取函数定义
      const functionMatch = trimmedLine.match(/function\s+(\w+)\s*\(/);
      if (functionMatch) {
        analysis.functions.push({
          name: functionMatch[1],
          line: index + 1
        });
      }

      // 提取变量定义
      const varMatch = trimmedLine.match(/var\s+(\w+)/);
      if (varMatch) {
        analysis.variables.push({
          name: varMatch[1],
          line: index + 1
        });
      }

      // 检查常见问题
      if (trimmedLine.includes('sleep(') && !trimmedLine.includes('//')) {
        const sleepMatch = trimmedLine.match(/sleep\((\d+)\)/);
        if (sleepMatch && parseInt(sleepMatch[1]) > 5000) {
          analysis.warnings.push({
            type: 'performance',
            message: `第${index + 1}行: 睡眠时间过长 (${sleepMatch[1]}ms)`,
            line: index + 1
          });
        }
      }

      // 检查未处理的异常
      if (trimmedLine.includes('try') && !content.includes('catch')) {
        analysis.warnings.push({
          type: 'error_handling',
          message: `第${index + 1}行: try语句缺少对应的catch处理`,
          line: index + 1
        });
      }
    });

    // 生成建议
    if (!analysis.hasScreenCapture && (content.includes('click(') || content.includes('text('))) {
      analysis.suggestions.push({
        type: 'permission',
        message: '脚本包含UI操作但未请求屏幕捕获权限，建议添加 requestScreenCapture()'
      });
    }

    if (analysis.functions.length === 0 && analysis.lineCount > 50) {
      analysis.suggestions.push({
        type: 'structure',
        message: '脚本较长但没有函数定义，建议将代码模块化'
      });
    }

    if (analysis.hasShellCommands) {
      analysis.suggestions.push({
        type: 'security',
        message: '脚本包含Shell命令，请确保命令安全性'
      });
    }

    return analysis;
  }

  // 脚本分析API
  app.post('/api/script/:id/analyze', authenticateToken, (req, res) => {
    const { id } = req.params;
    const script = scriptsStorage.get(id);

    if (!script) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    try {
      const analysis = analyzeScript(script.content);

      res.json({
        success: true,
        data: {
          scriptId: id,
          scriptName: script.name,
          analysis: analysis,
          analyzedAt: new Date()
        }
      });
    } catch (error) {
      console.error('脚本分析失败:', error);
      res.status(500).json({
        success: false,
        message: '脚本分析失败: ' + error.message
      });
    }
  });

  // 脚本优化建议API
  app.post('/api/script/:id/optimize', authenticateToken, (req, res) => {
    const { id } = req.params;
    const script = scriptsStorage.get(id);

    if (!script) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    try {
      const analysis = analyzeScript(script.content);

      const optimizations = [];

      // 性能优化建议
      if (analysis.warnings.some(w => w.type === 'performance')) {
        optimizations.push({
          type: 'performance',
          title: '性能优化',
          description: '减少不必要的睡眠时间，使用更精确的等待条件',
          priority: 'medium'
        });
      }

      // 错误处理优化
      if (analysis.warnings.some(w => w.type === 'error_handling')) {
        optimizations.push({
          type: 'error_handling',
          title: '错误处理',
          description: '添加适当的异常处理机制，提高脚本稳定性',
          priority: 'high'
        });
      }

      // 代码结构优化
      if (analysis.lineCount > 100 && analysis.functions.length < 3) {
        optimizations.push({
          type: 'structure',
          title: '代码结构',
          description: '将长脚本拆分为多个函数，提高代码可读性和维护性',
          priority: 'medium'
        });
      }

      // 权限优化
      if (analysis.suggestions.some(s => s.type === 'permission')) {
        optimizations.push({
          type: 'permission',
          title: '权限管理',
          description: '添加必要的权限请求，确保脚本正常运行',
          priority: 'high'
        });
      }

      res.json({
        success: true,
        data: {
          scriptId: id,
          scriptName: script.name,
          optimizations: optimizations,
          analysis: analysis,
          optimizedAt: new Date()
        }
      });
    } catch (error) {
      console.error('脚本优化分析失败:', error);
      res.status(500).json({
        success: false,
        message: '脚本优化分析失败: ' + error.message
      });
    }
  });

  // 脚本执行统计功能
  const scriptExecutionStats = new Map(); // 存储脚本执行统计

  function recordScriptExecution(scriptId, deviceId, status, duration = 0) {
    if (!scriptExecutionStats.has(scriptId)) {
      scriptExecutionStats.set(scriptId, {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageDuration: 0,
        lastExecuted: null,
        deviceStats: new Map(),
        executionHistory: []
      });
    }

    const stats = scriptExecutionStats.get(scriptId);

    // 更新总体统计
    stats.totalExecutions++;
    if (status === 'success') {
      stats.successfulExecutions++;
    } else {
      stats.failedExecutions++;
    }

    // 更新平均执行时间
    if (duration > 0) {
      const totalDuration = stats.averageDuration * (stats.totalExecutions - 1) + duration;
      stats.averageDuration = totalDuration / stats.totalExecutions;
    }

    stats.lastExecuted = new Date();

    // 更新设备统计
    if (!stats.deviceStats.has(deviceId)) {
      stats.deviceStats.set(deviceId, {
        executions: 0,
        successes: 0,
        failures: 0
      });
    }

    const deviceStats = stats.deviceStats.get(deviceId);
    deviceStats.executions++;
    if (status === 'success') {
      deviceStats.successes++;
    } else {
      deviceStats.failures++;
    }

    // 添加到执行历史
    stats.executionHistory.push({
      deviceId,
      status,
      duration,
      timestamp: new Date()
    });

    // 保持最近100条执行记录
    if (stats.executionHistory.length > 100) {
      stats.executionHistory.shift();
    }
  }

  // 获取脚本执行统计API
  app.get('/api/script/:id/stats', authenticateToken, (req, res) => {
    const { id } = req.params;

    if (!scriptsStorage.has(id)) {
      return res.status(404).json({
        success: false,
        message: '脚本不存在'
      });
    }

    const stats = scriptExecutionStats.get(id);

    if (!stats) {
      return res.json({
        success: true,
        data: {
          scriptId: id,
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          successRate: 0,
          averageDuration: 0,
          lastExecuted: null,
          deviceStats: {},
          executionHistory: []
        }
      });
    }

    // 转换设备统计为普通对象
    const deviceStatsObj = {};
    for (const [deviceId, deviceStat] of stats.deviceStats) {
      deviceStatsObj[deviceId] = deviceStat;
    }

    res.json({
      success: true,
      data: {
        scriptId: id,
        totalExecutions: stats.totalExecutions,
        successfulExecutions: stats.successfulExecutions,
        failedExecutions: stats.failedExecutions,
        successRate: stats.totalExecutions > 0 ?
          Math.round((stats.successfulExecutions / stats.totalExecutions) * 100) : 0,
        averageDuration: Math.round(stats.averageDuration),
        lastExecuted: stats.lastExecuted,
        deviceStats: deviceStatsObj,
        executionHistory: stats.executionHistory.slice(-20) // 返回最近20条记录
      }
    });
  });

  // 脚本性能监控API
  app.get('/api/script/performance', authenticateToken, (req, res) => {
    try {
      const performanceData = [];

      for (const [scriptId, stats] of scriptExecutionStats) {
        const script = scriptsStorage.get(scriptId);
        if (script) {
          performanceData.push({
            scriptId,
            scriptName: script.name,
            totalExecutions: stats.totalExecutions,
            successRate: stats.totalExecutions > 0 ?
              Math.round((stats.successfulExecutions / stats.totalExecutions) * 100) : 0,
            averageDuration: Math.round(stats.averageDuration),
            lastExecuted: stats.lastExecuted,
            deviceCount: stats.deviceStats.size
          });
        }
      }

      // 按执行次数排序
      performanceData.sort((a, b) => b.totalExecutions - a.totalExecutions);

      res.json({
        success: true,
        data: {
          scripts: performanceData,
          summary: {
            totalScripts: scriptsStorage.size,
            activeScripts: performanceData.filter(s => s.totalExecutions > 0).length,
            totalExecutions: performanceData.reduce((sum, s) => sum + s.totalExecutions, 0),
            averageSuccessRate: performanceData.length > 0 ?
              Math.round(performanceData.reduce((sum, s) => sum + s.successRate, 0) / performanceData.length) : 0
          }
        }
      });
    } catch (error) {
      console.error('获取脚本性能数据失败:', error);
      res.status(500).json({
        success: false,
        message: '获取性能数据失败: ' + error.message
      });
    }
  });

  // 增强版脚本配置页面路由 (原始文件第10818行)
  app.get('/script-config-enhanced.html', (req, res) => {
    const testPagePath = path.join(__dirname, '../script-config-enhanced.html');

    if (fs.existsSync(testPagePath)) {
      res.sendFile(testPagePath);
    } else {
      res.status(404).send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>增强版脚本配置页面</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>增强版脚本配置页面</h1>
            <p>页面文件不存在: ${testPagePath}</p>
            <p>请创建 script-config-enhanced.html 文件</p>
        </body>
        </html>
      `);
    }
  });

  console.log('✅ 脚本管理模块设置完成');

  // 返回脚本管理相关函数
  return {
    scriptsStorage,
    scriptTemplates,
    scriptVersions,
    scriptExecutionStats,
    analyzeScript,
    recordScriptExecution,
    saveScriptVersion
  };
}

module.exports = { setupServerScript };
