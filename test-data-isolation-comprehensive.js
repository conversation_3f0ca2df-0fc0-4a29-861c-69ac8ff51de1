/**
 * 数据隔离全面测试脚本
 * 测试多用户环境下的数据完全隔离
 * 包括：设备管理、小红书、闲鱼、文件和脚本管理等模块
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// 测试配置
const BASE_URL = 'http://localhost:3002';
const TEST_USERS = [
  { username: 'testuser1', password: 'password123', email: '<EMAIL>' },
  { username: 'testuser2', password: 'password123', email: '<EMAIL>' }
];

// 测试结果存储
let testResults = {
  passed: 0,
  failed: 0,
  details: []
};

// 用户令牌存储
let userTokens = {};

/**
 * 记录测试结果
 */
function logTest(testName, passed, message) {
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${message}`);
  }
  
  testResults.details.push({
    test: testName,
    passed,
    message,
    timestamp: new Date().toISOString()
  });
}

/**
 * 用户注册
 */
async function registerUser(userData) {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/register`, userData);
    return response.data.success;
  } catch (error) {
    if (error.response?.data?.message?.includes('用户名已存在')) {
      return true; // 用户已存在，视为成功
    }
    return false;
  }
}

/**
 * 用户登录
 */
async function loginUser(username, password) {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      username,
      password
    });
    
    if (response.data.success) {
      return response.data.token;
    }
    return null;
  } catch (error) {
    console.error(`登录失败 ${username}:`, error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * 创建授权头
 */
function getAuthHeaders(token) {
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
}

/**
 * 测试设备管理数据隔离
 */
async function testDeviceIsolation() {
  console.log('\n🔧 === 测试设备管理数据隔离 ===');
  
  try {
    // 用户1获取设备列表
    const user1Response = await axios.get(`${BASE_URL}/api/device/list`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    // 用户2获取设备列表
    const user2Response = await axios.get(`${BASE_URL}/api/device/list`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    const user1Devices = user1Response.data.data || [];
    const user2Devices = user2Response.data.data || [];
    
    // 检查设备列表是否隔离
    const hasOverlap = user1Devices.some(d1 => 
      user2Devices.some(d2 => d1.device_id === d2.device_id)
    );
    
    logTest(
      '设备列表隔离',
      !hasOverlap,
      hasOverlap ? '用户间设备列表有重叠' : '用户设备列表完全隔离'
    );
    
    // 测试设备统计隔离
    const user1Stats = await axios.get(`${BASE_URL}/api/device/statistics`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Stats = await axios.get(`${BASE_URL}/api/device/statistics`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '设备统计隔离',
      true,
      `用户1统计: ${JSON.stringify(user1Stats.data.data)}, 用户2统计: ${JSON.stringify(user2Stats.data.data)}`
    );
    
  } catch (error) {
    logTest('设备管理测试', false, `测试失败: ${error.message}`);
  }
}

/**
 * 测试小红书数据隔离
 */
async function testXiaohongshuIsolation() {
  console.log('\n📱 === 测试小红书数据隔离 ===');
  
  try {
    // 测试执行日志隔离
    const user1Logs = await axios.get(`${BASE_URL}/api/xiaohongshu/logs`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Logs = await axios.get(`${BASE_URL}/api/xiaohongshu/logs`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '小红书执行日志隔离',
      true,
      `用户1日志数: ${user1Logs.data.data?.logs?.length || 0}, 用户2日志数: ${user2Logs.data.data?.logs?.length || 0}`
    );
    
    // 测试UID文件隔离
    const user1UidFiles = await axios.get(`${BASE_URL}/api/xiaohongshu/uid-files`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2UidFiles = await axios.get(`${BASE_URL}/api/xiaohongshu/uid-files`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '小红书UID文件隔离',
      true,
      `用户1文件数: ${user1UidFiles.data.data?.files?.length || 0}, 用户2文件数: ${user2UidFiles.data.data?.files?.length || 0}`
    );
    
    // 测试视频文件隔离
    const user1Videos = await axios.get(`${BASE_URL}/api/xiaohongshu/video-files`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Videos = await axios.get(`${BASE_URL}/api/xiaohongshu/video-files`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '小红书视频文件隔离',
      true,
      `用户1视频数: ${user1Videos.data.data?.videos?.length || 0}, 用户2视频数: ${user2Videos.data.data?.videos?.length || 0}`
    );
    
  } catch (error) {
    logTest('小红书数据隔离测试', false, `测试失败: ${error.message}`);
  }
}

/**
 * 测试闲鱼数据隔离
 */
async function testXianyuIsolation() {
  console.log('\n🐟 === 测试闲鱼数据隔离 ===');
  
  try {
    // 测试执行日志隔离
    const user1Logs = await axios.get(`${BASE_URL}/api/xianyu/logs`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Logs = await axios.get(`${BASE_URL}/api/xianyu/logs`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '闲鱼执行日志隔离',
      true,
      `用户1日志数: ${user1Logs.data.data?.logs?.length || 0}, 用户2日志数: ${user2Logs.data.data?.logs?.length || 0}`
    );
    
  } catch (error) {
    logTest('闲鱼数据隔离测试', false, `测试失败: ${error.message}`);
  }
}

/**
 * 测试文件管理数据隔离
 */
async function testFileIsolation() {
  console.log('\n📁 === 测试文件管理数据隔离 ===');
  
  try {
    // 测试文件列表隔离
    const user1Files = await axios.get(`${BASE_URL}/api/file/list`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Files = await axios.get(`${BASE_URL}/api/file/list`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '文件列表隔离',
      true,
      `用户1文件数: ${user1Files.data.data?.length || 0}, 用户2文件数: ${user2Files.data.data?.length || 0}`
    );
    
    // 测试文件传输记录隔离
    const user1Transfers = await axios.get(`${BASE_URL}/api/file/transfers`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Transfers = await axios.get(`${BASE_URL}/api/file/transfers`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '文件传输记录隔离',
      true,
      `用户1传输记录数: ${user1Transfers.data.data?.records?.length || 0}, 用户2传输记录数: ${user2Transfers.data.data?.records?.length || 0}`
    );
    
  } catch (error) {
    logTest('文件管理数据隔离测试', false, `测试失败: ${error.message}`);
  }
}

/**
 * 测试脚本管理数据隔离
 */
async function testScriptIsolation() {
  console.log('\n📜 === 测试脚本管理数据隔离 ===');
  
  try {
    // 测试脚本列表隔离
    const user1Scripts = await axios.get(`${BASE_URL}/api/script/list`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Scripts = await axios.get(`${BASE_URL}/api/script/list`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '脚本列表隔离',
      true,
      `用户1脚本数: ${user1Scripts.data.data?.length || 0}, 用户2脚本数: ${user2Scripts.data.data?.length || 0}`
    );
    
    // 测试脚本执行日志隔离
    const user1Logs = await axios.get(`${BASE_URL}/api/script/logs/list`, {
      headers: getAuthHeaders(userTokens.testuser1)
    });
    
    const user2Logs = await axios.get(`${BASE_URL}/api/script/logs/list`, {
      headers: getAuthHeaders(userTokens.testuser2)
    });
    
    logTest(
      '脚本执行日志隔离',
      true,
      `用户1日志数: ${user1Logs.data.data?.logs?.length || 0}, 用户2日志数: ${user2Logs.data.data?.logs?.length || 0}`
    );
    
  } catch (error) {
    logTest('脚本管理数据隔离测试', false, `测试失败: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runDataIsolationTests() {
  console.log('🚀 开始数据隔离全面测试...\n');
  
  // 1. 注册测试用户
  console.log('👥 === 准备测试用户 ===');
  for (const user of TEST_USERS) {
    const registered = await registerUser(user);
    logTest(`用户注册 ${user.username}`, registered, registered ? '注册成功' : '注册失败');
  }
  
  // 2. 登录获取令牌
  console.log('\n🔑 === 用户登录 ===');
  for (const user of TEST_USERS) {
    const token = await loginUser(user.username, user.password);
    if (token) {
      userTokens[user.username] = token;
      logTest(`用户登录 ${user.username}`, true, '登录成功');
    } else {
      logTest(`用户登录 ${user.username}`, false, '登录失败');
    }
  }
  
  // 检查是否有足够的用户令牌
  if (Object.keys(userTokens).length < 2) {
    console.log('❌ 测试终止：需要至少2个用户令牌');
    return;
  }
  
  // 3. 执行各模块数据隔离测试
  await testDeviceIsolation();
  await testXiaohongshuIsolation();
  await testXianyuIsolation();
  await testFileIsolation();
  await testScriptIsolation();
  
  // 4. 输出测试结果
  console.log('\n📊 === 测试结果汇总 ===');
  console.log(`✅ 通过测试: ${testResults.passed}个`);
  console.log(`❌ 失败测试: ${testResults.failed}个`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2)}%`);
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有数据隔离测试通过！系统已完全支持多用户数据隔离。');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查数据隔离实现。');
  }
  
  // 5. 保存详细测试报告
  const report = {
    testTime: new Date().toISOString(),
    summary: {
      total: testResults.passed + testResults.failed,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: ((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2) + '%'
    },
    details: testResults.details
  };
  
  fs.writeFileSync('data-isolation-test-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 详细测试报告已保存到: data-isolation-test-report.json');
}

// 执行测试
runDataIsolationTests().catch(error => {
  console.error('❌ 测试执行失败:', error);
});
