"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[145],{5145:function(t,e,i){i.r(e),i.d(e,{default:function(){return u}});var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"login-container full-height flex-center"},[e("el-card",{staticClass:"login-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"text-center",attrs:{slot:"header"},slot:"header"},[e("h2",[t._v("Auto.js云群控系统")]),e("p",{staticClass:"subtitle"},[t._v("基于主站账号的安全登录")])]),e("el-form",{ref:"loginForm",attrs:{model:t.loginForm,rules:t.loginRules,"label-width":"100px"},nativeOn:{submit:function(e){return e.preventDefault(),t.handleLogin.apply(null,arguments)}}},[e("el-form-item",{attrs:{label:"用户名",prop:"username"}},[e("el-input",{attrs:{placeholder:"请输入主站用户名","prefix-icon":"el-icon-user"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleLogin.apply(null,arguments)}},model:{value:t.loginForm.username,callback:function(e){t.$set(t.loginForm,"username",e)},expression:"loginForm.username"}})],1),e("el-form-item",{attrs:{label:"密码",prop:"password"}},[e("el-input",{attrs:{type:"password",placeholder:"请输入主站密码","prefix-icon":"el-icon-lock","show-password":""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleLogin.apply(null,arguments)}},model:{value:t.loginForm.password,callback:function(e){t.$set(t.loginForm,"password",e)},expression:"loginForm.password"}})],1),t.showActivationCode?e("el-form-item",{attrs:{label:"卡密/激活码",prop:"activationCode"}},[e("el-input",{attrs:{placeholder:"请输入卡密或激活码","prefix-icon":"el-icon-key"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleLogin.apply(null,arguments)}},model:{value:t.loginForm.activationCode,callback:function(e){t.$set(t.loginForm,"activationCode",e)},expression:"loginForm.activationCode"}}),e("div",{staticClass:"activation-hint"},[t.activationHint?e("el-tag",{attrs:{size:"mini",type:"info"}},[t._v(" "+t._s(t.activationHint)+" ")]):t._e()],1)],1):t._e(),e("el-form-item",[e("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",loading:t.loading},on:{click:t.handleLogin}},[t._v(" "+t._s(t.loading?"登录中...":"登录")+" ")])],1)],1),t.accountStatus?e("div",{staticClass:"account-status"},[e("el-alert",{attrs:{title:t.accountStatus.title,description:t.accountStatus.description,type:t.accountStatus.type,"show-icon":"",closable:!1}})],1):t._e(),e("div",{staticClass:"login-footer"},[e("el-divider",[t._v("系统说明")]),e("ul",{staticClass:"feature-list"},[e("li",[t._v("✅ 使用主站账号进行身份验证")]),e("li",[t._v("👤 支持真实姓名、昵称或手机号登录")]),e("li",[t._v("🔑 首次登录需要提供卡密或激活码")]),e("li",[t._v("⏰ 账号具有时效性，过期需要续期")]),e("li",[t._v("🔒 多用户数据完全隔离")])]),e("p",{staticClass:"fallback-info"},[t._v(" 💡 如主站不可用，系统将自动切换到本地认证模式 ")])],1)],1)],1)},o=[],s=i(4335),n={name:"Login",data(){return{loading:!1,showActivationCode:!1,activationHint:"",accountStatus:null,loginForm:{username:"",password:"",activationCode:""},loginRules:{username:[{required:!0,message:"请输入用户名（真实姓名/昵称/手机号）",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],activationCode:[{required:!1,message:"请输入卡密或激活码",trigger:"blur",validator:(t,e,i)=>{this.showActivationCode&&!e?i(new Error("请输入卡密或激活码")):i()}}]}}},methods:{async handleLogin(){try{await this.$refs.loginForm.validate(),this.loading=!0,this.accountStatus=null;const t=await s.A.post("/api/auth/login",this.loginForm),e=t.data;e.success&&(localStorage.setItem("token",e.token),this.$store.commit("auth/SET_USER",e.user),this.$store.commit("auth/SET_TOKEN",e.token),e.isFirstLogin?this.$message.success("首次登录成功，账号已激活！"):e.isRenewal?this.$message.success("账号续期成功！"):this.$message.success("登录成功"),setTimeout(()=>{this.$router.push("/dashboard")},500))}catch(t){console.error("登录失败:",t);const e=t.response?.data;e?.requireActivation?(this.showActivationCode=!0,this.activationHint="首次登录需要卡密或激活码",this.accountStatus={title:"首次登录验证",description:"检测到您是首次登录，请输入有效的卡密或激活码以激活账号使用权限",type:"info"}):e?.requireRenewal?(this.showActivationCode=!0,this.activationHint="账号已过期，需要续期",this.accountStatus={title:"账号已过期",description:`您的账号已于 ${new Date(e.expiresAt).toLocaleString()} 过期，请输入卡密或激活码进行续期`,type:"warning"}):(this.$message.error(e?.message||"登录失败"),(e?.message?.includes("验证失败")||e?.message?.includes("无效"))&&(this.showActivationCode=!1,this.activationHint="",this.accountStatus=null,this.loginForm.activationCode=""))}finally{this.loading=!1}}}},l=n,r=i(1656),c=(0,r.A)(l,a,o,!1,null,"43695a3b",null),u=c.exports}}]);