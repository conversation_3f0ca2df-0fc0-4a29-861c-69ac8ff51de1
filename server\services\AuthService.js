/**
 * 认证服务核心类
 * 基于主站ddwx_member表实现账号验证和权限管理
 */

const bcrypt = require('bcryptjs');

class AuthService {
  constructor(mainPool, localPool) {
    this.mainPool = mainPool;
    this.localPool = localPool;
    this.isMainDbAvailable = false;

    // 初始化时检查主站数据库连接
    this.checkMainDbConnection();
  }

  /**
   * 检查主站数据库连接状态
   */
  async checkMainDbConnection() {
    try {
      if (this.mainPool) {
        const connection = await this.mainPool.getConnection();
        await connection.execute('SELECT 1');
        connection.release();
        this.isMainDbAvailable = true;
        console.log('✅ 主站数据库连接可用');
      }
    } catch (error) {
      this.isMainDbAvailable = false;
      console.log('❌ 主站数据库连接不可用，将使用本地认证模式');
      console.log('💡 提示：如需使用主站验证，请配置正确的主站数据库连接');
    }
  }

  /**
   * 验证主站账号（基于ddwx_member表）
   * @param {string} username 用户名（可以是realname、nickname或tel）
   * @param {string} password 密码
   * @returns {Object|null} 用户信息或null
   */
  async verifyMainAccount(username, password) {
    if (!this.isMainDbAvailable) {
      console.log('主站数据库不可用，跳过主站账号验证');
      return null;
    }

    try {
      // 使用您提供的查询语句
      const [users] = await this.mainPool.execute(
        `SELECT id, nickname, pwd, realname, tel, headimg, levelid
         FROM ddwx_member
         WHERE realname = ? OR nickname = ? OR tel = ?`,
        [username, username, username]
      );

      if (users.length === 0) {
        console.log(`主站用户不存在: ${username}`);
        return null;
      }

      const user = users[0];

      // 验证密码（支持明文和MD5）
      let isValid = false;
      if (user.pwd === password) {
        // 明文密码匹配
        isValid = true;
      } else if (user.pwd && user.pwd.length === 32) {
        // 可能是MD5，尝试MD5验证
        const crypto = require('crypto');
        const md5Password = crypto.createHash('md5').update(password).digest('hex');
        isValid = user.pwd.toLowerCase() === md5Password.toLowerCase();
      }

      if (isValid) {
        console.log(`主站账号验证成功: ${username}`);
        return user;
      } else {
        console.log(`主站账号密码错误: ${username}`);
        return null;
      }
    } catch (error) {
      console.error('主站账号验证失败:', error);
      return null;
    }
  }

  /**
   * 验证卡密/激活码（从本地数据库）
   * @param {string} code 卡密/激活码
   * @returns {Object|null} 卡密信息或null
   */
  async verifyActivationCode(code) {
    try {
      const [codes] = await this.localPool.execute(
        `SELECT * FROM activation_codes
         WHERE code = ? AND status = 'active'
         AND used_count < max_uses
         AND (expires_at IS NULL OR expires_at > NOW())`,
        [code]
      );

      if (codes.length > 0) {
        console.log(`卡密验证成功: ${code}`);
        return codes[0];
      } else {
        console.log(`卡密验证失败: ${code}`);
        return null;
      }
    } catch (error) {
      console.error('卡密验证失败:', error);
      return null;
    }
  }

  /**
   * 检查用户权限（基于ddwx_member表的简化版本）
   * @param {Object} mainUser 主站用户信息
   * @returns {Object} 权限检查结果
   */
  async checkUserPermissions(mainUser) {
    // 基于levelid判断权限
    const hasValidLevel = mainUser.levelid > 0;
    const isAdmin = mainUser.levelid >= 10; // 假设等级10以上为管理员

    return {
      isValid: true, // 能查询到就说明账号有效
      hasValidLevel,
      permissions: {
        xiaohongshu: hasValidLevel, // 有等级就能使用小红书功能
        xianyu: hasValidLevel, // 有等级就能使用闲鱼功能
        admin: isAdmin
      }
    };
  }

  /**
   * 首次登录处理（创建本地用户记录）
   * @param {Object} mainUser 主站用户信息
   * @param {Object} activationCode 激活码信息
   * @param {string} ipAddress IP地址
   * @param {string} userAgent 用户代理
   * @returns {Object} 处理结果
   */
  async handleFirstLogin(mainUser, activationCode, ipAddress = '', userAgent = '') {
    const connection = await this.localPool.getConnection();

    try {
      await connection.beginTransaction();

      // 根据主站用户权限设置本地账号
      const permissions = await this.checkUserPermissions(mainUser);

      // 根据激活码设置时效
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + activationCode.duration_days);

      // 确定用户名（优先使用tel，其次nickname，最后realname）
      const username = mainUser.tel || mainUser.nickname || mainUser.realname;

      // 创建本地用户记录
      const [result] = await connection.execute(
        `INSERT INTO users (username, password, email, main_user_id, expires_at, last_activation_at,
         activation_count, total_duration_days, account_status, is_main_verified, last_main_sync_at, role)
         VALUES (?, ?, ?, ?, ?, NOW(), 1, ?, 'active', TRUE, NOW(), ?)`,
        [
          username,
          mainUser.pwd,
          '', // 邮箱为空
          mainUser.id,
          expiresAt,
          activationCode.duration_days,
          permissions.permissions.admin ? 'admin' : 'user'
        ]
      );

      const localUserId = result.insertId;

      // 记录本地激活历史
      await connection.execute(
        `INSERT INTO local_activations (user_id, main_user_id, activation_code, activation_type,
         duration_days, expires_at, ip_address, user_agent, notes)
         VALUES (?, ?, ?, 'first_login', ?, ?, ?, ?, '首次登录激活')`,
        [localUserId, mainUser.id, activationCode.code, activationCode.duration_days, expiresAt, ipAddress, userAgent]
      );

      // 更新卡密使用记录
      await connection.execute(
        'UPDATE activation_codes SET used_count = used_count + 1 WHERE id = ?',
        [activationCode.id]
      );

      await connection.commit();

      console.log(`首次登录处理成功: ${username}, 本地用户ID: ${localUserId}`);

      return {
        success: true,
        localUserId,
        expiresAt,
        isFirstLogin: true,
        permissions,
        username
      };

    } catch (error) {
      await connection.rollback();
      console.error('首次登录处理失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 检查本地账号时效性
   * @param {string} username 用户名
   * @returns {Object} 检查结果
   */
  async checkLocalAccountValidity(username) {
    try {
      const [users] = await this.localPool.execute(
        'SELECT * FROM users WHERE username = ?',
        [username]
      );

      if (users.length === 0) {
        return { exists: false };
      }

      const user = users[0];
      const now = new Date();
      const expiresAt = user.expires_at ? new Date(user.expires_at) : null;

      const isValid = expiresAt && now < expiresAt && user.account_status === 'active';

      console.log(`本地账号检查: ${username}, 存在: true, 有效: ${isValid}`);

      return {
        exists: true,
        user,
        isValid,
        expiresAt,
        daysRemaining: expiresAt ? Math.ceil((expiresAt - now) / (1000 * 60 * 60 * 24)) : 0
      };
    } catch (error) {
      console.error('本地账号检查失败:', error);
      throw error;
    }
  }

  /**
   * 续期账号时效
   * @param {number} localUserId 本地用户ID
   * @param {Object} activationCode 激活码信息
   * @param {string} ipAddress IP地址
   * @param {string} userAgent 用户代理
   * @returns {Date} 新的过期时间
   */
  async renewAccountValidity(localUserId, activationCode, ipAddress = '', userAgent = '') {
    const connection = await this.localPool.getConnection();

    try {
      await connection.beginTransaction();

      // 获取当前用户信息
      const [currentUser] = await connection.execute(
        'SELECT expires_at, main_user_id, activation_count, total_duration_days, account_status FROM users WHERE id = ?',
        [localUserId]
      );

      if (currentUser.length === 0) {
        throw new Error('用户不存在');
      }

      const user = currentUser[0];

      // 检查账号是否被管理员禁用
      if (user.account_status === 'disabled') {
        throw new Error('账号已被管理员禁用，无法续期');
      }

      // 计算新的过期时间
      let newExpiresAt = new Date();
      if (user.expires_at && new Date(user.expires_at) > new Date()) {
        // 如果当前时效未过期，在现有基础上延长
        newExpiresAt = new Date(user.expires_at);
      }
      newExpiresAt.setDate(newExpiresAt.getDate() + activationCode.duration_days);

      // 更新本地用户时效（保持原有的account_status，不强制设为active）
      await connection.execute(
        `UPDATE users SET expires_at = ?, last_activation_at = NOW(),
         activation_count = ?, total_duration_days = ? WHERE id = ?`,
        [newExpiresAt, user.activation_count + 1,
         user.total_duration_days + activationCode.duration_days, localUserId]
      );

      // 记录本地激活历史
      await connection.execute(
        `INSERT INTO local_activations (user_id, main_user_id, activation_code, activation_type,
         duration_days, expires_at, ip_address, user_agent, notes)
         VALUES (?, ?, ?, 'renewal', ?, ?, ?, ?, '账号续期')`,
        [localUserId, user.main_user_id, activationCode.code, activationCode.duration_days,
         newExpiresAt, ipAddress, userAgent]
      );

      // 更新卡密使用记录
      await connection.execute(
        'UPDATE activation_codes SET used_count = used_count + 1 WHERE id = ?',
        [activationCode.id]
      );

      await connection.commit();

      console.log(`账号续期成功: 用户ID ${localUserId}, 新过期时间: ${newExpiresAt}`);

      return newExpiresAt;

    } catch (error) {
      await connection.rollback();
      console.error('账号续期失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 同步主站用户权限到本地
   * @param {number} localUserId 本地用户ID
   * @param {Object} mainUser 主站用户信息
   * @returns {Object} 同步结果
   */
  async syncUserPermissions(localUserId, mainUser) {
    try {
      const permissions = await this.checkUserPermissions(mainUser);

      // 更新本地用户权限
      await this.localPool.execute(
        `UPDATE users SET
         account_status = 'active',
         last_main_sync_at = NOW(),
         role = ?
         WHERE id = ?`,
        [
          permissions.permissions.admin ? 'admin' : 'user',
          localUserId
        ]
      );

      console.log(`用户权限同步成功: 本地用户ID ${localUserId}`);

      return {
        success: true,
        permissions
      };

    } catch (error) {
      console.error('用户权限同步失败:', error);
      throw error;
    }
  }

  /**
   * 本地认证模式登录（当主站数据库不可用时）
   * @param {string} username 用户名
   * @param {string} password 密码
   * @returns {Object|null} 用户信息或null
   */
  async localAuthentication(username, password) {
    try {
      const [users] = await this.localPool.execute(
        'SELECT * FROM users WHERE username = ? AND is_active = 1',
        [username]
      );

      if (users.length === 0) {
        return null;
      }

      const user = users[0];

      // 验证密码（支持明文和MD5）
      let passwordValid = false;
      if (user.password === password) {
        passwordValid = true;
      } else if (user.password && user.password.length === 32) {
        const crypto = require('crypto');
        const md5Password = crypto.createHash('md5').update(password).digest('hex');
        passwordValid = user.password.toLowerCase() === md5Password.toLowerCase();
      }

      if (passwordValid) {
        console.log(`本地认证成功: ${username}`);
        return user;
      } else {
        console.log(`本地认证密码错误: ${username}`);
        return null;
      }
    } catch (error) {
      console.error('本地认证失败:', error);
      return null;
    }
  }
}

module.exports = AuthService;
