-- =====================================================
-- Auto.js云群控系统 - 本地数据库升级脚本
-- 扩展本地用户表结构，支持主站账号验证和时效管理
-- =====================================================

USE autojs_control;

-- =====================================================
-- 1. 扩展用户表结构
-- =====================================================

-- 添加主站账号相关字段
ALTER TABLE users
ADD COLUMN IF NOT EXISTS main_user_id INT NULL COMMENT '主站用户ID' AFTER id,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP NULL COMMENT '账号时效过期时间' AFTER password,
ADD COLUMN IF NOT EXISTS last_activation_at TIMESTAMP NULL COMMENT '最后激活时间' AFTER expires_at,
ADD COLUMN IF NOT EXISTS activation_count INT DEFAULT 0 COMMENT '激活次数' AFTER last_activation_at,
ADD COLUMN IF NOT EXISTS total_duration_days INT DEFAULT 0 COMMENT '总激活天数' AFTER activation_count,
ADD COLUMN IF NOT EXISTS account_status ENUM('active', 'expired', 'disabled', 'trial') DEFAULT 'active' COMMENT '账号状态' AFTER role,
ADD COLUMN IF NOT EXISTS is_main_verified BOOLEAN DEFAULT FALSE COMMENT '是否已主站验证' AFTER account_status,
ADD COLUMN IF NOT EXISTS last_main_sync_at TIMESTAMP NULL COMMENT '最后主站同步时间' AFTER is_main_verified;

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_users_main_user_id ON users(main_user_id);
CREATE INDEX IF NOT EXISTS idx_users_expires_at ON users(expires_at);
CREATE INDEX IF NOT EXISTS idx_users_account_status ON users(account_status);
CREATE INDEX IF NOT EXISTS idx_users_is_main_verified ON users(is_main_verified);

-- =====================================================
-- 2. 创建本地激活记录表
-- =====================================================

CREATE TABLE IF NOT EXISTS local_activations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '本地用户ID',
  main_user_id INT COMMENT '主站用户ID',
  activation_code VARCHAR(100) NOT NULL COMMENT '使用的激活码',
  activation_type ENUM('first_login', 'renewal', 'trial') DEFAULT 'first_login' COMMENT '激活类型',
  duration_days INT NOT NULL COMMENT '激活天数',
  activated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '激活时间',
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  ip_address VARCHAR(45) COMMENT '激活IP地址',
  user_agent TEXT COMMENT '用户代理信息',
  notes TEXT COMMENT '备注信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_main_user_id (main_user_id),
  INDEX idx_activation_code (activation_code),
  INDEX idx_activation_type (activation_type),
  INDEX idx_activated_at (activated_at),
  INDEX idx_expires_at (expires_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='本地激活记录表';

-- =====================================================
-- 3. 创建用户会话表
-- =====================================================

CREATE TABLE IF NOT EXISTS user_sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT '用户ID',
  session_token VARCHAR(255) NOT NULL COMMENT '会话令牌',
  jwt_token TEXT COMMENT 'JWT令牌',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  expires_at TIMESTAMP NOT NULL COMMENT '会话过期时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
  logout_at TIMESTAMP NULL COMMENT '登出时间',
  
  UNIQUE KEY uk_session_token (session_token),
  INDEX idx_user_id (user_id),
  INDEX idx_jwt_token (jwt_token(100)),
  INDEX idx_ip_address (ip_address),
  INDEX idx_login_at (login_at),
  INDEX idx_expires_at (expires_at),
  INDEX idx_is_active (is_active),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- =====================================================
-- 4. 创建系统通知表
-- =====================================================

CREATE TABLE IF NOT EXISTS system_notifications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '目标用户ID（NULL表示全局通知）',
  title VARCHAR(200) NOT NULL COMMENT '通知标题',
  content TEXT COMMENT '通知内容',
  type ENUM('info', 'warning', 'error', 'success', 'system') DEFAULT 'info' COMMENT '通知类型',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
  is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
  is_global BOOLEAN DEFAULT FALSE COMMENT '是否全局通知',
  expires_at TIMESTAMP NULL COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  read_at TIMESTAMP NULL COMMENT '阅读时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_priority (priority),
  INDEX idx_is_read (is_read),
  INDEX idx_is_global (is_global),
  INDEX idx_created_at (created_at),
  INDEX idx_expires_at (expires_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统通知表';

-- =====================================================
-- 5. 数据迁移和初始化
-- =====================================================

-- 更新现有用户的账号状态
UPDATE users SET 
  account_status = 'active',
  is_main_verified = FALSE,
  expires_at = DATE_ADD(NOW(), INTERVAL 365 DAY),
  last_activation_at = NOW(),
  activation_count = 1,
  total_duration_days = 365
WHERE account_status IS NULL;

-- 为管理员账号设置特殊状态
UPDATE users SET 
  account_status = 'active',
  is_main_verified = TRUE,
  expires_at = DATE_ADD(NOW(), INTERVAL 3650 DAY), -- 10年有效期
  main_user_id = 1
WHERE username = 'admin' OR role = 'admin';

-- =====================================================
-- 6. 创建视图和存储过程
-- =====================================================

-- 用户状态统计视图
CREATE OR REPLACE VIEW user_status_stats AS
SELECT 
  account_status,
  COUNT(*) as user_count,
  COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as valid_count,
  COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_count
FROM users 
GROUP BY account_status;

-- 用户设备统计视图
CREATE OR REPLACE VIEW user_device_stats AS
SELECT 
  u.id as user_id,
  u.username,
  u.account_status,
  u.expires_at,
  COUNT(d.id) as device_count,
  COUNT(CASE WHEN d.status = 'online' THEN 1 END) as online_devices,
  COUNT(CASE WHEN d.status = 'busy' THEN 1 END) as busy_devices,
  MAX(d.last_seen) as last_device_activity
FROM users u
LEFT JOIN devices d ON u.id = d.user_id
GROUP BY u.id, u.username, u.account_status, u.expires_at;

-- 用户执行统计视图
CREATE OR REPLACE VIEW user_execution_stats AS
SELECT 
  u.id as user_id,
  u.username,
  COUNT(DISTINCT xl.id) as xiaohongshu_executions,
  COUNT(DISTINCT yl.id) as xianyu_executions,
  COUNT(DISTINCT xl.id) + COUNT(DISTINCT yl.id) as total_executions,
  MAX(GREATEST(COALESCE(xl.started_at, '1970-01-01'), COALESCE(yl.started_at, '1970-01-01'))) as last_execution
FROM users u
LEFT JOIN xiaohongshu_execution_logs xl ON u.id = xl.user_id
LEFT JOIN xianyu_execution_logs yl ON u.id = yl.user_id
GROUP BY u.id, u.username;

-- 检查账号过期的存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CheckExpiredAccounts()
BEGIN
  -- 更新过期账号状态
  UPDATE users 
  SET account_status = 'expired' 
  WHERE expires_at <= NOW() 
    AND account_status = 'active';
  
  -- 返回过期账号信息
  SELECT 
    id, username, email, expires_at, 
    DATEDIFF(NOW(), expires_at) as days_expired
  FROM users 
  WHERE account_status = 'expired';
END //
DELIMITER ;

-- =====================================================
-- 7. 创建定时任务事件
-- =====================================================

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建每日检查过期账号的事件
CREATE EVENT IF NOT EXISTS daily_check_expired_accounts
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
  CALL CheckExpiredAccounts();
  
  -- 清理过期的会话
  DELETE FROM user_sessions 
  WHERE expires_at <= NOW() OR logout_at IS NOT NULL;
  
  -- 清理过期的通知
  DELETE FROM system_notifications 
  WHERE expires_at <= NOW() AND expires_at IS NOT NULL;
END;

-- =====================================================
-- 完成信息
-- =====================================================

-- 显示升级完成信息
SELECT '本地数据库升级完成！' as message;
SELECT 'Extended users table with main account support' as info;
SELECT 'Created local_activations, user_sessions, system_notifications tables' as tables_info;
SELECT 'Created views and procedures for user management' as views_info;

-- 显示当前用户状态统计
SELECT * FROM user_status_stats;

COMMIT;
