/**
 * 服务器公共API模块 - 完整拆分版本
 * 包含所有无需认证的公共API
 * 对应原始文件中所有不需要认证的API，包含以下功能：
 * - 测试API端点
 * - 健康检查API
 * - 小红书测试API
 * - 闲鱼测试API
 * - 调试状态API
 * - 设备检查停止信号API
 * - 脚本状态上报API
 * - 其他公共API
 * 以及所有相关的公共功能
 */

// 服务器公共API模块设置函数
async function setupServerPublicApis(app, io, coreData, authData) {
  console.log('🔧 设置服务器公共API模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 测试API端点（用于CORS测试）(原始文件第10786行)
  app.get('/api/test', (req, res) => {
    res.json({
      success: true,
      message: 'CORS测试成功',
      timestamp: new Date().toISOString(),
      server: '群控服务器',
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      connectedDevices: devices.size,
      connectedWebClients: webClients.size
    });
  });

  // 小红书测试API (原始文件第1850行)
  app.get('/api/xiaohongshu/test', (req, res) => {
    res.json({
      success: true,
      message: '小红书自动化API正常工作',
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      features: [
        '修改资料',
        '搜索群聊加群发消息',
        '循环群发消息',
        '搜索文章评论',
        'UID私信'
      ]
    });
  });

  // 闲鱼测试API (原始文件第9762行)
  app.get('/api/xianyu/test', (req, res) => {
    res.json({
      success: true,
      message: '闲鱼自动化API正常工作',
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      features: [
        '关键词私信'
      ]
    });
  });

  // 设备检查停止信号API (原始文件第4507行)
  app.get('/api/device/check-stop', (req, res) => {
    try {
      const { deviceId } = req.query;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 检查设备是否有停止命令
      const commands = pendingCommands.get(deviceId) || [];
      const hasStopCommand = commands.some(cmd => cmd.type === 'stop_script' || cmd.type === 'stop');

      console.log(`设备 ${deviceId} 检查停止状态: ${hasStopCommand ? '有停止命令' : '无停止命令'}`);

      res.json({
        success: true,
        shouldStop: hasStopCommand,
        deviceId: deviceId,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('检查停止状态失败:', error);
      res.status(500).json({
        success: false,
        message: '检查停止状态失败: ' + error.message
      });
    }
  });

  // 设备通知脚本已停止API (原始文件第4536行)
  app.post('/api/device/script-stopped', (req, res) => {
    try {
      const { deviceId, timestamp } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      console.log(`设备 ${deviceId} 报告脚本已停止，时间: ${timestamp}`);

      // 更新设备状态
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          device.status = 'online'; // 从忙碌状态恢复到在线状态
          device.lastSeen = new Date();
          devices.set(socketId, device);
          break;
        }
      }

      // 清除该设备的停止命令
      const commands = pendingCommands.get(deviceId) || [];
      const filteredCommands = commands.filter(cmd => 
        cmd.type !== 'stop_script' && cmd.type !== 'stop'
      );
      pendingCommands.set(deviceId, filteredCommands);

      // 通知前端脚本已停止
      io.emit('script_stopped', {
        deviceId,
        timestamp: timestamp || new Date(),
        status: 'stopped'
      });

      res.json({
        success: true,
        message: '脚本停止通知已接收'
      });

    } catch (error) {
      console.error('处理脚本停止通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理脚本停止通知失败: ' + error.message
      });
    }
  });

  // 获取小红书任务状态API (原始文件第5353行)
  app.get('/api/xiaohongshu/tasks', (req, res) => {
    try {
      // 这里需要从其他模块获取活跃任务数据
      // 暂时返回空数组，实际实现需要访问xiaohongshuActiveTasks
      const activeTasks_array = [];

      res.json({
        success: true,
        data: {
          activeTasks: activeTasks_array,
          totalCount: activeTasks_array.length,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('获取小红书任务状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取任务状态失败: ' + error.message
      });
    }
  });

  // 调试API：查看当前执行状态（无需认证）(原始文件第5794行)
  app.get('/api/xiaohongshu/debug-status', async (req, res) => {
    try {
      if (!pool) {
        return res.json({ success: false, message: '数据库未连接' });
      }

      // 查询正在执行的任务
      const [runningTasks] = await pool.execute(`
        SELECT device_id, function_type, execution_status, started_at, progress_percentage
        FROM xiaohongshu_execution_logs 
        WHERE execution_status IN ('running', 'pending')
        ORDER BY started_at DESC
        LIMIT 20
      `);

      // 查询最近完成的任务
      const [recentTasks] = await pool.execute(`
        SELECT device_id, function_type, execution_status, started_at, completed_at, progress_percentage
        FROM xiaohongshu_execution_logs 
        WHERE execution_status IN ('completed', 'failed', 'stopped')
        ORDER BY completed_at DESC
        LIMIT 10
      `);

      res.json({
        success: true,
        data: {
          runningTasks: runningTasks,
          recentTasks: recentTasks,
          connectedDevices: devices.size,
          webClients: webClients.size,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('获取调试状态失败:', error);
      res.status(500).json({
        success: false,
        message: '获取调试状态失败: ' + error.message
      });
    }
  });

  // 闲鱼脚本实时状态上报API (原始文件第9594行)
  app.post('/api/xianyu/realtime-status', (req, res) => {
    try {
      const statusData = req.body;
      console.log('收到闲鱼脚本实时状态上报:', statusData);

      // 通知前端状态更新
      io.emit('xianyu_realtime_status', {
        ...statusData,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '状态上报已接收'
      });

    } catch (error) {
      console.error('处理闲鱼实时状态上报失败:', error);
      res.status(500).json({
        success: false,
        message: '处理状态上报失败: ' + error.message
      });
    }
  });

  // 闲鱼脚本执行完成通知API (原始文件第9627行)
  app.post('/api/xianyu/execution-completed', (req, res) => {
    try {
      const resultData = req.body;
      console.log('收到闲鱼脚本执行完成通知:', resultData);

      // 通知前端执行完成
      io.emit('xianyu_execution_completed', {
        ...resultData,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '执行完成通知已接收'
      });

    } catch (error) {
      console.error('处理闲鱼执行完成通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理完成通知失败: ' + error.message
      });
    }
  });

  // 小红书实时状态API（统一处理所有功能）(原始文件第7699行)
  app.post('/api/xiaohongshu/realtime-status', async (req, res) => {
    try {
      const {
        deviceId,
        functionType,
        status,
        progress,
        message,
        stage,
        taskId,
        debugInfo,
        timestamp
      } = req.body;

      console.log(`📊 [实时状态] 设备 ${deviceId} - ${functionType}: ${status} (${progress}%)`);
      if (message) {
        console.log(`📊 [实时状态] 消息: ${message}`);
      }

      // 通知前端实时状态更新
      io.emit('xiaohongshu_realtime_status', {
        deviceId,
        functionType,
        status,
        progress,
        message,
        stage,
        taskId,
        debugInfo,
        timestamp: timestamp || new Date()
      });

      res.json({
        success: true,
        message: '实时状态已接收'
      });

    } catch (error) {
      console.error('处理小红书实时状态失败:', error);
      res.status(500).json({
        success: false,
        message: '处理实时状态失败: ' + error.message
      });
    }
  });

  // 视频下载API - 供手机端下载视频文件，支持分块传输 (原始文件第7589行) - 已添加用户隔离
  app.get('/api/xiaohongshu/download-video/:videoId', authenticateToken, async (req, res) => {
    try {
      const { videoId } = req.params;
      const range = req.headers.range;
      const userId = req.user?.id;

      console.log(`📹 [视频下载] 用户${userId}的设备请求下载视频: ${videoId}`);
      if (range) {
        console.log(`📹 [视频下载] Range请求: ${range}`);
      }

      if (!pool) {
        return res.status(503).json({
          success: false,
          message: '数据库服务不可用'
        });
      }

      // 查询视频信息（按用户过滤）
      const [videos] = await pool.execute(`
        SELECT id, original_name as filename, file_path, file_size, 'video/mp4' as mime_type
        FROM xiaohongshu_video_files
        WHERE id = ? AND status = 'active' AND user_id = ?
      `, [videoId, userId]);

      if (videos.length === 0) {
        return res.status(404).json({
          success: false,
          message: '视频文件不存在'
        });
      }

      const video = videos[0];
      const filePath = video.file_path;

      // 检查文件是否存在
      const fs = require('fs');
      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '视频文件不存在于服务器'
        });
      }

      const stat = fs.statSync(filePath);
      const fileSize = stat.size;

      // 处理Range请求（分块传输）
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
        const chunksize = (end - start) + 1;

        res.status(206);
        res.set({
          'Content-Range': `bytes ${start}-${end}/${fileSize}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunksize,
          'Content-Type': video.mime_type || 'video/mp4'
        });

        const stream = fs.createReadStream(filePath, { start, end });
        stream.pipe(res);
      } else {
        // 完整文件传输
        res.set({
          'Content-Length': fileSize,
          'Content-Type': video.mime_type || 'video/mp4',
          'Content-Disposition': `attachment; filename="${video.filename}"`
        });

        const stream = fs.createReadStream(filePath);
        stream.pipe(res);
      }

      console.log(`📹 [视频下载] 开始传输视频: ${video.filename}`);

    } catch (error) {
      console.error('视频下载失败:', error);
      res.status(500).json({
        success: false,
        message: '视频下载失败: ' + error.message
      });
    }
  });

  console.log('✅ 服务器公共API模块设置完成');

  // 返回公共API相关函数供其他模块使用
  return {
    // 可以在这里返回一些公共API相关的工具函数
  };
}

module.exports = { setupServerPublicApis };
