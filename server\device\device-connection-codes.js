/**
 * 设备连接码管理模块
 * 实现设备连接码的生成、管理和验证功能
 */

const crypto = require('crypto');

// 生成随机连接码
function generateConnectionCode() {
  // 生成8位随机字符串，包含数字和大写字母
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 设备连接码管理API
async function setupDeviceConnectionCodes(app, pool, authenticateToken, userIsolationMiddleware) {
  console.log('🔧 设置设备连接码管理模块...');

  // 首先确保数据库表存在
  if (pool) {
    try {
      console.log('📋 检查并创建设备连接码相关表...');

      // 创建设备连接码表
      await pool.execute(`
        CREATE TABLE IF NOT EXISTS device_connection_codes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          code VARCHAR(20) UNIQUE NOT NULL COMMENT '连接码',
          user_id INT NOT NULL COMMENT '用户ID',
          username VARCHAR(50) NOT NULL COMMENT '用户名',
          description VARCHAR(200) DEFAULT '' COMMENT '连接码描述',
          max_devices INT DEFAULT 1 COMMENT '最大可连接设备数',
          used_count INT DEFAULT 0 COMMENT '已使用次数',
          expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
          is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_code (code),
          INDEX idx_user_id (user_id),
          INDEX idx_expires_at (expires_at),
          INDEX idx_is_active (is_active)
        )
      `);

      // 创建设备连接记录表
      await pool.execute(`
        CREATE TABLE IF NOT EXISTS device_connections (
          id INT AUTO_INCREMENT PRIMARY KEY,
          device_id VARCHAR(100) NOT NULL,
          connection_code VARCHAR(20) NOT NULL COMMENT '使用的连接码',
          user_id INT NOT NULL COMMENT '分配的用户ID',
          device_name VARCHAR(100) NOT NULL,
          device_info JSON COMMENT '设备信息',
          connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_device_id (device_id),
          INDEX idx_connection_code (connection_code),
          INDEX idx_user_id (user_id)
        )
      `);

      console.log('✅ 设备连接码相关表检查完成');
    } catch (error) {
      console.log('⚠️ 创建设备连接码表失败:', error.message);
    }
  }

  // 创建连接码API
  app.post('/api/device/connection-codes', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { description, maxDevices = 1, expiresInHours } = req.body;
      const userId = req.currentUserId;
      const username = req.user.username;

      console.log(`[连接码创建] 用户${userId}(${username})创建连接码`);

      if (!pool) {
        return res.status(500).json({ 
          success: false, 
          message: '数据库连接不可用' 
        });
      }

      // 生成唯一连接码
      let code;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        code = generateConnectionCode();
        attempts++;
        
        // 检查连接码是否已存在
        const [existing] = await pool.execute(
          'SELECT id FROM device_connection_codes WHERE code = ?',
          [code]
        );
        
        if (existing.length === 0) {
          break; // 找到唯一连接码
        }
        
        if (attempts >= maxAttempts) {
          return res.status(500).json({
            success: false,
            message: '生成连接码失败，请重试'
          });
        }
      } while (true);

      // 计算过期时间
      let expiresAt = null;
      if (expiresInHours && expiresInHours > 0) {
        expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000);
      }

      // 插入连接码记录
      await pool.execute(`
        INSERT INTO device_connection_codes 
        (code, user_id, username, description, max_devices, expires_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [code, userId, username, description || '', maxDevices, expiresAt]);

      console.log(`✅ 连接码创建成功: ${code} (用户${userId})`);

      res.json({
        success: true,
        message: '连接码创建成功',
        data: {
          code,
          description,
          maxDevices,
          expiresAt,
          createdAt: new Date()
        }
      });

    } catch (error) {
      console.error('创建连接码失败:', error);
      res.status(500).json({
        success: false,
        message: '创建失败: ' + error.message
      });
    }
  });

  // 获取用户的连接码列表API
  app.get('/api/device/connection-codes', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const userId = req.currentUserId;

      if (!pool) {
        return res.status(500).json({ 
          success: false, 
          message: '数据库连接不可用' 
        });
      }

      const [codes] = await pool.execute(`
        SELECT 
          id, code, description, max_devices, used_count, 
          expires_at, is_active, created_at, updated_at
        FROM device_connection_codes 
        WHERE user_id = ? 
        ORDER BY created_at DESC
      `, [userId]);

      res.json({
        success: true,
        data: codes.map(code => ({
          ...code,
          isExpired: code.expires_at ? new Date() > new Date(code.expires_at) : false,
          isAvailable: code.is_active && 
                      (code.expires_at ? new Date() <= new Date(code.expires_at) : true) &&
                      code.used_count < code.max_devices
        }))
      });

    } catch (error) {
      console.error('获取连接码列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取失败: ' + error.message
      });
    }
  });

  // 删除连接码API
  app.delete('/api/device/connection-codes/:id', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { id } = req.params;
      const userId = req.currentUserId;

      if (!pool) {
        return res.status(500).json({ 
          success: false, 
          message: '数据库连接不可用' 
        });
      }

      // 验证连接码所属权
      const [codes] = await pool.execute(
        'SELECT code FROM device_connection_codes WHERE id = ? AND user_id = ?',
        [id, userId]
      );

      if (codes.length === 0) {
        return res.status(404).json({
          success: false,
          message: '连接码不存在或无权删除'
        });
      }

      // 删除连接码
      await pool.execute(
        'DELETE FROM device_connection_codes WHERE id = ? AND user_id = ?',
        [id, userId]
      );

      console.log(`✅ 连接码删除成功: ${codes[0].code} (用户${userId})`);

      res.json({
        success: true,
        message: '连接码删除成功'
      });

    } catch (error) {
      console.error('删除连接码失败:', error);
      res.status(500).json({
        success: false,
        message: '删除失败: ' + error.message
      });
    }
  });

  // 验证连接码API（供设备使用）
  app.post('/api/device/verify-connection-code', async (req, res) => {
    try {
      const { code, deviceId, deviceName, deviceInfo } = req.body;

      console.log(`[连接码验证] 设备${deviceId}使用连接码: ${code}`);

      if (!code || !deviceId || !deviceName) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数：连接码、设备ID和设备名称'
        });
      }

      if (!pool) {
        return res.status(500).json({ 
          success: false, 
          message: '数据库连接不可用' 
        });
      }

      // 查找并验证连接码
      const [codes] = await pool.execute(`
        SELECT id, user_id, username, max_devices, used_count, expires_at, is_active
        FROM device_connection_codes 
        WHERE code = ?
      `, [code]);

      if (codes.length === 0) {
        console.log(`[连接码验证] 连接码不存在: ${code}`);
        return res.status(404).json({
          success: false,
          message: '连接码不存在'
        });
      }

      const connectionCode = codes[0];

      // 检查连接码是否激活
      if (!connectionCode.is_active) {
        console.log(`[连接码验证] 连接码已禁用: ${code}`);
        return res.status(400).json({
          success: false,
          message: '连接码已禁用'
        });
      }

      // 检查是否过期
      if (connectionCode.expires_at && new Date() > new Date(connectionCode.expires_at)) {
        console.log(`[连接码验证] 连接码已过期: ${code}`);
        return res.status(400).json({
          success: false,
          message: '连接码已过期'
        });
      }

      // 检查使用次数限制
      if (connectionCode.used_count >= connectionCode.max_devices) {
        console.log(`[连接码验证] 连接码使用次数已达上限: ${code}`);
        return res.status(400).json({
          success: false,
          message: '连接码使用次数已达上限'
        });
      }

      // 检查设备是否已经使用过此连接码
      const [existingConnections] = await pool.execute(
        'SELECT id FROM device_connections WHERE device_id = ? AND connection_code = ?',
        [deviceId, code]
      );

      if (existingConnections.length > 0) {
        console.log(`[连接码验证] 设备${deviceId}已使用过连接码${code}`);
        return res.json({
          success: true,
          message: '设备已连接',
          data: {
            userId: connectionCode.user_id,
            username: connectionCode.username,
            alreadyConnected: true
          }
        });
      }

      // 记录设备连接
      await pool.execute(`
        INSERT INTO device_connections 
        (device_id, connection_code, user_id, device_name, device_info)
        VALUES (?, ?, ?, ?, ?)
      `, [deviceId, code, connectionCode.user_id, deviceName, JSON.stringify(deviceInfo || {})]);

      // 更新连接码使用次数
      await pool.execute(
        'UPDATE device_connection_codes SET used_count = used_count + 1 WHERE code = ?',
        [code]
      );

      console.log(`✅ 设备连接成功: ${deviceId} -> 用户${connectionCode.user_id}(${connectionCode.username})`);

      res.json({
        success: true,
        message: '连接码验证成功',
        data: {
          userId: connectionCode.user_id,
          username: connectionCode.username,
          alreadyConnected: false
        }
      });

    } catch (error) {
      console.error('验证连接码失败:', error);
      res.status(500).json({
        success: false,
        message: '验证失败: ' + error.message
      });
    }
  });

  console.log('✅ 设备连接码管理模块设置完成');
}

module.exports = {
  setupDeviceConnectionCodes,
  generateConnectionCode
};
