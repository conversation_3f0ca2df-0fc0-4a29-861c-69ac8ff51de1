"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[400],{4400:function(t,s,e){e.r(s),e.d(s,{default:function(){return r}});var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"layout-container"},[s("el-container",{staticClass:"full-height"},[s("el-aside",{staticClass:"sidebar",attrs:{width:"200px"}},[s("div",{staticClass:"logo"},[s("h3",[t._v("Auto.js群控")])]),s("el-menu",{staticClass:"sidebar-menu",attrs:{"default-active":t.$route.path,"background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",router:""}},[s("el-menu-item",{attrs:{index:"/dashboard"}},[s("i",{staticClass:"el-icon-odometer"}),s("span",[t._v("仪表盘")])]),s("el-menu-item",{attrs:{index:"/devices"}},[s("i",{staticClass:"el-icon-mobile-phone"}),s("span",[t._v("设备管理")])]),s("el-menu-item",{attrs:{index:"/scripts"}},[s("i",{staticClass:"el-icon-document"}),s("span",[t._v("脚本管理")])]),s("el-menu-item",{attrs:{index:"/script-config"}},[s("i",{staticClass:"el-icon-setting"}),s("span",[t._v("脚本配置执行")])]),s("el-menu-item",{attrs:{index:"/files"}},[s("i",{staticClass:"el-icon-folder"}),s("span",[t._v("文件管理")])]),s("el-menu-item",{attrs:{index:"/logs"}},[s("i",{staticClass:"el-icon-tickets"}),s("span",[t._v("执行日志")])]),s("el-menu-item",{attrs:{index:"/xiaohongshu"}},[s("i",{staticClass:"el-icon-star-on"}),s("span",[t._v("小红书自动化")])]),s("el-menu-item",{attrs:{index:"/xiaohongshu-logs"}},[s("i",{staticClass:"el-icon-notebook-1"}),s("span",[t._v("小红书执行日志")])]),s("el-menu-item",{attrs:{index:"/xianyu"}},[s("i",{staticClass:"el-icon-fish"}),s("span",[t._v("闲鱼自动化")])]),s("el-menu-item",{attrs:{index:"/xianyu-logs"}},[s("i",{staticClass:"el-icon-document-copy"}),s("span",[t._v("闲鱼执行日志")])]),t.user&&"admin"===t.user.role?s("el-submenu",{attrs:{index:"admin"}},[s("template",{slot:"title"},[s("i",{staticClass:"el-icon-s-tools"}),s("span",[t._v("系统管理")])]),s("el-menu-item",{attrs:{index:"/admin/activation-codes"}},[s("i",{staticClass:"el-icon-key"}),s("span",[t._v("卡密管理")])]),s("el-menu-item",{attrs:{index:"/admin/users"}},[s("i",{staticClass:"el-icon-user-solid"}),s("span",[t._v("用户管理")])])],2):t._e()],1)],1),s("el-container",[s("el-header",{staticClass:"header"},[s("div",{staticClass:"header-left"},[s("span",{staticClass:"page-title"},[t._v(t._s(t.$route.meta.title||"控制台"))])]),s("div",{staticClass:"header-right"},[s("el-tag",{staticClass:"connection-status",attrs:{type:t.connected?"success":"danger",size:"small"}},[t._v(" "+t._s(t.connected?"已连接":"未连接")+" ")]),s("el-dropdown",{on:{command:t.handleCommand}},[s("span",{staticClass:"user-dropdown"},[s("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(t.user.username)+" "),s("i",{staticClass:"el-icon-arrow-down"})]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[s("el-dropdown-item",{attrs:{command:"logout"}},[t._v("退出登录")])],1)],1)],1)]),s("el-main",{staticClass:"main-content"},[s("router-view")],1)],1)],1)],1)},i=[],n={name:"Layout",computed:{user(){return this.$store.getters["auth/user"]},connected(){return this.$store.getters["socket/connected"]}},methods:{handleCommand(t){"logout"===t&&this.handleLogout()},async handleLogout(){try{await this.$confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),this.$store.dispatch("auth/logout"),this.$router.push("/login"),this.$message.success("已退出登录")}catch(t){}}}},o=n,l=e(1656),c=(0,l.A)(o,a,i,!1,null,"38e45cd8",null),r=c.exports}}]);