<template>
  <div class="devices">
    <el-card>
      <div slot="header" class="card-header">
        <span>设备管理</span>
        <div>
          <el-button
            type="primary"
            icon="el-icon-refresh"
            @click="refreshDevices"
            :loading="loading"
          >
            刷新
          </el-button>

          <el-button
            type="success"
            icon="el-icon-user"
            @click="showAssignDialog"
            v-if="isAdmin"
          >
            设备分配
          </el-button>
          <el-button
            type="info"
            icon="el-icon-question"
            @click="showUnassignedDevices"
            v-if="isAdmin"
          >
            未分配设备
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-key"
            @click="showConnectionCodesDialog"
          >
            连接码管理
          </el-button>
        </div>
      </div>

      <!-- 设备统计 -->
      <div class="device-stats">
        <el-tag type="success" size="medium">在线: {{ onlineCount }}</el-tag>
        <el-tag type="warning" size="medium">忙碌: {{ busyCount }}</el-tag>
        <el-tag type="danger" size="medium">离线: {{ offlineCount }}</el-tag>
        <el-tag type="info" size="medium">总计: {{ totalCount }}</el-tag>
      </div>

      <!-- 设备表格 -->
      <el-table
        :data="devices"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="device_name" label="设备名称" width="150" />

        <el-table-column prop="device_id" label="设备ID" width="200" show-overflow-tooltip />

        <el-table-column label="设备信息" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.device_info">
              <div>{{ scope.row.device_info.brand }} {{ scope.row.device_info.model }}</div>
              <div class="device-detail">
                Android {{ scope.row.device_info.androidVersion }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="IP地址" width="130">
          <template slot-scope="scope">
            <span>{{ scope.row.ip_address || '未知' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="屏幕分辨率" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.device_info">
              {{ scope.row.device_info.screenWidth }}x{{ scope.row.device_info.screenHeight }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="last_seen" label="最后在线" width="150">
          <template slot-scope="scope">
            {{ $moment(scope.row.last_seen).fromNow() }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="380">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewDevice(scope.row)"
            >
              详情
            </el-button>
            <el-button
              size="mini"
              type="info"
              @click="viewDeviceApps(scope.row)"
            >
              查看应用
            </el-button>
            <el-button
              size="mini"
              type="success"
              @click="testDevice(scope.row)"
              :disabled="scope.row.status !== 'online'"
            >
              测试
            </el-button>
            <el-button
              size="mini"
              type="warning"
              @click="disconnectDevice(scope.row)"
              :disabled="scope.row.status !== 'online'"
            >
              断开
            </el-button>

            <el-button
              size="mini"
              type="danger"
              @click="deleteDevice(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedDevices.length > 0">
        <span>已选择 {{ selectedDevices.length }} 个设备</span>
        <el-button
          type="primary"
          size="small"
          @click="batchTest"
        >
          批量测试
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="showScriptDialog"
        >
          执行脚本
        </el-button>
      </div>
    </el-card>

    <!-- 设备详情对话框 -->
    <el-dialog
      title="设备详情"
      :visible.sync="deviceDialogVisible"
      width="900px"
    >
      <div v-if="currentDevice">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称">
            {{ currentDevice.device_name }}
          </el-descriptions-item>
          <el-descriptions-item label="设备ID">
            {{ currentDevice.device_id }}
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ currentDevice.ip_address || '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="品牌">
            {{ currentDevice.device_info?.brand }}
          </el-descriptions-item>
          <el-descriptions-item label="型号">
            {{ currentDevice.device_info?.model }}
          </el-descriptions-item>
          <el-descriptions-item label="Android版本">
            {{ currentDevice.device_info?.androidVersion }}
          </el-descriptions-item>
          <el-descriptions-item label="屏幕分辨率">
            {{ currentDevice.device_info?.screenWidth }}x{{ currentDevice.device_info?.screenHeight }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDevice.status)">
              {{ getStatusText(currentDevice.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后在线">
            {{ $moment(currentDevice.last_seen).format('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 设备应用信息 -->
        <div style="margin-top: 20px;">
          <h4>设备应用信息</h4>
          <el-button
            type="primary"
            size="small"
            @click="loadDeviceApps"
            :loading="loadingApps"
            style="margin-bottom: 10px;"
          >
            {{ loadingApps ? '加载中...' : '刷新应用信息' }}
          </el-button>

          <div v-if="deviceApps">
            <!-- 小红书应用 -->
            <el-card shadow="never" style="margin-bottom: 10px;">
              <div slot="header">
                <span>小红书应用 ({{ deviceApps.xiaohongshu.length }}个)</span>
              </div>
              <div v-if="deviceApps.xiaohongshu.length > 0">
                <el-table
                  :data="deviceApps.xiaohongshu"
                  size="small"
                  style="width: 100%"
                >
                  <el-table-column prop="text" label="应用名称" width="200" />
                  <el-table-column prop="method" label="检测方式" width="100">
                    <template slot-scope="scope">
                      <el-tag size="mini" :type="scope.row.method === 'keyword' ? 'success' : 'info'">
                        {{ scope.row.method === 'keyword' ? '关键词' : '正则' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="clickable" label="可点击" width="80">
                    <template slot-scope="scope">
                      <el-tag size="mini" :type="scope.row.clickable ? 'success' : 'danger'">
                        {{ scope.row.clickable ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="detectedAt" label="检测时间" width="150">
                    <template slot-scope="scope">
                      {{ $moment(scope.row.detectedAt).format('MM-DD HH:mm') }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-else class="no-data">
                <el-empty description="未检测到小红书应用" :image-size="60" />
              </div>
            </el-card>

            <!-- 闲鱼应用 -->
            <el-card shadow="never">
              <div slot="header">
                <span>闲鱼应用 ({{ deviceApps.xianyu.length }}个)</span>
              </div>
              <div v-if="deviceApps.xianyu.length > 0">
                <el-table
                  :data="deviceApps.xianyu"
                  size="small"
                  style="width: 100%"
                >
                  <el-table-column prop="text" label="应用名称" width="200" />
                  <el-table-column prop="method" label="检测方式" width="100">
                    <template slot-scope="scope">
                      <el-tag size="mini" :type="scope.row.method === 'keyword' ? 'success' : 'info'">
                        {{ scope.row.method === 'keyword' ? '关键词' : '正则' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="clickable" label="可点击" width="80">
                    <template slot-scope="scope">
                      <el-tag size="mini" :type="scope.row.clickable ? 'success' : 'danger'">
                        {{ scope.row.clickable ? '是' : '否' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="detectedAt" label="检测时间" width="150">
                    <template slot-scope="scope">
                      {{ $moment(scope.row.detectedAt).format('MM-DD HH:mm') }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-else class="no-data">
                <el-empty description="未检测到闲鱼应用" :image-size="60" />
              </div>
            </el-card>
          </div>

          <div v-else-if="!loadingApps" class="no-data">
            <el-empty description="点击上方按钮加载应用信息" :image-size="60" />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 设备应用信息对话框 -->
    <el-dialog
      title="设备应用信息"
      :visible.sync="appsDialogVisible"
      width="800px"
    >
      <div v-if="currentDevice">
        <div style="margin-bottom: 20px;">
          <h4>{{ currentDevice.device_name }} ({{ currentDevice.device_id }})</h4>
          <el-button
            type="primary"
            size="small"
            @click="loadDeviceApps"
            :loading="loadingApps"
          >
            {{ loadingApps ? '加载中...' : '刷新应用信息' }}
          </el-button>
        </div>

        <div v-if="deviceApps">
          <!-- 小红书应用 -->
          <el-card shadow="never" style="margin-bottom: 15px;">
            <div slot="header">
              <span>小红书应用 ({{ deviceApps.xiaohongshu.length }}个)</span>
            </div>
            <div v-if="deviceApps.xiaohongshu.length > 0">
              <el-table
                :data="deviceApps.xiaohongshu"
                size="small"
                style="width: 100%"
              >
                <el-table-column prop="text" label="应用名称" width="200" />
                <el-table-column prop="method" label="检测方式" width="100">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="scope.row.method === 'keyword' ? 'success' : 'info'">
                      {{ scope.row.method === 'keyword' ? '关键词' : '正则' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="clickable" label="可点击" width="80">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="scope.row.clickable ? 'success' : 'danger'">
                      {{ scope.row.clickable ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="bounds" label="位置信息" width="200" show-overflow-tooltip />
                <el-table-column prop="detectedAt" label="检测时间" width="150">
                  <template slot-scope="scope">
                    {{ $moment(scope.row.detectedAt).format('MM-DD HH:mm') }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="no-data">
              <el-empty description="未检测到小红书应用" :image-size="60" />
            </div>
          </el-card>

          <!-- 闲鱼应用 -->
          <el-card shadow="never">
            <div slot="header">
              <span>闲鱼应用 ({{ deviceApps.xianyu.length }}个)</span>
            </div>
            <div v-if="deviceApps.xianyu.length > 0">
              <el-table
                :data="deviceApps.xianyu"
                size="small"
                style="width: 100%"
              >
                <el-table-column prop="text" label="应用名称" width="200" />
                <el-table-column prop="method" label="检测方式" width="100">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="scope.row.method === 'keyword' ? 'success' : 'info'">
                      {{ scope.row.method === 'keyword' ? '关键词' : '正则' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="clickable" label="可点击" width="80">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="scope.row.clickable ? 'success' : 'danger'">
                      {{ scope.row.clickable ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="bounds" label="位置信息" width="200" show-overflow-tooltip />
                <el-table-column prop="detectedAt" label="检测时间" width="150">
                  <template slot-scope="scope">
                    {{ $moment(scope.row.detectedAt).format('MM-DD HH:mm') }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="no-data">
              <el-empty description="未检测到闲鱼应用" :image-size="60" />
            </div>
          </el-card>
        </div>

        <div v-else-if="!loadingApps" class="no-data">
          <el-empty description="点击上方按钮加载应用信息" :image-size="80" />
        </div>
      </div>
    </el-dialog>

    <!-- 脚本执行对话框 -->
    <el-dialog
      title="执行脚本"
      :visible.sync="scriptDialogVisible"
      width="500px"
    >
      <el-form>
        <el-form-item label="选择脚本">
          <el-select v-model="selectedScript" placeholder="请选择脚本" style="width: 100%">
            <el-option
              v-for="script in scripts"
              :key="script.id"
              :label="script.name"
              :value="script.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="scriptDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="executeScript"
          :loading="executing"
        >
          执行
        </el-button>
      </div>
    </el-dialog>



    <!-- 设备分配对话框 -->
    <el-dialog
      title="设备分配"
      :visible.sync="assignDialogVisible"
      width="600px"
    >
      <div>
        <p style="margin-bottom: 20px; color: #666;">
          将设备分配给指定用户，支持通过设备ID或IP地址查找设备。
        </p>

        <el-form label-width="100px">
          <el-form-item label="查找方式">
            <el-radio-group v-model="assignSearchType">
              <el-radio label="deviceId">设备ID</el-radio>
              <el-radio label="ipAddress">IP地址</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="assignSearchType === 'deviceId' ? '设备ID' : 'IP地址'">
            <el-input
              v-model="assignSearchValue"
              :placeholder="assignSearchType === 'deviceId' ? '请输入设备ID' : '请输入IP地址，如：*************'"
              clearable
            />
          </el-form-item>

          <el-form-item label="目标用户">
            <el-select
              v-model="assignTargetUserId"
              placeholder="请选择用户"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="`${user.username} (ID: ${user.id})`"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmAssign"
          :loading="assignLoading"
          :disabled="!assignSearchValue || !assignTargetUserId"
        >
          确认分配
        </el-button>
      </div>
    </el-dialog>

    <!-- 未分配设备对话框 -->
    <el-dialog
      title="未分配设备"
      :visible.sync="unassignedDialogVisible"
      width="800px"
    >
      <div>
        <p style="margin-bottom: 20px; color: #666;">
          以下是系统中未分配给任何用户的设备列表：
        </p>

        <el-table
          :data="unassignedDevices"
          v-loading="unassignedLoading"
          style="width: 100%"
        >
          <el-table-column prop="device_name" label="设备名称" width="150" />
          <el-table-column prop="device_id" label="设备ID" width="200" show-overflow-tooltip />
          <el-table-column label="IP地址" width="150">
            <template slot-scope="scope">
              {{ scope.row.device_info?.ipAddress || '未知' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === 'online' ? 'success' :
                       scope.row.status === 'busy' ? 'warning' : 'danger'"
                size="small"
              >
                {{ scope.row.status === 'online' ? '在线' :
                   scope.row.status === 'busy' ? '忙碌' : '离线' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="最后连接" width="150">
            <template slot-scope="scope">
              {{ formatTime(scope.row.last_seen) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="quickAssign(scope.row)"
              >
                快速分配
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="unassignedDialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="loadUnassignedDevices"
          :loading="unassignedLoading"
        >
          刷新
        </el-button>
      </div>
    </el-dialog>

    <!-- 连接码管理对话框 -->
    <el-dialog
      title="设备连接码管理"
      :visible.sync="connectionCodesDialogVisible"
      width="900px"
    >
      <div>
        <div style="margin-bottom: 20px;">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="showCreateCodeDialog"
          >
            创建连接码
          </el-button>
          <el-button
            type="info"
            icon="el-icon-refresh"
            @click="loadConnectionCodes"
            :loading="codesLoading"
          >
            刷新
          </el-button>
        </div>

        <el-table
          :data="connectionCodes"
          v-loading="codesLoading"
          style="width: 100%"
        >
          <el-table-column prop="code" label="连接码" width="120">
            <template slot-scope="scope">
              <el-tag type="primary" size="medium">{{ scope.row.code }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" width="150" show-overflow-tooltip />

          <el-table-column label="使用情况" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.used_count }}/{{ scope.row.max_devices }}</span>
              <el-progress
                :percentage="(scope.row.used_count / scope.row.max_devices) * 100"
                :stroke-width="6"
                :show-text="false"
                style="margin-top: 5px;"
              />
            </template>
          </el-table-column>

          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.isAvailable ? 'success' :
                       scope.row.isExpired ? 'danger' : 'warning'"
                size="small"
              >
                {{ scope.row.isAvailable ? '可用' :
                   scope.row.isExpired ? '已过期' : '已满' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="过期时间" width="150">
            <template slot-scope="scope">
              {{ scope.row.expires_at ? formatTime(scope.row.expires_at) : '永不过期' }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="150">
            <template slot-scope="scope">
              {{ formatTime(scope.row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="copyConnectionCode(scope.row.code)"
              >
                复制
              </el-button>
              <el-button
                type="text"
                size="small"
                style="color: #f56c6c;"
                @click="deleteConnectionCode(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="connectionCodesDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 创建连接码对话框 -->
    <el-dialog
      title="创建设备连接码"
      :visible.sync="createCodeDialogVisible"
      width="500px"
    >
      <el-form :model="newCodeForm" label-width="120px">
        <el-form-item label="描述">
          <el-input
            v-model="newCodeForm.description"
            placeholder="请输入连接码描述（可选）"
          />
        </el-form-item>

        <el-form-item label="最大设备数">
          <el-input-number
            v-model="newCodeForm.maxDevices"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="有效期">
          <el-select v-model="newCodeForm.expiresInHours" style="width: 100%">
            <el-option label="永不过期" :value="null" />
            <el-option label="1小时" :value="1" />
            <el-option label="24小时" :value="24" />
            <el-option label="7天" :value="168" />
            <el-option label="30天" :value="720" />
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="createCodeDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="createConnectionCode"
          :loading="createCodeLoading"
        >
          创建
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Devices',
  data() {
    return {
      selectedDevices: [],
      deviceDialogVisible: false,
      appsDialogVisible: false,
      scriptDialogVisible: false,
      currentDevice: null,
      selectedScript: null,
      executing: false,
      hybridManager: null,
      socket: null, // 保留用于兼容性
      // 自动刷新相关
      refreshTimer: null,
      refreshInterval: 6000, // 6秒刷新一次
      isAutoRefreshEnabled: true,
      // 设备应用信息相关
      deviceApps: null,
      loadingApps: false,

      // 设备分配相关
      assignDialogVisible: false,
      assignSearchType: 'ipAddress',
      assignSearchValue: '',
      assignTargetUserId: null,
      assignLoading: false,
      userList: [],
      // 未分配设备相关
      unassignedDialogVisible: false,
      unassignedDevices: [],
      unassignedLoading: false,
      // 连接码管理相关
      connectionCodesDialogVisible: false,
      connectionCodes: [],
      codesLoading: false,
      createCodeDialogVisible: false,
      createCodeLoading: false,
      newCodeForm: {
        description: '',
        maxDevices: 1,
        expiresInHours: null
      }
    }
  },
  computed: {
    devices() {
      return this.$store.getters['device/devices']
    },
    scripts() {
      return this.$store.getters['script/scripts']
    },
    loading() {
      return this.$store.getters['device/loading']
    },
    totalCount() {
      return this.devices.length
    },
    onlineCount() {
      return this.devices.filter(d => d.status === 'online').length
    },
    busyCount() {
      return this.devices.filter(d => d.status === 'busy').length
    },
    offlineCount() {
      return this.devices.filter(d => d.status === 'offline').length
    },
    isAdmin() {
      return this.$store.getters['auth/user']?.role === 'admin'
    }
  },
  async created() {
    await this.loadData()

    // 初始化实时双向断开功能
    this.initRealTimeSync()

    // 启动自动刷新
    this.startAutoRefresh()
  },

  beforeDestroy() {
    // 停止自动刷新
    this.stopAutoRefresh()

    // 清理定时器和WebSocket连接
    this.cleanupRealTimeSync()
  },
  methods: {
    async loadData() {
      await this.$store.dispatch('device/fetchDevices')
      await this.$store.dispatch('script/fetchScripts')
    },

    async refreshDevices() {
      console.log('刷新设备列表...')
      await this.$store.dispatch('device/fetchDevices')
      console.log('设备列表已刷新，当前设备数量:', this.devices.length)
    },

    // ===== 自动刷新方法 =====

    // 开始自动刷新
    startAutoRefresh() {
      if (!this.isAutoRefreshEnabled) return

      console.log(`[Devices] 开始自动刷新，间隔: ${this.refreshInterval}ms`)
      this.refreshTimer = setInterval(() => {
        this.autoRefreshDevices()
      }, this.refreshInterval)
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        console.log('[Devices] 停止自动刷新')
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 自动刷新设备信息（静默刷新）
    async autoRefreshDevices() {
      try {
        // 静默刷新，不显示loading状态
        await this.$store.dispatch('device/fetchDevices')

        // 只在控制台输出，不显示用户提示
        console.log('[Devices] 设备列表自动刷新完成，当前设备数量:', this.devices.length)
      } catch (error) {
        console.error('[Devices] 自动刷新失败:', error)
        // 刷新失败时不显示错误提示，避免干扰用户
      }
    },

    // 初始化实时同步功能
    initRealTimeSync() {
      console.log('初始化WebSocket通信功能')

      // 使用WebSocket管理器
      this.initWebSocketCommunication()
    },

    // 初始化WebSocket通信
    async initWebSocketCommunication() {
      try {
        // 导入WebSocket管理器
        const { getWebSocketManager, ensureConnection } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 确保连接状态
        await ensureConnection()

        // 注册事件处理器（避免重复注册）
        this.wsManager.off('connection_established')
        this.wsManager.on('connection_established', (data) => {
          console.log('WebSocket连接建立:', data.type)
          this.$message.success('✅ WebSocket连接已建立')
        })

        // 监听连接失败事件
        this.wsManager.off('connection_failed')
        this.wsManager.on('connection_failed', (data) => {
          console.error('WebSocket连接失败:', data)
          this.$message.error(`连接失败: ${data.reason}`)
        })

        // 监听设备状态变化
        this.wsManager.off('device_status_changed')
        this.wsManager.on('device_status_changed', (data) => {
          this.handleDeviceStatusChange(data)
        })

        // 监听设备列表更新
        this.wsManager.off('devices_list')
        this.wsManager.on('devices_list', (devices) => {
          console.log('收到设备列表更新:', devices)
          this.handleDevicesListUpdate(devices)
        })

        // 监听设备状态更新
        this.wsManager.off('device_status_update')
        this.wsManager.on('device_status_update', (data) => {
          console.log('收到设备状态更新:', data)
          this.handleDeviceStatusUpdate(data)
        })

        // 监听服务器关闭事件
        this.wsManager.off('server_shutdown')
        this.wsManager.on('server_shutdown', (data) => {
          console.log('服务器关闭通知:', data)
          this.$message.error('服务器已关闭: ' + data.message)
          // 清空设备列表
          this.$store.commit('device/SET_DEVICES', [])
        })

        // 初始化连接
        await this.wsManager.init()

        // 显示连接状态
        this.showConnectionStatus()

      } catch (error) {
        console.error('WebSocket通信初始化失败:', error.message)
        this.$message.error('通信初始化失败: ' + error.message)
      }
    },

    // 显示连接状态
    showConnectionStatus() {
      if (this.wsManager) {
        const status = this.wsManager.getConnectionStatus()
        console.log('连接状态:', status)

        if (status.isConnected) {
          this.$message.success('实时连接 (WebSocket)')
        }
      }
    },

    // 处理设备状态变化
    handleDeviceStatusChange(data) {
      console.log('收到设备状态变化通知:', data)

      switch (data.type) {
        case 'device_connected':
          this.$message.success(`设备 "${data.deviceName}" 已连接`)
          // 更新设备状态为在线
          this.$store.commit('device/UPDATE_DEVICE', {
            deviceId: data.deviceId,
            updates: { status: 'online' }
          })
          break
        case 'device_disconnected':
          this.$message.info(`设备 "${data.deviceName}" 已断开连接`)
          // 更新设备状态为离线
          this.$store.commit('device/UPDATE_DEVICE', {
            deviceId: data.deviceId,
            updates: { status: 'offline' }
          })
          break
        case 'device_deleted':
          this.$message.warning(`设备 "${data.deviceName}" 记录已删除`)
          // 从设备列表中移除设备
          this.$store.commit('device/REMOVE_DEVICE', data.deviceId)
          break
        case 'device_removed':
        case 'device_removed_by_pc':
          this.$message.warning(`设备 "${data.deviceName}" 已被移除`)
          // 从设备列表中移除设备
          this.$store.commit('device/REMOVE_DEVICE', data.deviceId)
          break
      }

      // 立即刷新设备列表（作为备用机制）
      console.log('状态变化事件触发设备列表刷新')
      this.refreshDevices()
    },

    // 处理设备列表更新
    handleDevicesListUpdate(devices) {
      console.log('处理设备列表更新:', devices)
      // 直接更新store中的设备列表，避免额外的API调用
      this.$store.commit('device/SET_DEVICES', devices.map(device => ({
        device_id: device.deviceId,
        device_name: device.deviceName,
        device_info: device.deviceInfo,
        status: device.status,
        last_seen: device.lastSeen,
        created_at: device.connectedAt,
        ip_address: device.ipAddress || 'Unknown'
      })))
    },

    // 处理设备状态更新
    handleDeviceStatusUpdate(data) {
      console.log('处理设备状态更新:', data)
      // 更新设备状态
      this.$store.commit('device/UPDATE_DEVICE', {
        deviceId: data.deviceId,
        updates: {
          status: data.status,
          last_seen: data.lastSeen || new Date().toISOString()
        }
      })
    },

    // 清理实时同步功能
    cleanupRealTimeSync() {
      console.log('清理设备页面事件监听器')

      // 只清理事件监听器，不断开全局连接
      if (this.wsManager) {
        this.wsManager.off('connection_established')
        this.wsManager.off('connection_failed')
        this.wsManager.off('device_status_changed')
        this.wsManager.off('devices_list')
        this.wsManager.off('device_status_update')
        this.wsManager.off('server_shutdown')
        this.wsManager = null
      }

      // 兼容性清理：清理可能存在的旧连接
      if (this.socket) {
        this.socket.disconnect()
        this.socket = null
      }
    },

    handleSelectionChange(selection) {
      this.selectedDevices = selection
    },

    viewDevice(device) {
      this.currentDevice = device
      this.deviceDialogVisible = true
      // 重置应用信息
      this.deviceApps = null
    },

    // 查看设备应用信息
    viewDeviceApps(device) {
      this.currentDevice = device
      this.appsDialogVisible = true
      // 重置应用信息
      this.deviceApps = null
      // 自动加载应用信息
      this.loadDeviceApps()
    },

    // 加载设备应用信息
    async loadDeviceApps() {
      if (!this.currentDevice) {
        this.$message.error('请先选择设备')
        return
      }

      this.loadingApps = true
      try {
        const response = await this.$http.get(`/api/device/${this.currentDevice.device_id}/apps`)
        if (response.data.success) {
          this.deviceApps = response.data.data
          this.$message.success('应用信息加载成功')
        } else {
          this.$message.error('加载应用信息失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('加载设备应用信息失败:', error)
        this.$message.error('加载应用信息失败: ' + (error.response?.data?.message || error.message))
      } finally {
        this.loadingApps = false
      }
    },

    async testDevice(device) {
      try {
        const testScript = `
          toast('来自群控系统的测试消息');
          console.log('设备测试成功: ${device.device_name}');
        `

        // 这里需要直接发送脚本内容，而不是脚本ID
        await this.$http.post('/api/script/execute', {
          deviceIds: [device.device_id],
          script: testScript
        })

        this.$message.success('测试脚本已发送')
      } catch (error) {
        this.$message.error('发送失败: ' + error.message)
      }
    },

    async disconnectDevice(device) {
      try {
        await this.$confirm(
          `确定要断开设备 "${device.device_name}" 的连接吗？\n\n断开后：\n• 手机端会同时收到断开通知并自动断开连接\n• 设备记录将保留，状态变为离线\n• 设备可以重新连接恢复在线状态`,
          '确认断开连接',
          {
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        console.log('PC端断开设备:', device.device_name, device.device_id)
        await this.$store.dispatch('device/deleteDevice', device.device_id)
        this.$message.success('设备连接已断开，设备记录已保留为离线状态')

        // 立即刷新设备列表
        setTimeout(() => {
          this.refreshDevices()
        }, 1000)
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('断开失败: ' + error.message)
        }
      }
    },

    async deleteDevice(device) {
      try {
        await this.$confirm(
          `确定要删除设备 "${device.device_name}" 吗？\n\n删除后：\n• 设备记录将从数据库中永久删除\n• 如果设备在线，将被强制断开连接\n• 此操作不可恢复`,
          '确认删除设备记录',
          {
            type: 'error',
            dangerouslyUseHTMLString: false
          }
        )

        await this.$store.dispatch('device/deleteDeviceRecord', device.device_id)
        this.$message.success('设备记录已删除')
        await this.refreshDevices()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },





    async batchTest() {
      const deviceIds = this.selectedDevices.map(d => d.device_id)
      const testScript = `
        toast('批量测试消息');
        console.log('批量测试执行成功');
      `

      try {
        await this.$http.post('/api/script/execute', {
          deviceIds,
          script: testScript
        })

        this.$message.success(`测试脚本已发送到 ${deviceIds.length} 个设备`)
      } catch (error) {
        this.$message.error('发送失败: ' + error.message)
      }
    },

    showScriptDialog() {
      this.scriptDialogVisible = true
    },

    async executeScript() {
      if (!this.selectedScript) {
        this.$message.warning('请选择要执行的脚本')
        return
      }

      this.executing = true
      try {
        const deviceIds = this.selectedDevices.map(d => d.device_id)

        await this.$store.dispatch('script/executeScript', {
          deviceIds,
          scriptId: this.selectedScript
        })

        this.$message.success(`脚本已发送到 ${deviceIds.length} 个设备`)
        this.scriptDialogVisible = false
      } catch (error) {
        this.$message.error('执行失败: ' + error.message)
      } finally {
        this.executing = false
      }
    },

    getStatusType(status) {
      const types = {
        online: 'success',
        offline: 'danger',
        busy: 'warning'
      }
      return types[status] || 'info'
    },

    getStatusText(status) {
      const texts = {
        online: '在线',
        offline: '离线',
        busy: '忙碌'
      }
      return texts[status] || status
    },

    // 连接码管理相关方法
    showConnectionCodesDialog() {
      this.connectionCodesDialogVisible = true
      this.loadConnectionCodes()
    },

    async loadConnectionCodes() {
      this.codesLoading = true
      try {
        const response = await this.$http.get('/api/device/connection-codes')
        if (response.data.success) {
          this.connectionCodes = response.data.data
        } else {
          this.$message.error('加载连接码失败: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('加载连接码失败: ' + error.message)
      } finally {
        this.codesLoading = false
      }
    },

    showCreateCodeDialog() {
      this.createCodeDialogVisible = true
      this.newCodeForm = {
        description: '',
        maxDevices: 1,
        expiresInHours: null
      }
    },

    async createConnectionCode() {
      this.createCodeLoading = true
      try {
        const response = await this.$http.post('/api/device/connection-codes', this.newCodeForm)
        if (response.data.success) {
          this.$message.success('连接码创建成功: ' + response.data.data.code)
          this.createCodeDialogVisible = false
          this.loadConnectionCodes()
        } else {
          this.$message.error('创建失败: ' + response.data.message)
        }
      } catch (error) {
        this.$message.error('创建失败: ' + error.message)
      } finally {
        this.createCodeLoading = false
      }
    },

    async copyConnectionCode(code) {
      try {
        await navigator.clipboard.writeText(code)
        this.$message.success('连接码已复制到剪贴板')
      } catch (error) {
        // 降级方案：创建临时输入框
        const textArea = document.createElement('textarea')
        textArea.value = code
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('连接码已复制到剪贴板')
      }
    },

    async deleteConnectionCode(id) {
      try {
        await this.$confirm('确定要删除这个连接码吗？', '确认删除', {
          type: 'warning'
        })

        const response = await this.$http.delete(`/api/device/connection-codes/${id}`)
        if (response.data.success) {
          this.$message.success('连接码删除成功')
          this.loadConnectionCodes()
        } else {
          this.$message.error('删除失败: ' + response.data.message)
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message)
        }
      }
    },

    formatTime(timeString) {
      if (!timeString) return ''
      return new Date(timeString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.devices {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-stats {
  margin-bottom: 20px;
}

.device-stats .el-tag {
  margin-right: 10px;
}

.device-detail {
  font-size: 12px;
  color: #999;
}

.batch-actions {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #999;
}
</style>
