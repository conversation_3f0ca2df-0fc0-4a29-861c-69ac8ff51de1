/**
 * 服务器认证模块 - 完整拆分版本
 * 包含所有认证相关的API和功能
 * 对应原始文件第298-322行的完整内容，包含以下API：
 * - POST /api/auth/login - 用户登录
 * - GET /api/auth/verify - 验证令牌
 * - GET /api/test/throttle-log - 测试节流日志
 * - GET /api/test - 测试API
 * 以及所有相关的认证和测试功能
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// JWT密钥（应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// 认证模块设置函数
async function setupServerAuth(app, io, coreData, authData) {
  console.log('🔧 设置认证模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 引入认证服务和数据库连接
  const { localPool, mainPool } = require('../config/database');
  const AuthService = require('../services/AuthService');

  // 创建认证服务实例
  const authService = new AuthService(mainPool, localPool);

  // 用户登录API - 支持完整认证流程
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { username, password, activationCode } = req.body;
      const ipAddress = req.ip || req.connection.remoteAddress || '';
      const userAgent = req.headers['user-agent'] || '';

      console.log('登录尝试:', { username: username, hasActivationCode: !!activationCode });

      if (!username || !password) {
        return res.status(400).json({
          success: false,
          message: '用户名和密码不能为空'
        });
      }

      // 1. 检查本地账号是否存在及时效性
      const localAccountCheck = await authService.checkLocalAccountValidity(username);

      if (localAccountCheck.exists) {
        // 本地账号存在，检查时效性
        if (localAccountCheck.isValid) {
          // 账号有效，验证主站账号状态（如果可用）
          const mainUser = await authService.verifyMainAccount(username, password);

          if (mainUser) {
            // 主站验证成功，同步权限
            await authService.syncUserPermissions(localAccountCheck.user.id, mainUser);

            // 生成JWT token
            const tokenPayload = {
              userId: localAccountCheck.user.id,
              username: localAccountCheck.user.username,
              email: localAccountCheck.user.email,
              role: localAccountCheck.user.role
            };

            const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

            // 更新最后登录时间
            await pool.execute(
              'UPDATE users SET last_login_time = NOW(), login_count = COALESCE(login_count, 0) + 1 WHERE id = ?',
              [localAccountCheck.user.id]
            );

            console.log(`用户 ${username} 登录成功`);

            return res.json({
              success: true,
              message: '登录成功',
              token: token,
              user: {
                id: localAccountCheck.user.id,
                username: localAccountCheck.user.username,
                email: localAccountCheck.user.email,
                role: localAccountCheck.user.role,
                expiresAt: localAccountCheck.user.expires_at,
                daysRemaining: localAccountCheck.daysRemaining
              }
            });
          } else if (!authService.isMainDbAvailable) {
            // 主站不可用，使用本地认证
            const localUser = await authService.localAuthentication(username, password);

            if (!localUser) {
              return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
              });
            }

            // 生成JWT token
            const tokenPayload = {
              userId: localUser.id,
              username: localUser.username,
              email: localUser.email,
              role: localUser.role
            };

            const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

            // 更新最后登录时间
            await pool.execute(
              'UPDATE users SET last_login_time = NOW(), login_count = COALESCE(login_count, 0) + 1 WHERE id = ?',
              [localUser.id]
            );

            console.log(`用户 ${username} 本地认证登录成功`);

            return res.json({
              success: true,
              message: '登录成功（本地认证模式）',
              token: token,
              user: {
                id: localUser.id,
                username: localUser.username,
                email: localUser.email,
                role: localUser.role,
                expiresAt: localUser.expires_at,
                daysRemaining: localAccountCheck.daysRemaining
              }
            });
          } else {
            return res.status(401).json({
              success: false,
              message: '主站账号验证失败'
            });
          }
        } else {
          // 账号已过期，需要续期
          if (!activationCode) {
            return res.status(401).json({
              success: false,
              message: '账号已过期，请输入卡密或激活码进行续期',
              requireRenewal: true,
              expiresAt: localAccountCheck.expiresAt
            });
          }

          // 验证主站账号
          const mainUser = await authService.verifyMainAccount(username, password);
          if (!mainUser && authService.isMainDbAvailable) {
            return res.status(401).json({
              success: false,
              message: '主站账号验证失败'
            });
          }

          // 验证卡密
          const codeInfo = await authService.verifyActivationCode(activationCode);
          if (!codeInfo) {
            return res.status(400).json({
              success: false,
              message: '卡密或激活码无效'
            });
          }

          // 续期账号
          const newExpiresAt = await authService.renewAccountValidity(
            localAccountCheck.user.id, codeInfo, ipAddress, userAgent
          );

          // 生成JWT token
          const tokenPayload = {
            userId: localAccountCheck.user.id,
            username: localAccountCheck.user.username,
            email: localAccountCheck.user.email,
            role: localAccountCheck.user.role
          };

          const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

          console.log(`用户 ${username} 续期成功`);

          return res.json({
            success: true,
            message: '账号续期成功',
            token: token,
            isRenewal: true,
            user: {
              id: localAccountCheck.user.id,
              username: localAccountCheck.user.username,
              email: localAccountCheck.user.email,
              role: localAccountCheck.user.role,
              expiresAt: newExpiresAt
            }
          });
        }
      } else {
        // 本地账号不存在，首次登录
        if (!activationCode) {
          return res.status(401).json({
            success: false,
            message: '首次登录需要卡密或激活码',
            requireActivation: true
          });
        }

        // 验证主站账号
        const mainUser = await authService.verifyMainAccount(username, password);
        if (!mainUser) {
          if (authService.isMainDbAvailable) {
            return res.status(401).json({
              success: false,
              message: '主站账号验证失败'
            });
          } else {
            return res.status(401).json({
              success: false,
              message: '主站数据库不可用，无法进行首次登录'
            });
          }
        }

        // 验证卡密
        const codeInfo = await authService.verifyActivationCode(activationCode);
        if (!codeInfo) {
          return res.status(400).json({
            success: false,
            message: '卡密或激活码无效'
          });
        }

        // 首次登录处理
        const result = await authService.handleFirstLogin(
          mainUser, codeInfo, ipAddress, userAgent
        );

        // 生成JWT token
        const tokenPayload = {
          userId: result.localUserId,
          username: result.username,
          email: '',
          role: result.permissions.permissions.admin ? 'admin' : 'user'
        };

        const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: '24h' });

        console.log(`用户 ${username} 首次登录成功`);

        return res.json({
          success: true,
          message: '首次登录成功，账号已激活',
          token: token,
          isFirstLogin: true,
          user: {
            id: result.localUserId,
            username: result.username,
            email: '',
            role: result.permissions.permissions.admin ? 'admin' : 'user',
            expiresAt: result.expiresAt
          }
        });
      }

    } catch (error) {
      console.error('登录失败:', error);

      // 处理特定的业务错误
      if (error.message && error.message.includes('账号已被管理员禁用')) {
        return res.status(403).json({
          success: false,
          message: '账号已被管理员禁用，无法登录'
        });
      }

      if (error.message && error.message.includes('卡密已被使用')) {
        return res.status(400).json({
          success: false,
          message: '卡密已被使用，请使用新的卡密'
        });
      }

      if (error.message && error.message.includes('卡密已过期')) {
        return res.status(400).json({
          success: false,
          message: '卡密已过期，请使用有效的卡密'
        });
      }

      // 其他未知错误
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  });

  // 验证令牌API (原始文件第313行)
  app.get('/api/auth/verify', authenticateToken, (req, res) => {
    res.json({
      success: true,
      message: '令牌有效',
      user: req.user
    });
  });

  // 测试节流日志功能的接口 (原始文件第322行)
  app.get('/api/test/throttle-log', (req, res) => {
    const testKey = 'test_throttle';
    const testMessage = `测试节流日志 - ${new Date().toLocaleTimeString()}`;
    
    // 测试节流日志功能
    const logged = throttledLog(testKey, testMessage);
    
    res.json({
      success: true,
      message: '节流日志测试完成',
      data: {
        key: testKey,
        message: testMessage,
        logged: logged,
        timestamp: new Date().toISOString()
      }
    });
  });

  // 通用测试API (原始文件第10786行)
  app.get('/api/test', (req, res) => {
    res.json({
      success: true,
      message: 'API测试成功',
      timestamp: new Date().toISOString(),
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version
      },
      statistics: {
        connectedDevices: devices.size,
        webClients: webClients.size,
        pendingCommands: Array.from(pendingCommands.values()).reduce((sum, cmds) => sum + cmds.length, 0),
        totalLogs: logs.length
      }
    });
  });

  console.log('✅ 认证模块设置完成');

  // 返回认证相关函数供其他模块使用
  return {
    // 可以在这里返回一些认证相关的工具函数
  };
}

module.exports = { setupServerAuth };
