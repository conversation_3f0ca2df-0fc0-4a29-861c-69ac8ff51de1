/**
 * 设备连接码功能测试脚本
 * 用于测试连接码的创建、验证和设备注册功能
 */

const mysql = require('mysql2/promise');
const jwt = require('jsonwebtoken');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'autojs_control',
  password: 'root',
  database: 'autojs_control',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// JWT密钥
const JWT_SECRET = 'your-secret-key';

async function testConnectionCodes() {
  let pool;
  
  try {
    // 创建数据库连接
    pool = mysql.createPool(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 检查必要的表是否存在
    console.log('\n📋 检查数据库表结构...');
    
    const tables = ['users', 'devices', 'device_connection_codes', 'device_connections'];
    for (const table of tables) {
      try {
        const [rows] = await pool.execute(`SHOW TABLES LIKE '${table}'`);
        if (rows.length > 0) {
          console.log(`✅ 表 ${table} 存在`);
        } else {
          console.log(`❌ 表 ${table} 不存在`);
        }
      } catch (error) {
        console.log(`❌ 检查表 ${table} 失败:`, error.message);
      }
    }

    // 2. 检查devices表是否有user_id字段
    try {
      const [columns] = await pool.execute(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'autojs_control' 
          AND TABLE_NAME = 'devices' 
          AND COLUMN_NAME = 'user_id'
      `);
      
      if (columns.length > 0) {
        console.log('✅ devices表有user_id字段');
      } else {
        console.log('❌ devices表缺少user_id字段');
        console.log('💡 请执行数据库更新脚本：数据库更新-设备连接码.sql');
      }
    } catch (error) {
      console.log('❌ 检查devices表字段失败:', error.message);
    }

    // 3. 检查是否有admin用户
    try {
      const [users] = await pool.execute('SELECT id, username FROM users WHERE username = "admin"');
      if (users.length > 0) {
        console.log(`✅ admin用户存在，ID: ${users[0].id}`);
        
        // 4. 创建测试连接码
        const testCode = 'TEST' + Math.random().toString(36).substr(2, 4).toUpperCase();
        console.log(`\n🔑 创建测试连接码: ${testCode}`);
        
        try {
          await pool.execute(`
            INSERT INTO device_connection_codes 
            (code, user_id, username, description, max_devices, expires_at)
            VALUES (?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 1 HOUR))
          `, [testCode, users[0].id, users[0].username, '测试连接码', 5]);
          
          console.log('✅ 测试连接码创建成功');
          
          // 5. 验证连接码
          const [codes] = await pool.execute(`
            SELECT id, user_id, username, max_devices, used_count, expires_at, is_active
            FROM device_connection_codes 
            WHERE code = ?
          `, [testCode]);
          
          if (codes.length > 0) {
            console.log('✅ 连接码验证成功');
            console.log('📋 连接码信息:', {
              code: testCode,
              userId: codes[0].user_id,
              username: codes[0].username,
              maxDevices: codes[0].max_devices,
              usedCount: codes[0].used_count,
              isActive: codes[0].is_active
            });
            
            // 6. 模拟设备连接
            const testDeviceId = 'test_device_' + Date.now();
            console.log(`\n📱 模拟设备连接: ${testDeviceId}`);
            
            try {
              // 记录设备连接
              await pool.execute(`
                INSERT INTO device_connections 
                (device_id, connection_code, user_id, device_name, device_info)
                VALUES (?, ?, ?, ?, ?)
              `, [testDeviceId, testCode, codes[0].user_id, '测试设备', JSON.stringify({
                brand: 'Test',
                model: 'TestDevice',
                ipAddress: '*************'
              })]);
              
              // 更新连接码使用次数
              await pool.execute(
                'UPDATE device_connection_codes SET used_count = used_count + 1 WHERE code = ?',
                [testCode]
              );
              
              // 注册设备到devices表
              await pool.execute(`
                INSERT INTO devices (device_id, device_name, device_info, status, user_id, last_seen)
                VALUES (?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                device_name = VALUES(device_name),
                device_info = VALUES(device_info),
                status = VALUES(status),
                user_id = VALUES(user_id),
                last_seen = NOW()
              `, [testDeviceId, '测试设备', JSON.stringify({
                brand: 'Test',
                model: 'TestDevice',
                ipAddress: '*************'
              }), 'online', codes[0].user_id]);
              
              console.log('✅ 设备连接模拟成功');
              
              // 7. 验证设备分配
              const [devices] = await pool.execute(
                'SELECT device_id, device_name, user_id, status FROM devices WHERE device_id = ?',
                [testDeviceId]
              );
              
              if (devices.length > 0) {
                console.log('✅ 设备分配验证成功');
                console.log('📋 设备信息:', {
                  deviceId: devices[0].device_id,
                  deviceName: devices[0].device_name,
                  userId: devices[0].user_id,
                  status: devices[0].status
                });
              }
              
              // 8. 清理测试数据
              console.log('\n🧹 清理测试数据...');
              await pool.execute('DELETE FROM device_connections WHERE device_id = ?', [testDeviceId]);
              await pool.execute('DELETE FROM devices WHERE device_id = ?', [testDeviceId]);
              await pool.execute('DELETE FROM device_connection_codes WHERE code = ?', [testCode]);
              console.log('✅ 测试数据清理完成');
              
            } catch (error) {
              console.log('❌ 设备连接模拟失败:', error.message);
            }
            
          } else {
            console.log('❌ 连接码验证失败');
          }
          
        } catch (error) {
          console.log('❌ 创建测试连接码失败:', error.message);
        }
        
      } else {
        console.log('❌ admin用户不存在');
        console.log('💡 请先运行数据库初始化脚本');
      }
    } catch (error) {
      console.log('❌ 检查admin用户失败:', error.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (pool) {
      await pool.end();
      console.log('\n📋 数据库连接已关闭');
    }
  }
}

// 运行测试
console.log('🚀 开始设备连接码功能测试...');
testConnectionCodes().then(() => {
  console.log('\n✅ 测试完成');
}).catch(error => {
  console.error('\n❌ 测试异常:', error);
});
