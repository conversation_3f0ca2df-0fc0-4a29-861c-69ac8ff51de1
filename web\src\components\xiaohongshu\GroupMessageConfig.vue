<template>
  <div class="group-message-config">
    <el-form :model="config" label-width="120px">
      <el-form-item label="群发消息内容">
        <el-input
          v-model="config.messageContent"
          type="textarea"
          :rows="4"
          placeholder="请输入要群发的消息内容"
          maxlength="500"
          show-word-limit
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          支持变量：{时间} {日期} {群名} {随机数}
        </div>
      </el-form-item>

      <el-form-item label="发送间隔">
        <el-input-number
          v-model="config.sendInterval"
          :min="5"
          :max="300"
        />
        <span style="margin-left: 10px; color: #909399;">每条消息发送间隔时间（秒）</span>
      </el-form-item>

      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <el-form-item label="执行模式">
        <el-radio-group v-model="config.executionMode">
          <el-radio label="once">单次执行</el-radio>
          <el-radio label="loop">循环执行</el-radio>
          <el-radio label="scheduled">定时执行</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="循环间隔" v-if="config.executionMode === 'loop'">
        <el-input-number
          v-model="config.loopInterval"
          :min="30"
          :max="1440"
        />
        <span style="margin-left: 10px; color: #909399;">每轮循环间隔时间（分钟）</span>
      </el-form-item>

      <el-form-item label="定时时间" v-if="config.executionMode === 'scheduled'">
        <el-time-picker
          v-model="config.scheduledTime"
          format="HH:mm"
          placeholder="选择执行时间"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          每天在指定时间执行群发
        </div>
      </el-form-item>

      <el-form-item label="群聊筛选">
        <el-checkbox-group v-model="config.groupFilter">
          <el-checkbox label="excludeSystemMsg">排除系统消息</el-checkbox>
          <el-checkbox label="excludeActivityMsg">排除活动消息</el-checkbox>
          <el-checkbox label="onlyActiveGroups">仅活跃群聊</el-checkbox>
          <el-checkbox label="excludeMutedGroups">排除已静音群聊</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="发送设置">
        <el-checkbox-group v-model="config.sendSettings">
          <el-checkbox label="randomDelay">随机延迟发送</el-checkbox>
          <el-checkbox label="skipSentGroups">跳过已发送群聊</el-checkbox>
          <el-checkbox label="autoScroll">自动滚动查找群聊</el-checkbox>
          <el-checkbox label="confirmBeforeSend">发送前确认</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="消息模板">
        <el-select v-model="config.messageTemplate" placeholder="选择消息模板">
          <el-option label="自定义消息" value="custom" />
          <el-option label="问候模板" value="greeting" />
          <el-option label="分享模板" value="sharing" />
          <el-option label="通知模板" value="notification" />
          <el-option label="营销模板" value="marketing" />
        </el-select>
      </el-form-item>

      <el-form-item label="模板内容" v-if="config.messageTemplate !== 'custom'">
        <el-input
          v-model="config.templateContent"
          type="textarea"
          :rows="3"
          readonly
          :placeholder="getTemplateContent()"
        />
      </el-form-item>

      <el-form-item label="执行统计">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="已发送消息" :value="sentMessageCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="处理控件数" :value="processedControlCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="执行次数" :value="executionCount" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="循环次数" :value="loopCount" />
          </el-col>
        </el-row>
        <div style="margin-top: 10px;">
          <el-tag :type="currentStatus === '等待开始' ? 'info' : 'success'">
            {{ currentStatus }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="高级设置">
        <el-checkbox-group v-model="config.advancedSettings">
          <el-checkbox label="enableRetry">发送失败时重试</el-checkbox>
          <el-checkbox label="logDetails">详细日志记录</el-checkbox>
          <el-checkbox label="pauseOnError">出错时暂停</el-checkbox>
          <el-checkbox label="smartInterval">智能间隔调整</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="重试设置" v-if="config.advancedSettings.includes('enableRetry')">
        <el-col :span="12">
          <el-input-number
            v-model="config.retryCount"
            :min="1"
            :max="5"
            placeholder="重试次数"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="12">
          <el-input-number
            v-model="config.retryDelay"
            :min="5"
            :max="60"
            placeholder="重试延迟（秒）"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>

      <el-alert
        title="使用提醒"
        type="info"
        :closable="false"
        show-icon
      >
        <div>
          • 群发消息请遵守相关法律法规和平台规则<br>
          • 建议设置合理的发送间隔，避免被识别为垃圾消息<br>
          • 循环执行模式会持续运行，请注意及时停止<br>
          • 发送前请仔细检查消息内容，避免发送错误信息
        </div>
      </el-alert>
    </el-form>
  </div>
</template>

<script>
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'GroupMessageConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 保存执行时的真实logId和taskId
      currentLogId: null,
      currentTaskId: null,
      // 实时状态数据
      sentMessageCount: 0,
      processedControlCount: 0,
      executionCount: 0,
      loopCount: 0,
      currentStatus: '等待开始',
      config: {
        messageContent: '',
        sendInterval: 10,
        executionMode: 'once',
        loopInterval: 60,
        scheduledTime: null,
        groupFilter: ['excludeSystemMsg', 'excludeActivityMsg'],
        sendSettings: ['randomDelay', 'skipSentGroups', 'autoScroll'],
        messageTemplate: 'custom',
        templateContent: '',
        targetGroupCount: 0,
        advancedSettings: ['logDetails'],
        retryCount: 3,
        retryDelay: 10,
        selectedApp: '' // 选择的小红书应用
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && typeof newVal === 'object') {
          this.config = { ...this.config, ...newVal }
        }
      },
      immediate: true
    },
    config: {
      handler(newVal, oldVal) {
        // 避免初始化时的无限循环
        if (oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.$nextTick(() => {
            this.updateConfig()
          })
        }
      },
      deep: true
    },
    'config.messageTemplate': {
      handler(newVal, oldVal) {
        if (newVal !== 'custom' && newVal !== oldVal) {
          this.$nextTick(() => {
            this.config.templateContent = this.getTemplateContent()
            this.config.messageContent = this.config.templateContent
          })
        }
      }
    }
  },
  methods: {
    updateConfig() {
      this.$emit('input', this.config)
      this.$emit('update', this.config)
    },

    getTemplateContent() {
      const templates = {
        greeting: '大家好！今天是{日期}，祝大家工作顺利，生活愉快！',
        sharing: '分享一个有用的信息给大家：{内容}，希望对大家有帮助！',
        notification: '【通知】{时间} - 重要提醒：{内容}',
        marketing: '🔥限时优惠🔥 {产品名称} 现在下单立享优惠，详情咨询！'
      }
      return templates[this.config.messageTemplate] || ''
    },

    // 处理任务停止事件
    handleTaskStopped(data) {
      console.log('[GroupMessageConfig] 收到任务停止事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = (functionType === 'groupMessage' || functionType === 'all') && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[GroupMessageConfig] 循环群发任务停止，原因: ${reason}`)

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('循环群发功能已被批量停止')
        }
      }
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[GroupMessageConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'groupMessage',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      console.log('[GroupMessageConfig] 设备离线处理完成')
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [GroupMessageConfig] 收到实时状态数据:', data)
      console.log('📋 [GroupMessageConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [GroupMessageConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [GroupMessageConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [GroupMessageConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.sentMessageCount !== undefined) {
          this.sentMessageCount = data.sentMessageCount
          console.log('📊 [GroupMessageConfig] 更新已发送消息数:', this.sentMessageCount)
        }
        if (data.processedControlCount !== undefined) {
          this.processedControlCount = data.processedControlCount
          console.log('📊 [GroupMessageConfig] 更新已处理控件数:', this.processedControlCount)
        }
        if (data.executionCount !== undefined) {
          this.executionCount = data.executionCount
          console.log('📊 [GroupMessageConfig] 更新执行次数:', this.executionCount)
        }
        if (data.loopCount !== undefined) {
          this.loopCount = data.loopCount
          console.log('📊 [GroupMessageConfig] 更新循环次数:', this.loopCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [GroupMessageConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [GroupMessageConfig] 实时状态已更新:', {
          sentMessageCount: this.sentMessageCount,
          processedControlCount: this.processedControlCount,
          executionCount: this.executionCount,
          loopCount: this.loopCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [GroupMessageConfig] 已强制更新视图')
      } else {
        console.log('❌ [GroupMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.sentMessageCount = 0
      this.processedControlCount = 0
      this.executionCount = 0
      this.loopCount = 0
      this.currentStatus = '等待开始'
      console.log('[GroupMessageConfig] 实时状态已重置')
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      console.log('[GroupMessageConfig] 收到任务开始事件:', data)
      if (data.functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[GroupMessageConfig] 循环群发任务开始，更新状态')

        // 保存logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[GroupMessageConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[GroupMessageConfig] 保存taskId:', this.currentTaskId)
        }

        // 重置实时状态
        this.resetRealtimeStatus()

        console.log('[GroupMessageConfig] 状态已更新:', {
          isScriptRunning: true,
          isScriptCompleted: false,
          currentLogId: this.currentLogId,
          currentTaskId: this.currentTaskId
        })
      } else {
        console.log('[GroupMessageConfig] 任务开始事件不匹配，忽略')
      }
    },

    // 初始化Socket连接
    initializeSocket() {
      // 使用工具函数获取服务器地址
      const { getWebSocketUrl } = require('@/utils/serverConfig')
      const serverUrl = getWebSocketUrl()
      console.log('🔧 [GroupMessageConfig] 连接到WebSocket服务器:', serverUrl)

      // 只使用WebSocket传输，不使用长轮询
      this.socket = io(serverUrl, {
        transports: ['websocket'], // 只使用WebSocket，不降级到polling
        upgrade: false // 禁用传输升级
      })

      this.socket.on('connect', () => {
        console.log('✅ [GroupMessageConfig] Socket连接成功')
        // 不需要重复注册，主WebSocket管理器已经处理了用户认证
        // this.socket.emit('web_client_connect', { userId: 'group_message_config' })
      })

      this.socket.on('disconnect', () => {
        console.log('❌ [GroupMessageConfig] Socket连接断开')
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [GroupMessageConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      // 监听脚本执行完成事件
      this.socket.on('xiaohongshu_execution_completed', (data) => {
        console.log('[GroupMessageConfig] 收到WebSocket脚本执行完成事件:', data)
        if (data.deviceId === this.deviceId || !this.deviceId) {
          console.log('[GroupMessageConfig] 脚本执行完成，更新状态')

          // 重置实时状态
          this.resetRealtimeStatus()

          // 清除任务ID
          this.currentTaskId = null
          this.currentLogId = null

          // 如果执行成功，1分钟后重置完成状态
          if (data.status === 'success') {
            setTimeout(() => {
              console.log('[GroupMessageConfig] 1分钟后重置完成状态')
            }, 60000)
          }
        }
      })

      console.log('✅ [GroupMessageConfig] WebSocket事件监听已设置')
    }
  },

  mounted() {
    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', this.handleTaskStopped)

    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', this.handleTaskStarted)

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      if (data.deviceId === this.deviceId) {
        console.log('[GroupMessageConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })

    // 初始化Socket连接并监听实时状态
    this.initializeSocket()
  },

  beforeDestroy() {
    // 清理事件监听
    this.$root.$off('xiaohongshu-task-stopped', this.handleTaskStopped)
    this.$root.$off('xiaohongshu-task-started', this.handleTaskStarted)
    this.$root.$off('device-offline')

    // 断开Socket连接
    if (this.socket) {
      this.socket.disconnect()
      console.log('[GroupMessageConfig] Socket连接已断开')
    }
  }
}
</script>

<style scoped>
.group-message-config {
  padding: 10px 0;
}
</style>
