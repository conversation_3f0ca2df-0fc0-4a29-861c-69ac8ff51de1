<template>
  <div class="login-container full-height flex-center">
    <el-card class="login-card" shadow="hover">
      <div slot="header" class="text-center">
        <h2>Auto.js云群控系统</h2>
        <p class="subtitle">基于主站账号的安全登录</p>
      </div>

      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        label-width="100px"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入主站用户名"
            prefix-icon="el-icon-user"
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入主站密码"
            prefix-icon="el-icon-lock"
            show-password
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>

        <!-- 卡密输入框（条件显示） -->
        <el-form-item
          v-if="showActivationCode"
          label="卡密/激活码"
          prop="activationCode"
        >
          <el-input
            v-model="loginForm.activationCode"
            placeholder="请输入卡密或激活码"
            prefix-icon="el-icon-key"
            @keyup.enter.native="handleLogin"
          />
          <div class="activation-hint">
            <el-tag v-if="activationHint" size="mini" type="info">
              {{ activationHint }}
            </el-tag>
          </div>
        </el-form-item>



        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            style="width: 100%"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 账号状态提示 -->
      <div v-if="accountStatus" class="account-status">
        <el-alert
          :title="accountStatus.title"
          :description="accountStatus.description"
          :type="accountStatus.type"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 功能说明 -->
      <div class="login-footer">
        <el-divider>系统说明</el-divider>
        <ul class="feature-list">
          <li>✅ 使用主站账号进行身份验证</li>
          <li>👤 支持真实姓名、昵称或手机号登录</li>
          <li>🔑 首次登录需要提供卡密或激活码</li>
          <li>⏰ 账号具有时效性，过期需要续期</li>
          <li>🔒 多用户数据完全隔离</li>
        </ul>
        <p class="fallback-info">
          💡 如主站不可用，系统将自动切换到本地认证模式
        </p>
      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      showActivationCode: false,
      activationHint: '',
      accountStatus: null,
      loginForm: {
        username: '',
        password: '',
        activationCode: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名（真实姓名/昵称/手机号）', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        activationCode: [
          {
            required: false,
            message: '请输入卡密或激活码',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.showActivationCode && !value) {
                callback(new Error('请输入卡密或激活码'));
              } else {
                callback();
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    async handleLogin() {
      try {
        await this.$refs.loginForm.validate()
        this.loading = true
        this.accountStatus = null

        // 直接调用登录API
        const response = await axios.post('/api/auth/login', this.loginForm)
        const result = response.data

        if (result.success) {
          // 保存token到localStorage
          localStorage.setItem('token', result.token)

          // 保存用户信息
          this.$store.commit('auth/SET_USER', result.user)
          this.$store.commit('auth/SET_TOKEN', result.token)

          // 根据登录类型显示不同消息
          if (result.isFirstLogin) {
            this.$message.success('首次登录成功，账号已激活！')
          } else if (result.isRenewal) {
            this.$message.success('账号续期成功！')
          } else {
            this.$message.success('登录成功')
          }

          // 延迟跳转
          setTimeout(() => {
            this.$router.push('/dashboard')
          }, 500)
        }

      } catch (error) {
        console.error('登录失败:', error)
        const errorData = error.response?.data

        if (errorData?.requireActivation) {
          // 首次登录需要激活码
          this.showActivationCode = true
          this.activationHint = '首次登录需要卡密或激活码'
          this.accountStatus = {
            title: '首次登录验证',
            description: '检测到您是首次登录，请输入有效的卡密或激活码以激活账号使用权限',
            type: 'info'
          }
        } else if (errorData?.requireRenewal) {
          // 账号过期需要续期
          this.showActivationCode = true
          this.activationHint = '账号已过期，需要续期'
          this.accountStatus = {
            title: '账号已过期',
            description: `您的账号已于 ${new Date(errorData.expiresAt).toLocaleString()} 过期，请输入卡密或激活码进行续期`,
            type: 'warning'
          }
        } else {
          // 其他错误
          this.$message.error(errorData?.message || '登录失败')

          // 如果是验证失败，清除激活码相关状态
          if (errorData?.message?.includes('验证失败') || errorData?.message?.includes('无效')) {
            this.showActivationCode = false
            this.activationHint = ''
            this.accountStatus = null
            this.loginForm.activationCode = ''
          }
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 480px;
  border-radius: 10px;
}

.subtitle {
  color: #666;
  font-size: 14px;
  margin: 0;
}



.activation-hint {
  margin-top: 5px;
}

.account-status {
  margin-top: 20px;
}

.login-footer {
  margin-top: 20px;
  color: #666;
  font-size: 12px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 10px 0;
  text-align: left;
}

.feature-list li {
  padding: 2px 0;
  font-size: 12px;
}

.fallback-info {
  margin-top: 10px;
  font-size: 11px;
  color: #999;
  font-style: italic;
}
</style>
