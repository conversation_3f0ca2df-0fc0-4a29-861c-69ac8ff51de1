/**
 * 服务器额外API模块 - 完整拆分版本
 * 包含所有剩余的API和功能
 * 对应原始文件中其他未拆分的API，包含以下功能：
 * - 脚本状态上报API
 * - 应用状态上报API
 * - 设备脚本完成确认API
 * - 清理聊天记录响应API
 * - 节流日志测试API
 * - 其他辅助API
 * 以及所有相关的额外功能
 */

// 额外API模块设置函数
async function setupServerAdditionalApis(app, io, coreData, authData) {
  console.log('🔧 设置额外API模块...');
  
  const { 
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 节流日志测试API (原始文件第322行)
  app.get('/api/test/throttle-log', (req, res) => {
    const testKey = 'test_throttle';
    const message = `测试节流日志 - ${new Date().toLocaleTimeString()}`;

    const logged = throttledLog(testKey, message);

    res.json({
      success: true,
      message: logged ? '日志已输出' : '日志被节流限制',
      logged: logged,
      throttleInterval: 5 * 60 * 1000 / 1000 + '秒', // 5分钟转换为秒
      nextAllowedTime: logged ? 
        new Date(Date.now() + 5 * 60 * 1000).toLocaleTimeString() :
        '立即'
    });
  });

  // 接收设备脚本状态API (原始文件第12625行) - 连接码模式，无需token
  app.post('/api/device/:deviceId/script-status', async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;

    console.log(`🛑 [脚本状态] 收到设备 ${deviceId} 的脚本状态: ${status}`);
    console.log(`🛑 [脚本状态] 原因: ${reason}`);
    console.log(`🛑 [脚本状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备脚本状态变化
      io.emit('device_script_status', {
        deviceId: deviceId,
        status: status,
        reason: reason,
        message: message,
        timestamp: new Date().toISOString()
      });

      console.log(`🛑 [脚本状态] 已通知前端设备 ${deviceId} 脚本状态: ${status}`);

      res.json({
        success: true,
        message: '脚本状态已接收'
      });

    } catch (error) {
      console.error(`🛑 [脚本状态] 处理失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理脚本状态失败: ' + error.message
      });
    }
  });

  // 接收设备应用状态API (原始文件第12660行) - 连接码模式，无需token
  app.post('/api/device/:deviceId/app-status', async (req, res) => {
    const { deviceId } = req.params;
    const { status, reason, message } = req.body;

    console.log(`📱 [应用状态] 收到设备 ${deviceId} 的应用状态: ${status}`);
    console.log(`📱 [应用状态] 原因: ${reason}`);
    console.log(`📱 [应用状态] 消息: ${message}`);

    try {
      // 通知所有Web客户端设备应用状态变化
      io.emit('device_app_status', {
        deviceId: deviceId,
        status: status,
        reason: reason,
        message: message,
        timestamp: new Date().toISOString()
      });

      console.log(`📱 [应用状态] 已通知前端设备 ${deviceId} 应用状态: ${status}`);

      res.json({
        success: true,
        message: '应用状态已接收'
      });

    } catch (error) {
      console.error(`📱 [应用状态] 处理失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理应用状态失败: ' + error.message
      });
    }
  });

  // 清理聊天记录响应API (原始文件第11244行)
  app.post('/api/device/clear-chat-records-response', (req, res) => {
    try {
      const { deviceId, success, message } = req.body;

      console.log(`📱 [清理聊天记录] 设备 ${deviceId} 响应: ${success ? '成功' : '失败'}`);
      if (message) {
        console.log(`📱 [清理聊天记录] 消息: ${message}`);
      }

      // 通知前端清理结果
      io.emit('chat_records_clear_result', {
        deviceId,
        success,
        message,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '清理聊天记录响应已接收'
      });

    } catch (error) {
      console.error('处理清理聊天记录响应失败:', error);
      res.status(500).json({
        success: false,
        message: '处理响应失败: ' + error.message
      });
    }
  });

  // 设备脚本完成确认API (原始文件第11272行)
  app.post('/api/device/script-completion-ack', (req, res) => {
    try {
      const { deviceId, taskId, message, hasRunningScript } = req.body;

      console.log(`📱 [脚本完成确认] 设备 ${deviceId} 确认脚本完成`);
      console.log(`📱 [脚本完成确认] 任务ID: ${taskId}`);
      console.log(`📱 [脚本完成确认] 消息: ${message}`);
      console.log(`📱 [脚本完成确认] 是否有运行中脚本: ${hasRunningScript}`);

      // 更新设备状态
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          device.status = hasRunningScript ? 'busy' : 'online';
          device.lastSeen = new Date();
          devices.set(socketId, device);
          break;
        }
      }

      // 通知前端脚本完成确认
      io.emit('script_completion_ack', {
        deviceId,
        taskId,
        message,
        hasRunningScript,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '脚本完成确认已接收'
      });

    } catch (error) {
      console.error('处理脚本完成确认失败:', error);
      res.status(500).json({
        success: false,
        message: '处理确认失败: ' + error.message
      });
    }
  });

  console.log('✅ 额外API模块设置完成');

  // 返回额外API相关函数供其他模块使用
  return {
    // 可以在这里返回一些额外API相关的工具函数
  };
}

module.exports = { setupServerAdditionalApis };
