<template>
  <div class="user-management-container">
    <el-card class="page-header" shadow="never">
      <div slot="header" class="clearfix">
        <span class="page-title">用户管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="refreshData"
        >
          刷新
        </el-button>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-row">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ stats.users?.total_users || 0 }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ stats.users?.active_users || 0 }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ stats.users?.expired_users || 0 }}</div>
              <div class="stat-label">过期用户</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ stats.devices?.total_devices || 0 }}</div>
              <div class="stat-label">总设备数</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ stats.devices?.online_devices || 0 }}</div>
              <div class="stat-label">在线设备</div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-item">
              <div class="stat-value">{{ (stats.executions?.total_xiaohongshu_executions || 0) + (stats.executions?.total_xianyu_executions || 0) }}</div>
              <div class="stat-label">总执行次数</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="list-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>用户列表</span>
        <div style="float: right">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索用户名或邮箱"
            style="width: 200px; margin-right: 10px"
            prefix-icon="el-icon-search"
            @keyup.enter.native="loadUsers"
          />
          <el-select v-model="searchForm.status" placeholder="状态" style="width: 120px; margin-right: 10px" @change="loadUsers">
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="过期" value="expired" />
            <el-option label="禁用" value="disabled" />
          </el-select>
          <el-select v-model="searchForm.role" placeholder="角色" style="width: 120px; margin-right: 10px" @change="loadUsers">
            <el-option label="全部" value="" />
            <el-option label="用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
          <el-button type="primary" @click="loadUsers">搜索</el-button>
        </div>
      </div>
      
      <el-table 
        :data="users" 
        v-loading="loading"
        style="width: 100%"
        :default-sort="{prop: 'created_at', order: 'descending'}"
      >
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />
        
        <el-table-column prop="role" label="角色" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.role === 'admin' ? 'danger' : 'primary'" size="small">
              {{ scope.row.role === 'admin' ? '管理员' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="账号状态" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getAccountStatusType(scope.row)"
              size="small"
            >
              {{ getAccountStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="时效信息" width="150">
          <template slot-scope="scope">
            <div v-if="scope.row.expires_at">
              <div style="font-size: 12px">
                {{ formatDate(scope.row.expires_at) }}
              </div>
              <div :style="{ color: scope.row.isExpired ? '#f56c6c' : scope.row.daysRemaining <= 7 ? '#e6a23c' : '#67c23a' }">
                {{ scope.row.isExpired ? '已过期' : `剩余${scope.row.daysRemaining}天` }}
              </div>
            </div>
            <span v-else>无限制</span>
          </template>
        </el-table-column>
        
        <el-table-column label="设备统计" width="120">
          <template slot-scope="scope">
            <div style="font-size: 12px">
              <div>总数: {{ scope.row.deviceStats?.total_devices || 0 }}</div>
              <div>在线: {{ scope.row.deviceStats?.online_devices || 0 }}</div>
              <div>忙碌: {{ scope.row.deviceStats?.busy_devices || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="执行统计" width="120">
          <template slot-scope="scope">
            <div style="font-size: 12px">
              <div>小红书: {{ scope.row.execStats?.xiaohongshu_executions || 0 }}</div>
              <div>闲鱼: {{ scope.row.execStats?.xianyu_executions || 0 }}</div>
              <div>总计: {{ scope.row.execStats?.total_executions || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="文件统计" width="120">
          <template slot-scope="scope">
            <div style="font-size: 12px">
              <div>文件数: {{ scope.row.fileStats?.total_files || 0 }}</div>
              <div>大小: {{ formatFileSize(scope.row.fileStats?.total_file_size || 0) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="注册时间" width="150">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="last_login_time" label="最后登录" width="150">
          <template slot-scope="scope">
            {{ scope.row.last_login_time ? formatDate(scope.row.last_login_time) : '从未登录' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="viewUserDetail(scope.row)">详情</el-button>
            
            <el-dropdown @command="handleUserAction" trigger="click">
              <el-button size="mini" type="primary">
                管理<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{action: 'toggleStatus', user: scope.row}">
                  {{ scope.row.account_status === 'active' ? '禁用账号' : '启用账号' }}
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'toggleRole', user: scope.row}" v-if="scope.row.role !== 'admin'">
                  设为管理员
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'extend', user: scope.row}">
                  延长时效
                </el-dropdown-item>
                <el-dropdown-item :command="{action: 'delete', user: scope.row}" v-if="scope.row.role !== 'admin'">
                  <span style="color: #f56c6c">删除用户</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详情"
      :visible.sync="showUserDetail"
      width="80%"
      :close-on-click-modal="false"
    >
      <div v-if="selectedUser">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户名">{{ selectedUser.user?.username }}</el-descriptions-item>
              <el-descriptions-item label="邮箱">{{ selectedUser.user?.email || '未设置' }}</el-descriptions-item>
              <el-descriptions-item label="角色">
                <el-tag :type="selectedUser.user?.role === 'admin' ? 'danger' : 'primary'">
                  {{ selectedUser.user?.role === 'admin' ? '管理员' : '用户' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="账号状态">
                <el-tag :type="getAccountStatusType(selectedUser.user)">
                  {{ getAccountStatusText(selectedUser.user) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="注册时间">{{ formatDate(selectedUser.user?.created_at) }}</el-descriptions-item>
              <el-descriptions-item label="最后登录">{{ selectedUser.user?.last_login_time ? formatDate(selectedUser.user?.last_login_time) : '从未登录' }}</el-descriptions-item>
              <el-descriptions-item label="登录次数">{{ selectedUser.user?.login_count || 0 }}</el-descriptions-item>
              <el-descriptions-item label="激活次数">{{ selectedUser.user?.activation_count || 0 }}</el-descriptions-item>
              <el-descriptions-item label="账号过期时间">{{ selectedUser.user?.expires_at ? formatDate(selectedUser.user?.expires_at) : '无限制' }}</el-descriptions-item>
              <el-descriptions-item label="总激活天数">{{ selectedUser.user?.total_duration_days || 0 }}天</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <el-tab-pane label="设备列表" name="devices">
            <el-table :data="selectedUser.devices" style="width: 100%">
              <el-table-column prop="device_name" label="设备名称" />
              <el-table-column prop="device_id" label="设备ID" />
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === 'online' ? 'success' : scope.row.status === 'busy' ? 'warning' : 'info'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="last_seen" label="最后活动">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.last_seen) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="激活记录" name="activations">
            <el-table :data="selectedUser.activations" style="width: 100%">
              <el-table-column prop="activation_code" label="激活码" />
              <el-table-column prop="activation_type" label="类型">
                <template slot-scope="scope">
                  <el-tag size="small">
                    {{ scope.row.activation_type === 'first_login' ? '首次登录' : scope.row.activation_type === 'renewal' ? '续期' : '试用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="duration_days" label="激活天数" />
              <el-table-column prop="activated_at" label="激活时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.activated_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="expires_at" label="过期时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.expires_at) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="执行记录" name="executions">
            <el-table :data="selectedUser.recentExecutions" style="width: 100%">
              <el-table-column prop="platform" label="平台">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.platform === 'xiaohongshu' ? 'danger' : 'warning'">
                    {{ scope.row.platform === 'xiaohongshu' ? '小红书' : '闲鱼' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="function_type" label="功能类型" />
              <el-table-column prop="execution_status" label="执行状态">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.execution_status === 'completed' ? 'success' : scope.row.execution_status === 'failed' ? 'danger' : 'warning'">
                    {{ scope.row.execution_status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="device_id" label="设备ID" />
              <el-table-column prop="started_at" label="开始时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.started_at) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="文件列表" name="files">
            <el-table :data="selectedUser.recentFiles" style="width: 100%">
              <el-table-column prop="filename" label="文件名" />
              <el-table-column prop="file_type" label="文件类型" />
              <el-table-column prop="file_size" label="文件大小">
                <template slot-scope="scope">
                  {{ formatFileSize(scope.row.file_size) }}
                </template>
              </el-table-column>
              <el-table-column prop="upload_time" label="上传时间">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.upload_time) }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showUserDetail = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 延长时效对话框 -->
    <el-dialog
      title="延长用户时效"
      :visible.sync="showExtendDialog"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form :model="extendForm" :rules="extendRules" ref="extendForm" label-width="100px">
        <el-form-item label="用户名">
          <span>{{ extendUser?.username }}</span>
        </el-form-item>
        <el-form-item label="当前过期时间">
          <span>{{ extendUser?.expires_at ? formatDate(extendUser.expires_at) : '无限制' }}</span>
        </el-form-item>
        <el-form-item label="延长天数" prop="days">
          <el-input-number 
            v-model="extendForm.days" 
            :min="1" 
            :max="3650" 
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showExtendDialog = false">取消</el-button>
        <el-button type="primary" :loading="extending" @click="confirmExtend">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'UserManagement',
  data() {
    return {
      loading: false,
      extending: false,
      users: [],
      stats: {},
      showUserDetail: false,
      showExtendDialog: false,
      selectedUser: null,
      extendUser: null,
      activeTab: 'basic',
      
      // 搜索表单
      searchForm: {
        search: '',
        status: '',
        role: ''
      },
      
      // 分页
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      
      // 延长时效表单
      extendForm: {
        days: 30
      },
      
      extendRules: {
        days: [
          { required: true, message: '请输入延长天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 3650, message: '天数必须在1-3650之间', trigger: 'blur' }
        ]
      }
    }
  },
  
  mounted() {
    this.loadStats()
    this.loadUsers()
  },
  
  methods: {
    async loadStats() {
      try {
        const response = await axios.get('/api/admin/users/stats', {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.stats = response.data.data
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    
    async loadUsers() {
      try {
        this.loading = true
        
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          ...this.searchForm
        }
        
        const response = await axios.get('/api/admin/users', {
          params,
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.users = response.data.data.users
          this.pagination = response.data.data.pagination
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    async viewUserDetail(user) {
      try {
        const response = await axios.get(`/api/admin/users/${user.id}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.selectedUser = response.data.data
          this.showUserDetail = true
          this.activeTab = 'basic'
        }
      } catch (error) {
        console.error('加载用户详情失败:', error)
        this.$message.error('加载用户详情失败')
      }
    },
    
    async handleUserAction(command) {
      const { action, user } = command
      
      switch (action) {
        case 'toggleStatus':
          await this.toggleUserStatus(user)
          break
        case 'toggleRole':
          await this.toggleUserRole(user)
          break
        case 'extend':
          this.showExtendUserDialog(user)
          break
        case 'delete':
          await this.deleteUser(user)
          break
      }
    },
    
    async toggleUserStatus(user) {
      try {
        const newStatus = user.account_status === 'active' ? 'disabled' : 'active'
        
        await this.$confirm(`确定要${newStatus === 'active' ? '启用' : '禁用'}用户 ${user.username} 吗？`, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await axios.put(`/api/admin/users/${user.id}/status`, 
          { status: newStatus },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        )
        
        this.$message.success('用户状态更新成功')
        this.loadUsers()
        this.loadStats()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更新用户状态失败:', error)
          this.$message.error('更新用户状态失败')
        }
      }
    },
    
    async toggleUserRole(user) {
      try {
        await this.$confirm(`确定要将用户 ${user.username} 设为管理员吗？`, '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await axios.put(`/api/admin/users/${user.id}/role`, 
          { role: 'admin' },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        )
        
        this.$message.success('用户角色更新成功')
        this.loadUsers()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('更新用户角色失败:', error)
          this.$message.error('更新用户角色失败')
        }
      }
    },
    
    showExtendUserDialog(user) {
      this.extendUser = user
      this.extendForm.days = 30
      this.showExtendDialog = true
    },
    
    async confirmExtend() {
      try {
        await this.$refs.extendForm.validate()
        this.extending = true
        
        await axios.put(`/api/admin/users/${this.extendUser.id}/extend`, 
          this.extendForm,
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        )
        
        this.$message.success('用户时效延长成功')
        this.showExtendDialog = false
        this.loadUsers()
        this.loadStats()
      } catch (error) {
        console.error('延长用户时效失败:', error)
        this.$message.error(error.response?.data?.message || '延长用户时效失败')
      } finally {
        this.extending = false
      }
    },
    
    async deleteUser(user) {
      try {
        await this.$confirm(`确定要删除用户 ${user.username} 吗？删除后无法恢复，该用户的所有数据都将被删除。`, '确认删除', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error'
        })
        
        await axios.delete(`/api/admin/users/${user.id}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        this.$message.success('用户删除成功')
        this.loadUsers()
        this.loadStats()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          this.$message.error(error.response?.data?.message || '删除用户失败')
        }
      }
    },
    
    refreshData() {
      this.loadStats()
      this.loadUsers()
    },
    
    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.loadUsers()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadUsers()
    },
    
    getAccountStatusType(user) {
      if (!user) return 'info'
      if (user.isExpired || user.account_status === 'expired') return 'danger'
      if (user.account_status === 'active') return 'success'
      if (user.account_status === 'disabled') return 'warning'
      return 'info'
    },
    
    getAccountStatusText(user) {
      if (!user) return '未知'
      if (user.isExpired) return '已过期'
      if (user.account_status === 'active') return '正常'
      if (user.account_status === 'disabled') return '已禁用'
      if (user.account_status === 'expired') return '已过期'
      return user.account_status
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    },
    
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.user-management-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
}

.stats-row {
  margin-top: 10px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
