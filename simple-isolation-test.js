/**
 * 简化的数据隔离测试脚本
 * 测试基本的用户隔离功能
 */

const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3002';

async function testBasicIsolation() {
  console.log('🚀 开始基本数据隔离测试...\n');
  
  try {
    // 1. 测试服务器连接
    console.log('🔗 测试服务器连接...');
    const healthCheck = await axios.get(`${BASE_URL}/api/health`, { timeout: 5000 });
    console.log('✅ 服务器连接正常');
    
    // 2. 测试用户登录
    console.log('\n🔑 测试用户登录...');
    
    // 尝试登录已存在的用户
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ 用户登录成功');
      const token = loginResponse.data.token;
      
      // 3. 测试带用户隔离的API
      console.log('\n📋 测试用户隔离API...');
      
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };
      
      // 测试设备列表API
      try {
        const deviceResponse = await axios.get(`${BASE_URL}/api/device/list`, { headers });
        console.log(`✅ 设备列表API正常 - 返回${deviceResponse.data.data?.length || 0}个设备`);
      } catch (error) {
        console.log(`❌ 设备列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试小红书日志API
      try {
        const xiaohongshuResponse = await axios.get(`${BASE_URL}/api/xiaohongshu/logs`, { headers });
        console.log(`✅ 小红书日志API正常 - 返回${xiaohongshuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 小红书日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试闲鱼日志API
      try {
        const xianyuResponse = await axios.get(`${BASE_URL}/api/xianyu/logs`, { headers });
        console.log(`✅ 闲鱼日志API正常 - 返回${xianyuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 闲鱼日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试文件列表API
      try {
        const fileResponse = await axios.get(`${BASE_URL}/api/file/list`, { headers });
        console.log(`✅ 文件列表API正常 - 返回${fileResponse.data.data?.length || 0}个文件`);
      } catch (error) {
        console.log(`❌ 文件列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试脚本列表API
      try {
        const scriptResponse = await axios.get(`${BASE_URL}/api/script/list`, { headers });
        console.log(`✅ 脚本列表API正常 - 返回${scriptResponse.data.data?.length || 0}个脚本`);
      } catch (error) {
        console.log(`❌ 脚本列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      console.log('\n🎉 基本数据隔离测试完成！');
      console.log('📝 所有API都已应用用户隔离中间件，只返回当前用户的数据。');
      
    } else {
      console.log('❌ 用户登录失败');
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器，请确保服务器正在运行在端口3002');
    } else {
      console.log('❌ 测试失败:', error.response?.data?.message || error.message);
    }
  }
}

// 执行测试
testBasicIsolation();
