<template>
  <div class="video-file-manager">
    <el-card>
      <div slot="header" class="card-header">
        <span>视频文件管理</span>
        <div class="header-actions">
          <el-button
            type="primary"
            icon="el-icon-view"
            size="small"
            @click="showVideoListDialog"
          >
            查看视频列表
          </el-button>
          <el-button
            type="success"
            icon="el-icon-share"
            size="small"
            @click="showVideoAssignDialog"
          >
            视频分配
          </el-button>
        </div>
      </div>

      <!-- 视频上传区域 -->
      <div class="upload-section">
        <div class="upload-buttons">
          <!-- 文件上传 -->
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            accept="video/*"
            :show-file-list="false"
            :disabled="uploading"
            multiple
            name="videos"
          >
            <el-button size="small" type="primary" icon="el-icon-upload" :loading="uploading">
              {{ uploading ? '上传中...' : '选择视频文件' }}
            </el-button>
          </el-upload>

          <!-- 文件夹上传 -->
          <div class="folder-upload">
            <input
              ref="folderInput"
              type="file"
              webkitdirectory
              directory
              multiple
              accept="video/*"
              style="display: none"
              @change="handleFolderSelect"
              :disabled="uploading"
            />
            <el-button
              size="small"
              type="success"
              icon="el-icon-folder-add"
              @click="selectFolder"
              :disabled="uploading"
            >
              选择视频文件夹
            </el-button>
          </div>
        </div>

        <div class="upload-tips">
          <div class="tip-item">
            <i class="el-icon-document"></i>
            <span>支持 MP4、AVI、MOV、WMV、FLV、MKV 等格式，单个文件不超过2GB</span>
          </div>
          <div class="tip-item">
            <i class="el-icon-folder"></i>
            <span>选择文件夹可批量上传文件夹内所有视频文件，最多1000个文件</span>
          </div>
          <div class="tip-item warning">
            <i class="el-icon-warning"></i>
            <span>系统会自动检测重复文件并跳过</span>
          </div>
        </div>

        <!-- 文件夹选择预览 -->
        <div v-if="selectedFolder" class="folder-preview">
          <div class="folder-info">
            <h4>
              <i class="el-icon-folder-opened"></i>
              已选择文件夹: {{ selectedFolder.name }}
            </h4>
            <p>找到 {{ selectedFolder.videoFiles.length }} 个视频文件</p>
          </div>

          <div class="file-list" v-if="selectedFolder.videoFiles.length > 0">
            <div class="file-list-header">
              <span>视频文件列表:</span>
              <el-button
                size="mini"
                type="primary"
                @click="uploadFolderVideos"
                :disabled="uploading"
              >
                上传这些文件
              </el-button>
            </div>
            <div class="file-items">
              <div
                v-for="(file, index) in selectedFolder.videoFiles.slice(0, 10)"
                :key="index"
                class="file-item"
              >
                <i class="el-icon-video-camera"></i>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
              </div>
              <div v-if="selectedFolder.videoFiles.length > 10" class="more-files">
                还有 {{ selectedFolder.videoFiles.length - 10 }} 个文件...
              </div>
            </div>
          </div>

          <div v-else class="no-videos">
            <i class="el-icon-warning"></i>
            <span>该文件夹中没有找到视频文件</span>
          </div>
        </div>

        <!-- 上传进度 -->
        <div v-if="uploading" class="upload-progress">
          <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
          <p>{{ uploadMessage }}</p>
          <div v-if="uploadDetails.length > 0" class="upload-details">
            <div class="detail-header">上传详情:</div>
            <div class="detail-items">
              <div
                v-for="(detail, index) in uploadDetails.slice(-5)"
                :key="index"
                class="detail-item"
                :class="detail.status"
              >
                <i :class="getStatusIcon(detail.status)"></i>
                <span>{{ detail.fileName }}</span>
                <span class="status-text">{{ detail.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传统计信息 -->
      <div class="upload-stats">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ totalVideos }}</div>
              <div class="stat-label">总视频数</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ totalSize }}</div>
              <div class="stat-label">总大小</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ recentUploads }}</div>
              <div class="stat-label">今日上传</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 视频列表弹窗 -->
    <VideoListDialog
      v-model="videoListDialogVisible"
      @videos-uploaded="handleDialogVideosUploaded"
      @show-transfer-info="showVideoTransferInfo"
    />

    <!-- 视频分配对话框 -->
    <el-dialog
      title="视频分配管理"
      :visible.sync="videoAssignDialogVisible"
      width="70%"
      :before-close="handleAssignDialogClose"
    >
      <div class="video-assign-content">
        <!-- 分配配置 -->
        <el-card class="assign-config-card">
          <div slot="header">
            <span>分配配置</span>
          </div>

          <el-form :model="assignForm" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="参考设备数量">
                  <el-input-number
                    v-model="assignForm.deviceCount"
                    :min="1"
                    :max="Math.max(1, Math.min(onlineDevices.length, 20))"
                    placeholder="设备数量"
                    @change="updateDeviceAndVideoCount"
                  />
                  <span style="margin-left: 10px; color: #909399;">在线设备: {{ onlineDevices.length }}个</span>
                </el-form-item>
                <el-alert
                  title="分配说明"
                  type="info"
                  :closable="false"
                  show-icon
                  style="margin-top: 10px;"
                >
                  灵活分配模式：您可以自由选择任意数量的设备和对应数量的视频进行分配，每个设备分配一个视频
                </el-alert>
              </el-col>
            </el-row>

            <el-form-item label="分配策略">
              <el-radio-group v-model="assignForm.strategy">
                <el-radio label="average">平均分配</el-radio>
                <el-radio label="random">随机分配</el-radio>
                <el-radio label="sequential">顺序分配</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>

          <div class="assign-actions">
            <el-button
              type="primary"
              icon="el-icon-check"
              @click="executeVideoAssignment"
              :disabled="!canExecuteAssignment"
              :loading="assignmentLoading"
            >
              {{ assignmentLoading ? '分配中...' : '执行分配' }}
            </el-button>
            <el-button @click="resetAssignForm">重置</el-button>
          </div>
        </el-card>

        <!-- 设备选择 -->
        <el-card class="device-selection-card" v-if="assignForm.deviceCount > 0">
          <div slot="header">
            <div class="selection-header">
              <div class="selection-info">
                <span>选择设备 ({{ selectedDevices.length }}个已选择)</span>
                <el-tooltip content="支持Ctrl+点击多选，Shift+点击范围选择" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399;"></i>
                </el-tooltip>
              </div>
              <div class="selection-actions">
                <el-button
                  size="mini"
                  type="primary"
                  @click="selectAllDevices"
                  :disabled="selectedDevices.length >= onlineDevices.length"
                >
                  全选
                </el-button>
                <el-button
                  size="mini"
                  @click="clearDeviceSelection"
                  :disabled="selectedDevices.length === 0"
                >
                  清空
                </el-button>
              </div>
            </div>
          </div>

          <div class="device-grid">
            <div
              v-for="(device, index) in onlineDevices"
              :key="device.device_id"
              class="device-item"
              :class="{ 'selected': isDeviceSelected(device.device_id), 'disabled': !canSelectDevice(device.device_id) }"
              @click="handleDeviceClick(device.device_id, index, $event)"
            >
              <div class="device-info">
                <div class="device-name" :title="device.device_name">
                  {{ device.device_name || device.device_id }}
                </div>
                <div class="device-details">
                  <el-tag
                    :type="device.status === 'online' ? 'success' : 'warning'"
                    size="mini"
                  >
                    {{ device.status === 'online' ? '在线' : '忙碌' }}
                  </el-tag>
                  <span class="device-ip" :title="getDeviceIP(device)">
                    {{ getDeviceIP(device) }}
                  </span>
                </div>
                <div class="device-extra-info">
                  <span class="device-model" :title="getDeviceModel(device.device_info)">
                    {{ getDeviceModel(device.device_info) }}
                  </span>
                  <span class="device-android" :title="getAndroidVersion(device.device_info)">
                    {{ getAndroidVersion(device.device_info) }}
                  </span>
                </div>
                <div class="device-connection-info">
                  <span class="last-seen">{{ formatLastSeen(device.last_seen) }}</span>
                </div>
              </div>
              <div class="device-checkbox" @click.stop>
                <el-checkbox
                  :value="isDeviceSelected(device.device_id)"
                  :disabled="!canSelectDevice(device.device_id)"
                  @change="toggleDeviceSelection(device.device_id)"
                ></el-checkbox>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 视频选择 -->
        <el-card class="video-selection-card" v-if="assignForm.deviceCount > 0">
          <div slot="header">
            <div class="selection-header">
              <div class="selection-info">
                <span>选择视频 ({{ selectedVideos.length }}/{{ selectedDevices.length || '请先选择设备' }}) - 每设备一视频</span>
                <el-tooltip content="支持Ctrl+点击多选，Shift+点击范围选择。每个设备只能分配一个视频" placement="top">
                  <i class="el-icon-info" style="margin-left: 5px; color: #909399;"></i>
                </el-tooltip>
              </div>
              <div class="selection-actions">
                <el-button
                  size="mini"
                  type="primary"
                  @click="selectAllVideos"
                  :disabled="selectedDevices.length === 0 || selectedVideos.length >= selectedDevices.length"
                >
                  全选
                </el-button>
                <el-button
                  size="mini"
                  @click="clearVideoSelection"
                  :disabled="selectedVideos.length === 0"
                >
                  清空
                </el-button>
                <el-button
                  size="mini"
                  type="success"
                  icon="el-icon-magic-stick"
                  @click="smartSelectVideos"
                  :disabled="selectedDevices.length === 0 || availableVideos.length === 0"
                  :loading="smartSelectionLoading"
                >
                  智能选择
                </el-button>
              </div>
            </div>
          </div>

          <div class="video-grid">
            <div
              v-for="(video, index) in availableVideos"
              :key="video.id"
              class="video-item"
              :class="{ 'selected': isVideoSelected(video.id), 'disabled': !canSelectVideo(video.id) }"
              @click="handleVideoClick(video.id, index, $event)"
            >
              <div class="video-thumbnail">
                <img
                  v-if="video.thumbnail_path"
                  :src="getThumbnailUrl(video.thumbnail_path)"
                  :alt="video.original_name"
                />
                <div v-else class="no-thumbnail">
                  <i class="el-icon-video-camera"></i>
                </div>
              </div>
              <div class="video-info">
                <div class="video-name" :title="video.original_name">{{ video.original_name }}</div>
                <div class="video-details">
                  <span class="video-size">{{ formatFileSize(video.file_size) }}</span>
                  <span class="video-duration" v-if="video.duration">{{ formatDuration(video.duration) }}</span>
                </div>
                <div class="video-extra-info">
                  <el-tag size="mini" type="info">{{ getVideoFormat(video.original_name) }}</el-tag>
                  <span class="upload-time">{{ formatTime(video.created_at) }}</span>
                </div>
                <div class="video-transfer-info">
                  <div class="transfer-stats">
                    <span class="transfer-count">
                      <i class="el-icon-download"></i>
                      传输: {{ video.total_transfer_count || 0 }}次
                    </span>
                    <span class="success-count">
                      <i class="el-icon-check"></i>
                      成功: {{ video.successful_transfer_count || 0 }}次
                    </span>
                  </div>
                  <div v-if="getTransferredDevices(video.transferred_devices).length > 0" class="transferred-devices">
                    <span class="devices-label">已传输设备:</span>
                    <el-tag
                      v-for="device in getTransferredDevices(video.transferred_devices).slice(0, 3)"
                      :key="device.deviceId"
                      size="mini"
                      type="success"
                      :title="`设备: ${device.deviceName || device.deviceId}, 传输时间: ${formatTime(device.transferTime)}`"
                    >
                      {{ getDeviceDisplayName(device) }}
                    </el-tag>
                    <span v-if="getTransferredDevices(video.transferred_devices).length > 3" class="more-devices">
                      +{{ getTransferredDevices(video.transferred_devices).length - 3 }}个设备
                    </span>
                  </div>
                </div>
              </div>
              <div class="video-checkbox" @click.stop>
                <el-checkbox
                  :value="isVideoSelected(video.id)"
                  :disabled="!canSelectVideo(video.id)"
                  @change="toggleVideoSelection(video.id)"
                ></el-checkbox>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 分配预览 -->
        <el-card class="assignment-preview-card" v-if="assignmentPreview.length > 0">
          <div slot="header">
            <span>分配预览</span>
          </div>

          <div class="preview-content">
            <div
              v-for="(assignment, index) in assignmentPreview"
              :key="index"
              class="assignment-item"
              :class="{ 'no-video': assignment.status === 'no_video' }"
            >
              <div class="device-info">
                <strong>{{ assignment.deviceName }}</strong>
                <span class="video-count" v-if="assignment.videos.length > 0">
                  ({{ assignment.videos.length }} 个视频)
                </span>
                <span class="no-video-text" v-else>
                  (未分配视频)
                </span>
              </div>
              <div class="video-list" v-if="assignment.videos.length > 0">
                <el-tag
                  v-for="video in assignment.videos"
                  :key="video.id"
                  size="mini"
                  type="success"
                >
                  {{ video.original_name }}
                </el-tag>
              </div>
              <div class="no-video-placeholder" v-else>
                <el-tag size="mini" type="warning">
                  需要更多视频
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 视频传输信息弹出框 -->
    <el-dialog
      :title="transferInfoDialogTitle"
      :visible.sync="transferInfoDialogVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentVideoTransferInfo">
        <!-- 单个视频传输信息 -->
        <div class="video-transfer-info">
          <div class="video-basic-info">
            <h4>📹 视频基本信息</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">文件名：</span>
                  <span class="info-value">{{ currentVideoTransferInfo.original_name }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">文件大小：</span>
                  <span class="info-value">{{ formatFileSize(currentVideoTransferInfo.file_size) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">视频时长：</span>
                  <span class="info-value">{{ formatDuration(currentVideoTransferInfo.video_duration) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">上传时间：</span>
                  <span class="info-value">{{ formatDateTime(currentVideoTransferInfo.upload_time) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="transfer-statistics">
            <h4>📊 传输统计</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number">{{ transferStatistics.totalTransfers }}</div>
                  <div class="stat-label">总传输次数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number">{{ transferStatistics.uniqueDevices }}</div>
                  <div class="stat-label">传输设备数</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="stat-item">
                  <div class="stat-number">{{ transferStatistics.successRate }}%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="transfer-records">
            <h4>📋 传输记录</h4>
            <el-table
              :data="transferRecords"
              style="width: 100%"
              max-height="300"
              empty-text="暂无数据"
            >
              <el-table-column prop="device_name" label="设备名称" width="120" />
              <el-table-column prop="device_id" label="设备ID" width="180" />
              <el-table-column prop="device_ip" label="设备IP" width="120" />
              <el-table-column label="传输类型" width="100">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.transfer_type === 'manual' ? 'primary' : 'success'" size="mini">
                    {{ scope.row.transfer_type === 'manual' ? '手动传输' : '脚本传输' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="传输状态" width="100">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.status === 'completed' ? 'success' :
                           scope.row.status === 'failed' ? 'danger' : 'warning'"
                    size="mini"
                  >
                    {{ getTransferStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="transfer_progress" label="进度" width="80">
                <template slot-scope="scope">
                  {{ scope.row.transfer_progress || 0 }}%
                </template>
              </el-table-column>
              <el-table-column prop="transfer_speed" label="传输速度" width="120">
                <template slot-scope="scope">
                  <span v-if="scope.row.transfer_speed && scope.row.transfer_speed > 0">
                    {{ formatFileSize(scope.row.transfer_speed * 1024) }}/s
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="transfer_time" label="开始时间" width="160">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.transfer_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="completed_time" label="完成时间" width="160">
                <template slot-scope="scope">
                  <span v-if="scope.row.completed_time">
                    {{ formatDateTime(scope.row.completed_time) }}
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column prop="task_id" label="任务ID" width="120" show-overflow-tooltip />
              <el-table-column prop="error_message" label="错误信息" min-width="200">
                <template slot-scope="scope">
                  <span v-if="scope.row.error_message" style="color: #F56C6C;">
                    {{ scope.row.error_message }}
                  </span>
                  <span v-else style="color: #67C23A;">正常</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import VideoListDialog from './VideoListDialog.vue'
import { getApiBaseUrl } from '@/utils/serverConfig'

export default {
  name: 'VideoFileManager',
  components: {
    VideoListDialog
  },
  data() {
    return {
      uploading: false,
      uploadProgress: 0,
      uploadStatus: '',
      uploadMessage: '',
      uploadDetails: [],
      videoListDialogVisible: false,
      totalVideos: 0,
      totalSize: '0 B',
      recentUploads: 0,
      selectedFolder: null,

      // 视频分配相关
      videoAssignDialogVisible: false,
      assignForm: {
        deviceCount: 1,
        videoCount: 1,
        strategy: 'average'
      },
      onlineDevices: [],
      availableVideos: [],
      selectedDevices: [],
      selectedVideos: [],
      assignmentPreview: [],
      assignmentLoading: false,
      smartSelectionLoading: false,

      // 批量选择相关
      lastSelectedDeviceIndex: -1,
      lastSelectedVideoIndex: -1,

      // 传输信息弹出框相关
      transferInfoDialogVisible: false,
      transferInfoDialogTitle: '',
      currentVideoTransferInfo: null,
      transferRecords: [],
      transferStatistics: {
        totalTransfers: 0,
        uniqueDevices: 0,
        successRate: 0
      }
    }
  },
  computed: {
    uploadUrl() {
      // 使用动态服务器地址
      const { getApiBaseUrl } = require('@/utils/serverConfig')
      return `${getApiBaseUrl()}/api/xiaohongshu/upload-video-files`
    },
    uploadHeaders() {
      const token = this.$store.getters['auth/token']
      return token ? { 'Authorization': `Bearer ${token}` } : {}
    },
    canExecuteAssignment() {
      // 灵活分配：只要有选择的设备和对应数量的视频即可
      return this.selectedDevices.length > 0 &&
             this.selectedVideos.length > 0 &&
             this.selectedVideos.length === this.selectedDevices.length
    }
  },
  mounted() {
    this.loadVideoStats()
  },
  methods: {
    // 获取API基础URL
    getApiBaseUrl() {
      return getApiBaseUrl()
    },

    async loadVideoStats() {
      try {
        const response = await axios.get('/api/xiaohongshu/video-stats')

        if (response.data.success) {
          this.totalVideos = response.data.data.totalVideos
          this.totalSize = this.formatFileSize(response.data.data.totalSize)
          this.recentUploads = response.data.data.todayUploads
        }
      } catch (error) {
        console.error('加载视频统计失败:', error)
        // 设置默认值
        this.totalVideos = 0
        this.totalSize = '0 B'
        this.recentUploads = 0
      }
    },

    showVideoListDialog() {
      this.videoListDialogVisible = true
    },

    handleDialogVideosUploaded(data) {
      // 刷新统计信息
      this.loadVideoStats()
      // 向父组件传递事件
      this.$emit('videos-uploaded', data)
    },

    // 显示单个视频传输信息
    async showVideoTransferInfo(video) {
      try {
        console.log('🎯🎯🎯 [VideoFileManager] ===== 接收到show-transfer-info事件 ===== 🎯🎯🎯')
        console.log('🔍 [VideoFileManager] 显示视频传输信息:', video)
        console.log('🔍 [VideoFileManager] 视频ID:', video.id, '视频名称:', video.original_name)

        // 强制显示弹出框，先设置基本信息
        this.transferInfoDialogVisible = true
        this.transferInfoDialogTitle = `视频传输信息 - ${video.original_name}`
        this.currentVideoTransferInfo = video

        console.log('🎯 [VideoFileManager] 弹出框已强制显示，标题:', this.transferInfoDialogTitle)

        // 获取视频传输记录
        console.log('📡 [VideoFileManager] 请求传输记录:', `/api/videos/${video.id}/transfer-records`)

        // 获取认证token
        const token = localStorage.getItem('token')

        const response = await axios.get(`/api/videos/${video.id}/transfer-records`, {
          headers: {
            ...(token ? { 'Authorization': `Bearer ${token}` } : {})
          }
        })
        console.log('📊 [VideoFileManager] 传输记录响应:', response.data)

        if (response.data.success) {
          this.transferRecords = response.data.records || []
          this.transferStatistics = response.data.statistics || {
            totalTransfers: 0,
            uniqueDevices: 0,
            successRate: 0
          }
          console.log('✅ [VideoFileManager] 传输记录设置完成:', this.transferRecords)
          console.log('📈 [VideoFileManager] 传输统计:', this.transferStatistics)
        } else {
          console.error('❌ [VideoFileManager] API返回失败:', response.data.message)
        }

      } catch (error) {
        console.error('❌ [VideoFileManager] 获取视频传输信息失败:', error)
        this.$message.error('获取传输信息失败: ' + error.message)
      }
    },

    beforeUpload(file) {
      // 检查文件类型
      const isVideo = file.type.startsWith('video/') || this.isVideoFile(file.name)
      if (!isVideo) {
        this.$message.error('只能上传视频文件!')
        return false
      }

      // 检查文件大小
      const isLt2G = file.size / 1024 / 1024 / 1024 < 2
      if (!isLt2G) {
        this.$message.error(`视频文件 "${file.name}" 大小不能超过 2GB! 当前大小: ${this.formatFileSize(file.size)}`)
        return false
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.uploadMessage = '准备上传...'
      return true
    },

    // 检查是否为视频文件
    isVideoFile(fileName) {
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp']
      const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
      return videoExtensions.includes(ext)
    },

    handleUploadSuccess(response, file) {
      this.uploading = false
      if (response.success) {
        let message = `成功上传 ${response.data.uploadCount} 个视频文件`
        if (response.data.duplicateCount > 0) {
          message += `，跳过 ${response.data.duplicateCount} 个重复文件`
        }
        this.$message.success(message)
        this.loadVideoStats()
        this.$emit('videos-uploaded', response.data)
      } else {
        this.$message.error(response.message || '上传失败')
      }
    },

    handleUploadError(error) {
      this.uploading = false
      this.$message.error('上传失败: ' + error.message)
    },

    // 选择文件夹
    selectFolder() {
      this.$refs.folderInput.click()
    },

    // 处理文件夹选择
    handleFolderSelect(event) {
      const files = Array.from(event.target.files)
      if (files.length === 0) {
        return
      }

      // 过滤出视频文件并检查大小
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.m4v', '.3gp']
      const videoFiles = []
      const oversizedFiles = []

      files.forEach(file => {
        const ext = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
        if (videoExtensions.includes(ext)) {
          const fileSizeGB = file.size / 1024 / 1024 / 1024
          if (fileSizeGB < 2) {
            videoFiles.push(file)
          } else {
            oversizedFiles.push({
              name: file.name,
              size: this.formatFileSize(file.size)
            })
          }
        }
      })

      // 提示超大文件
      if (oversizedFiles.length > 0) {
        const oversizedNames = oversizedFiles.map(f => `${f.name} (${f.size})`).join(', ')
        this.$message.warning(`以下文件超过2GB限制，已跳过: ${oversizedNames}`)
      }

      if (videoFiles.length === 0) {
        this.$message.warning('选择的文件夹中没有找到视频文件')
        return
      }

      // 获取文件夹名称（从第一个文件的路径中提取）
      const firstFile = files[0]
      const pathParts = firstFile.webkitRelativePath.split('/')
      const folderName = pathParts[0]

      this.selectedFolder = {
        name: folderName,
        videoFiles: videoFiles,
        totalSize: videoFiles.reduce((sum, file) => sum + file.size, 0)
      }

      this.$message.success(`已选择文件夹 "${folderName}"，找到 ${videoFiles.length} 个视频文件`)
    },

    // 上传文件夹中的视频
    async uploadFolderVideos() {
      if (!this.selectedFolder || this.selectedFolder.videoFiles.length === 0) {
        this.$message.warning('没有可上传的视频文件')
        return
      }

      this.uploading = true
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.uploadMessage = '准备上传文件夹中的视频...'
      this.uploadDetails = []

      try {
        const formData = new FormData()

        // 添加所有视频文件
        this.selectedFolder.videoFiles.forEach(file => {
          formData.append('videos', file)
        })

        // 添加描述信息
        formData.append('description', `从文件夹 "${this.selectedFolder.name}" 批量上传`)
        formData.append('tags', `文件夹上传,${this.selectedFolder.name}`)

        this.uploadMessage = `正在上传 ${this.selectedFolder.videoFiles.length} 个视频文件...`

        const response = await axios.post(this.uploadUrl, formData, {
          headers: {
            ...this.uploadHeaders
            // 不要手动设置Content-Type，让axios自动设置
          },
          timeout: 600000, // 10分钟超时，适合大文件上传
          onUploadProgress: (progressEvent) => {
            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          }
        })

        this.handleUploadSuccess(response.data)

        // 清空文件夹选择
        this.selectedFolder = null
        this.$refs.folderInput.value = ''

      } catch (error) {
        this.handleUploadError(error)
      }
    },

    // 获取状态图标
    getStatusIcon(status) {
      switch (status) {
        case 'success':
          return 'el-icon-success'
        case 'error':
          return 'el-icon-error'
        case 'uploading':
          return 'el-icon-loading'
        default:
          return 'el-icon-info'
      }
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatTime(timeString) {
      if (!timeString) return '-'
      return new Date(timeString).toLocaleString('zh-CN')
    },

    // 视频分配相关方法
    async showVideoAssignDialog() {
      this.videoAssignDialogVisible = true
      await this.loadOnlineDevices()
      await this.loadAvailableVideos()

      // 设置合理的默认值（一对一分配）
      if (this.onlineDevices.length > 0 && this.assignForm.deviceCount === 1) {
        // 默认选择设备数量，但不超过可用视频数量
        const maxDevices = Math.min(this.onlineDevices.length, this.availableVideos.length, 3)
        this.assignForm.deviceCount = Math.max(1, maxDevices)
      }

      console.log('视频分配对话框已打开')
      console.log('在线设备数量:', this.onlineDevices.length)
      console.log('可用视频数量:', this.availableVideos.length)
      console.log('默认设备数量:', this.assignForm.deviceCount)
      console.log('分配模式: 一对一（每个设备分配一个视频）')
    },

    handleAssignDialogClose() {
      this.resetAssignForm()
      this.videoAssignDialogVisible = false
    },

    async loadOnlineDevices() {
      try {
        const response = await axios.get('/api/device/list')
        if (response.data.success) {
          // 将API响应的驼峰命名字段映射为下划线命名字段
          const mappedDevices = response.data.data.map(device => ({
            device_id: device.deviceId,
            device_name: device.deviceName,
            device_info: device.deviceInfo,
            status: device.status,
            last_seen: device.lastActiveTime,
            created_at: device.createdAt,
            ip_address: device.deviceIP || '未知',
            is_connected: device.isConnected
          }))

          // 只显示在线状态的设备，不包括忙碌状态
          this.onlineDevices = mappedDevices.filter(device =>
            device.status === 'online'
          )
          console.log('加载在线设备成功:', this.onlineDevices.length, '个设备')
          if (this.onlineDevices.length > 0) {
            console.log('设备数据结构示例:', this.onlineDevices[0])
            console.log('设备字段:', Object.keys(this.onlineDevices[0]))
          }
        }
      } catch (error) {
        console.error('加载在线设备失败:', error)
        this.$message.error('加载在线设备失败')
      }
    },

    async loadAvailableVideos() {
      try {
        const response = await axios.get('/api/xiaohongshu/video-files', {
          params: { status: 'active', limit: 100 }
        })
        if (response.data.success) {
          this.availableVideos = response.data.data.videos
        }
      } catch (error) {
        console.error('加载可用视频失败:', error)
        this.$message.error('加载可用视频失败')
      }
    },

    updateDeviceAndVideoCount() {
      // 当设备数量改变时，不强制限制选择的设备和视频数量
      // 只是作为一个参考值，实际分配以用户选择为准

      // 如果当前选择的视频数量超过了选择的设备数量，则截取
      if (this.selectedVideos.length > this.selectedDevices.length && this.selectedDevices.length > 0) {
        this.selectedVideos = this.selectedVideos.slice(0, this.selectedDevices.length)
      }

      // 重置批量选择状态
      this.lastSelectedDeviceIndex = -1
      this.lastSelectedVideoIndex = -1

      this.updateAssignmentPreview()

      console.log('设备数量参考值已更新为:', this.assignForm.deviceCount)
      console.log('实际分配将根据用户选择的设备和视频数量进行（灵活分配）')
    },

    isDeviceSelected(deviceId) {
      return this.selectedDevices.includes(deviceId)
    },

    toggleDeviceSelection(deviceId) {
      console.log('toggleDeviceSelection called with:', deviceId)
      console.log('canSelectDevice:', this.canSelectDevice(deviceId))
      console.log('isDeviceSelected:', this.isDeviceSelected(deviceId))
      console.log('selectedDevices.length:', this.selectedDevices.length)
      console.log('assignForm.deviceCount:', this.assignForm.deviceCount)

      if (!this.canSelectDevice(deviceId)) {
        console.log('Cannot select device, returning')
        return
      }

      if (this.isDeviceSelected(deviceId)) {
        console.log('Removing device from selection')
        this.selectedDevices = this.selectedDevices.filter(id => id !== deviceId)
      } else {
        console.log('Adding device to selection')
        this.selectedDevices.push(deviceId)
      }

      console.log('Updated selectedDevices:', this.selectedDevices)
      this.updateAssignmentPreview()
    },

    handleDeviceClick(deviceId, index, event) {
      if (!this.canSelectDevice(deviceId)) return

      if (event.ctrlKey || event.metaKey) {
        // Ctrl+点击：切换单个设备选择状态
        this.toggleDeviceSelection(deviceId)
        this.lastSelectedDeviceIndex = index
      } else if (event.shiftKey && this.lastSelectedDeviceIndex !== -1) {
        // Shift+点击：选择范围内的设备
        this.selectDeviceRange(this.lastSelectedDeviceIndex, index)
      } else {
        // 普通点击：单选
        this.toggleDeviceSelection(deviceId)
        this.lastSelectedDeviceIndex = index
      }
    },

    selectDeviceRange(startIndex, endIndex) {
      const start = Math.min(startIndex, endIndex)
      const end = Math.max(startIndex, endIndex)

      for (let i = start; i <= end; i++) {
        if (i < this.onlineDevices.length) {
          const deviceId = this.onlineDevices[i].device_id
          if (!this.isDeviceSelected(deviceId)) {
            this.selectedDevices.push(deviceId)
          }
        }
      }
      this.updateAssignmentPreview()
    },

    canSelectDevice(deviceId) {
      return true // 允许选择任意数量的设备
    },

    selectAllDevices() {
      // 选择所有在线设备，不受deviceCount限制
      this.selectedDevices = this.onlineDevices.map(device => device.device_id)
      this.updateAssignmentPreview()
    },

    clearDeviceSelection() {
      this.selectedDevices = []
      this.lastSelectedDeviceIndex = -1
      this.updateAssignmentPreview()
    },

    getDeviceIP(device) {
      // 尝试多个可能的IP字段
      return device.ip_address ||
             device.deviceIP ||
             device.device_ip ||
             (device.device_info && typeof device.device_info === 'object' && device.device_info.ipAddress) ||
             (device.device_info && typeof device.device_info === 'object' && device.device_info.ip) ||
             '未知IP'
    },

    getDeviceModel(deviceInfo) {
      if (!deviceInfo) {
        return '未知型号'
      }

      if (typeof deviceInfo === 'string') {
        try {
          deviceInfo = JSON.parse(deviceInfo)
        } catch (e) {
          console.log('设备信息JSON解析失败:', deviceInfo)
          return '未知型号'
        }
      }

      // 尝试多个可能的型号字段
      const model = deviceInfo?.model ||
                   deviceInfo?.brand ||
                   deviceInfo?.manufacturer ||
                   deviceInfo?.device ||
                   deviceInfo?.product ||
                   '未知型号'

      return model
    },

    getAndroidVersion(deviceInfo) {
      if (!deviceInfo) return ''

      if (typeof deviceInfo === 'string') {
        try {
          deviceInfo = JSON.parse(deviceInfo)
        } catch (e) {
          return ''
        }
      }

      // 尝试多个可能的版本字段
      const version = deviceInfo?.release ||
                     deviceInfo?.version ||
                     deviceInfo?.android_version ||
                     deviceInfo?.sdk_int

      if (version) {
        return typeof version === 'number' ? `API ${version}` : `Android ${version}`
      }
      return ''
    },

    formatLastSeen(lastSeen) {
      if (!lastSeen) return '从未连接'

      const now = new Date()
      const lastSeenDate = new Date(lastSeen)
      const diffMs = now - lastSeenDate
      const diffMins = Math.floor(diffMs / (1000 * 60))

      if (diffMins < 1) return '刚刚活跃'
      if (diffMins < 60) return `${diffMins}分钟前`
      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`
      return `${Math.floor(diffMins / 1440)}天前`
    },

    isVideoSelected(videoId) {
      return this.selectedVideos.some(video => video.id === videoId)
    },

    toggleVideoSelection(videoId) {
      console.log('toggleVideoSelection called with:', videoId)
      console.log('canSelectVideo:', this.canSelectVideo(videoId))
      console.log('isVideoSelected:', this.isVideoSelected(videoId))
      console.log('selectedVideos.length:', this.selectedVideos.length)
      console.log('assignForm.videoCount:', this.assignForm.videoCount)

      if (!this.canSelectVideo(videoId)) {
        console.log('Cannot select video, returning')
        return
      }

      const video = this.availableVideos.find(v => v.id === videoId)
      if (!video) {
        console.log('Video not found:', videoId)
        return
      }

      if (this.isVideoSelected(videoId)) {
        console.log('Removing video from selection')
        this.selectedVideos = this.selectedVideos.filter(v => v.id !== videoId)
      } else if (this.selectedVideos.length < this.selectedDevices.length) {
        console.log('Adding video to selection')
        this.selectedVideos.push(video)
      }

      console.log('Updated selectedVideos:', this.selectedVideos)
      this.updateAssignmentPreview()
    },

    handleVideoClick(videoId, index, event) {
      if (!this.canSelectVideo(videoId)) return

      if (event.ctrlKey || event.metaKey) {
        // Ctrl+点击：切换单个视频选择状态
        this.toggleVideoSelection(videoId)
        this.lastSelectedVideoIndex = index
      } else if (event.shiftKey && this.lastSelectedVideoIndex !== -1) {
        // Shift+点击：选择范围内的视频
        this.selectVideoRange(this.lastSelectedVideoIndex, index)
      } else {
        // 普通点击：单选
        this.toggleVideoSelection(videoId)
        this.lastSelectedVideoIndex = index
      }
    },

    selectVideoRange(startIndex, endIndex) {
      const start = Math.min(startIndex, endIndex)
      const end = Math.max(startIndex, endIndex)

      for (let i = start; i <= end; i++) {
        if (i < this.availableVideos.length) {
          const video = this.availableVideos[i]
          // 视频数量不能超过选中的设备数量
          if (!this.isVideoSelected(video.id) && this.selectedVideos.length < this.selectedDevices.length) {
            this.selectedVideos.push(video)
          }
        }
      }
      this.updateAssignmentPreview()
    },

    canSelectVideo(videoId) {
      // 视频数量不能超过选中的设备数量（一对一分配）
      if (this.selectedDevices.length === 0) return false
      return this.isVideoSelected(videoId) || this.selectedVideos.length < this.selectedDevices.length
    },

    selectAllVideos() {
      // 视频数量不能超过选中的设备数量
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请先选择设备')
        return
      }
      const maxVideos = Math.min(this.selectedDevices.length, this.availableVideos.length)
      const availableVideos = this.availableVideos.slice(0, maxVideos)
      this.selectedVideos = availableVideos
      this.updateAssignmentPreview()
    },

    clearVideoSelection() {
      this.selectedVideos = []
      this.lastSelectedVideoIndex = -1
      this.updateAssignmentPreview()
    },

    // 智能选择视频
    async smartSelectVideos() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请先选择设备')
        return
      }

      this.smartSelectionLoading = true
      try {
        const token = this.$store.getters['auth/token']
        const response = await axios.post(`${this.getApiBaseUrl()}/api/xiaohongshu/smart-select-videos`, {
          deviceIds: this.selectedDevices,
          videoCount: this.selectedDevices.length
        }, {
          headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        })

        if (response.data.success) {
          const { selectedVideos, selectionStrategy } = response.data.data

          // 清空当前选择
          this.selectedVideos = []

          // 设置智能选择的视频 - 需要找到完整的视频对象
          const selectedVideoObjects = []
          selectedVideos.forEach(smartVideo => {
            const fullVideo = this.availableVideos.find(v => v.id === smartVideo.id)
            if (fullVideo) {
              selectedVideoObjects.push(fullVideo)
            } else {
              // 如果在availableVideos中找不到，使用API返回的视频信息
              selectedVideoObjects.push({
                id: smartVideo.id,
                original_name: smartVideo.original_name,
                file_size: smartVideo.file_size,
                total_transfer_count: smartVideo.total_transfers,
                successful_transfer_count: smartVideo.successful_transfers,
                transferred_devices: smartVideo.transferred_devices,
                last_transfer_time: smartVideo.last_transfer_time
              })
            }
          })

          this.selectedVideos = selectedVideoObjects

          // 更新预览
          this.updateAssignmentPreview()

          // 显示选择策略信息
          this.$message({
            type: 'success',
            message: `智能选择完成！选择了 ${selectedVideos.length} 个视频`,
            duration: 3000
          })

          // 显示详细的选择策略
          this.$notify({
            title: '智能选择策略',
            message: `
              ${selectionStrategy.strategy}

              统计信息：
              • 从未传输的视频：${selectionStrategy.neverTransferredCount} 个
              • 未传输到选中设备的视频：${selectionStrategy.notToSelectedDevicesCount} 个
              • 总可用视频：${selectionStrategy.totalVideos} 个
            `,
            type: 'info',
            duration: 8000
          })

          console.log('智能选择结果:', {
            selectedVideos: selectedVideoObjects,
            selectionStrategy,
            selectedDevices: this.selectedDevices
          })

        } else {
          this.$message.error('智能选择失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('智能选择视频失败:', error)
        this.$message.error('智能选择视频失败: ' + error.message)
      } finally {
        this.smartSelectionLoading = false
      }
    },

    getVideoFormat(filename) {
      const ext = filename.split('.').pop()?.toLowerCase()
      return ext ? ext.toUpperCase() : 'VIDEO'
    },

    updateAssignmentPreview() {
      if (this.selectedDevices.length === 0 || this.selectedVideos.length === 0) {
        this.assignmentPreview = []
        return
      }

      const preview = []

      // 一对一分配：每个设备分配一个视频
      for (let i = 0; i < this.selectedDevices.length; i++) {
        const deviceId = this.selectedDevices[i]
        const device = this.onlineDevices.find(d => d.device_id === deviceId)

        // 如果有对应的视频，则分配给该设备
        if (i < this.selectedVideos.length) {
          const assignedVideo = this.selectedVideos[i]
          preview.push({
            deviceId: deviceId,
            deviceName: device ? device.device_name : deviceId,
            videos: [assignedVideo] // 每个设备只分配一个视频
          })
        } else {
          // 如果视频不够，显示未分配状态
          preview.push({
            deviceId: deviceId,
            deviceName: device ? device.device_name : deviceId,
            videos: [],
            status: 'no_video' // 标记为无视频分配
          })
        }
      }

      this.assignmentPreview = preview
    },

    resetAssignForm() {
      this.assignForm = {
        deviceCount: 1,
        strategy: 'one_to_one' // 每个设备一个视频
      }
      this.selectedDevices = []
      this.selectedVideos = []
      this.assignmentPreview = []
      // 重置批量选择状态
      this.lastSelectedDeviceIndex = -1
      this.lastSelectedVideoIndex = -1
    },

    getThumbnailUrl(thumbnailPath) {
      if (!thumbnailPath) return ''
      // 使用动态服务器地址
      const { getApiBaseUrl } = require('@/utils/serverConfig')
      return `${getApiBaseUrl()}${thumbnailPath}`
    },

    async executeVideoAssignment() {
      if (!this.canExecuteAssignment) {
        this.$message.warning('请完善分配配置')
        return
      }

      this.assignmentLoading = true

      try {
        const taskId = `video_assign_${Date.now()}`
        const videoIds = this.selectedVideos.map(v => v.id)

        const response = await axios.post('/api/xiaohongshu/assign-videos', {
          videoIds: videoIds,
          deviceIds: this.selectedDevices,
          taskId: taskId,
          strategy: this.assignForm.strategy
        })

        if (response.data.success) {
          this.$message.success(`视频分配成功！共分配了 ${response.data.data.assignments} 个任务`)

          // 同步分配结果到小红书自动化的视频发布功能
          this.syncAssignmentToVideoPublish()

          // 显示分配结果
          this.$alert(`
分配完成详情：
• 任务ID: ${taskId}
• 设备数量: ${this.selectedDevices.length}
• 视频数量: ${this.selectedVideos.length}
• 分配策略: 灵活分配（每个设备一个视频）

分配详情:
${this.assignmentPreview.map(item => `• ${item.deviceName}: ${item.videos.length} 个视频`).join('\n')}

✅ 已自动同步到视频发布功能：
• 已选中分配的设备
• 已为每个设备配置对应的视频
          `, '分配成功', {
            confirmButtonText: '确定',
            type: 'success'
          })

          // 重置表单并关闭对话框
          this.resetAssignForm()
          this.videoAssignDialogVisible = false

          // 刷新统计信息
          this.loadVideoStats()
        } else {
          this.$message.error('分配失败: ' + response.data.message)
        }

      } catch (error) {
        console.error('执行视频分配失败:', error)
        this.$message.error('执行视频分配失败: ' + error.message)
      } finally {
        this.assignmentLoading = false
      }
    },

    // 同步分配结果到小红书自动化的视频发布功能
    syncAssignmentToVideoPublish() {
      try {
        console.log('🔄 开始同步视频分配结果到视频发布功能')
        console.log('分配预览:', this.assignmentPreview)

        // 构建分配结果数据
        const assignmentData = {
          selectedDevices: this.selectedDevices, // 选中的设备ID列表
          deviceVideoAssignments: {} // 每个设备分配的视频
        }

        // 为每个设备构建视频分配数据
        this.assignmentPreview.forEach(assignment => {
          if (assignment.videos && assignment.videos.length > 0) {
            assignmentData.deviceVideoAssignments[assignment.deviceId] = {
              deviceId: assignment.deviceId,
              deviceName: assignment.deviceName,
              assignedVideo: assignment.videos[0], // 每个设备只分配一个视频
              videoInfo: {
                id: assignment.videos[0].id,
                original_name: assignment.videos[0].original_name,
                file_path: assignment.videos[0].file_path,
                file_size: assignment.videos[0].file_size,
                thumbnail_path: assignment.videos[0].thumbnail_path
              }
            }
          }
        })

        console.log('构建的分配数据:', assignmentData)

        // 发射事件到父组件，通知同步分配结果
        this.$emit('video-assignment-completed', assignmentData)

        // 也可以通过全局事件总线发送
        this.$root.$emit('xiaohongshu-video-assignment-sync', assignmentData)

        console.log('✅ 视频分配结果同步事件已发送')

      } catch (error) {
        console.error('❌ 同步视频分配结果失败:', error)
        this.$message.warning('同步分配结果到视频发布功能时出现问题，请手动配置')
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 获取传输设备信息
    getTransferredDevices(transferredDevicesJson) {
      if (!transferredDevicesJson) return []
      try {
        const devices = typeof transferredDevicesJson === 'string'
          ? JSON.parse(transferredDevicesJson)
          : transferredDevicesJson

        return Array.isArray(devices) ? devices : []
      } catch (e) {
        console.error('解析传输设备信息失败:', e)
        return []
      }
    },

    // 获取设备显示名称
    getDeviceDisplayName(device) {
      if (!device) return ''

      // 优先显示IP地址，如果没有则显示设备名称或设备ID
      if (device.ipAddress) {
        return device.ipAddress
      } else if (device.deviceName && device.deviceName !== device.deviceId) {
        return device.deviceName
      } else {
        return device.deviceId || ''
      }
    },

    // 格式化时长
    formatDuration(seconds) {
      if (!seconds) return '-'
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    // 获取传输状态文本
    getTransferStatusText(status) {
      const statusMap = {
        'pending': '等待中',
        'transferring': '传输中',
        'in_progress': '传输中',
        'completed': '已完成',
        'failed': '失败',
        'success': '成功',
        'error': '错误'
      }
      return statusMap[status] || status
    }
  }
}
</script>

<style scoped>
.video-file-manager {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;

  .upload-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
  }

  .upload-tips {
    margin-bottom: 20px;

    .tip-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
      color: #606266;

      i {
        margin-right: 8px;
        color: #909399;
      }

      &.warning {
        color: #E6A23C;

        i {
          color: #E6A23C;
        }
      }
    }
  }

  .folder-preview {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    padding: 15px;
    background: white;
    margin-bottom: 15px;

    .folder-info {
      margin-bottom: 15px;

      h4 {
        margin: 0 0 8px 0;
        color: #303133;
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #409eff;
        }
      }

      p {
        margin: 0;
        color: #909399;
        font-size: 13px;
      }
    }

    .file-list {
      .file-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid #ebeef5;

        span {
          font-weight: 500;
          color: #303133;
        }
      }

      .file-items {
        max-height: 200px;
        overflow-y: auto;

        .file-item {
          display: flex;
          align-items: center;
          padding: 6px 0;
          border-bottom: 1px solid #f5f7fa;

          i {
            margin-right: 8px;
            color: #409eff;
          }

          .file-name {
            flex: 1;
            color: #303133;
            font-size: 13px;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
            min-width: 60px;
            text-align: right;
          }

          &:last-child {
            border-bottom: none;
          }
        }

        .more-files {
          text-align: center;
          color: #909399;
          font-size: 12px;
          padding: 8px 0;
          font-style: italic;
        }
      }
    }

    .no-videos {
      text-align: center;
      color: #909399;
      padding: 20px;

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

.upload-progress {
  margin-top: 15px;

  .upload-details {
    margin-top: 15px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
    background: white;

    .detail-header {
      font-weight: 500;
      margin-bottom: 8px;
      color: #303133;
    }

    .detail-items {
      max-height: 120px;
      overflow-y: auto;

      .detail-item {
        display: flex;
        align-items: center;
        padding: 4px 0;
        font-size: 12px;

        i {
          margin-right: 8px;
          width: 14px;
        }

        .status-text {
          margin-left: auto;
          font-weight: 500;
        }

        &.success {
          color: #67c23a;
        }

        &.error {
          color: #f56c6c;
        }

        &.uploading {
          color: #409eff;
        }
      }
    }
  }
}

.upload-stats {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      color: #909399;
    }
  }
}

/* 视频分配对话框样式 */
.video-assign-content {
  .assign-config-card,
  .device-selection-card,
  .video-selection-card,
  .assignment-preview-card {
    margin-bottom: 20px;
  }

  .assign-actions {
    margin-top: 20px;
    text-align: center;
  }

  .selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selection-info {
      display: flex;
      align-items: center;
    }

    .selection-actions {
      display: flex;
      gap: 8px;
    }
  }

  .device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;

    .device-item {
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      padding: 15px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
      }

      &.selected {
        border-color: #409eff;
        background-color: #ecf5ff;
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:hover {
          border-color: #e4e7ed;
          background-color: transparent;
        }
      }

      .device-info {
        flex: 1;

        .device-name {
          font-weight: 500;
          color: #303133;
          margin-bottom: 8px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .device-details {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 6px;

          .device-ip {
            font-size: 12px;
            color: #409eff;
            font-weight: 500;
            background: #f0f9ff;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #b3d8ff;
          }
        }

        .device-extra-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;

          .device-model {
            font-size: 11px;
            color: #606266;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 60%;
          }

          .device-android {
            font-size: 11px;
            color: #67c23a;
            font-weight: 500;
          }
        }

        .device-connection-info {
          .last-seen {
            font-size: 10px;
            color: #c0c4cc;
            font-style: italic;
          }
        }
      }

      .device-checkbox {
        margin-left: 10px;
      }
    }
  }

  .video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;

    .video-item {
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: #409eff;
        background-color: #ecf5ff;
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:hover {
          border-color: #e4e7ed;
          background-color: transparent;
          transform: none;
          box-shadow: none;
        }
      }

      .video-thumbnail {
        height: 100px;
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .no-thumbnail {
          color: #c0c4cc;
          font-size: 24px;
        }
      }

      .video-info {
        padding: 10px;
        position: relative;
        padding-top: 35px;

        .video-name {
          font-size: 12px;
          color: #303133;
          margin-bottom: 6px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: 500;
        }

        .video-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;

          .video-size {
            font-size: 11px;
            color: #909399;
          }

          .video-duration {
            font-size: 11px;
            color: #67c23a;
            font-weight: 500;
          }
        }

        .video-extra-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .upload-time {
            font-size: 10px;
            color: #c0c4cc;
          }
        }

        .video-transfer-info {
          margin-top: 6px;
          padding-top: 6px;
          border-top: 1px solid #f0f0f0;

          .transfer-stats {
            font-size: 10px;
            margin-bottom: 4px;

            .transfer-count, .success-count {
              display: inline-block;
              margin-right: 12px;
              color: #67c23a;

              i {
                margin-right: 2px;
              }
            }

            .success-count {
              color: #409eff;
            }
          }

          .transferred-devices {
            .devices-label {
              font-size: 9px;
              color: #606266;
              margin-right: 4px;
            }

            .el-tag {
              margin-right: 2px;
              margin-bottom: 2px;
            }

            .more-devices {
              font-size: 9px;
              color: #909399;
              margin-left: 4px;
            }
          }
        }
      }

      .video-checkbox {
        position: absolute;
        top: 8px;
        right: 8px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        padding: 2px;
        z-index: 10;
        cursor: pointer;
      }
    }
  }

  .preview-content {
    .assignment-item {
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 15px;

      &.no-video {
        border-color: #f56c6c;
        background-color: #fef0f0;
      }

      .device-info {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;

        .video-count {
          color: #909399;
          font-size: 12px;
        }

        .no-video-text {
          color: #f56c6c;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .video-list {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .el-tag {
          font-size: 11px;
        }
      }

      .no-video-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        background-color: #fdf6ec;
        border: 1px dashed #e6a23c;
        border-radius: 4px;
        color: #e6a23c;
        font-size: 12px;
      }
    }
  }
}

/* 传输信息弹出框样式 */
.video-transfer-info {
  .video-basic-info,
  .transfer-statistics,
  .transfer-records {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #409EFF;
      font-size: 16px;
    }
  }

  .info-item {
    margin-bottom: 10px;

    .info-label {
      font-weight: 600;
      color: #606266;
      margin-right: 8px;
    }

    .info-value {
      color: #303133;
    }
  }

  .stat-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>
