# =====================================================
# Auto.js云群控系统 - 环境配置文件
# 复制此文件为 .env 并根据您的环境修改配置
# =====================================================

# 服务器配置
NODE_ENV=development
SERVER_PORT=3002
SERVER_HOST=localhost

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 本地数据库配置（现有系统）
LOCAL_DB_HOST=localhost
LOCAL_DB_PORT=3306
LOCAL_DB_USER=autojs_control
LOCAL_DB_PASSWORD=root
LOCAL_DB_NAME=autojs_control

# 主站数据库配置（用于账号验证）
MAIN_DB_HOST=localhost
MAIN_DB_PORT=3306
MAIN_DB_USER=root
MAIN_DB_PASSWORD=root
MAIN_DB_NAME=autojs_main_test

# 系统配置
DEFAULT_TRIAL_DAYS=7
MAX_DEVICES_PER_USER=50
ENABLE_REGISTRATION=false
MAINTENANCE_MODE=false

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_PATH=./uploads

# 缓存配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=

# 安全配置
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=86400
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
