<template>
  <div class="activation-codes-container">
    <el-card class="page-header" shadow="never">
      <div slot="header" class="clearfix">
        <span class="page-title">卡密/激活码管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="refreshData"
        >
          刷新
        </el-button>
      </div>
      
      <!-- 统计信息 -->
      <div class="stats-row">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.basic?.total_codes || 0 }}</div>
              <div class="stat-label">总卡密数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.basic?.active_codes || 0 }}</div>
              <div class="stat-label">可用卡密</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.basic?.used_codes || 0 }}</div>
              <div class="stat-label">已使用</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.basic?.total_uses || 0 }}</div>
              <div class="stat-label">总使用次数</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 生成卡密 -->
    <el-card class="generate-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>生成新卡密</span>
      </div>
      
      <el-form :model="generateForm" :rules="generateRules" ref="generateForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="生成数量" prop="count">
              <el-input-number 
                v-model="generateForm.count" 
                :min="1" 
                :max="1000" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卡密类型" prop="type">
              <el-select v-model="generateForm.type" style="width: 100%">
                <el-option label="正式卡密" value="card" />
                <el-option label="激活码" value="activation" />
                <el-option label="试用卡密" value="trial" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效天数" prop="durationDays">
              <el-input-number 
                v-model="generateForm.durationDays" 
                :min="1" 
                :max="3650" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最大使用次数" prop="maxUses">
              <el-input-number 
                v-model="generateForm.maxUses" 
                :min="1" 
                :max="100" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批次ID" prop="batchId">
              <el-input v-model="generateForm.batchId" placeholder="可选，用于批量管理" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卡密过期时间">
              <el-date-picker
                v-model="generateForm.expiresAt"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() < Date.now()
                  }
                }"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="描述信息">
          <el-input 
            v-model="generateForm.description" 
            type="textarea" 
            :rows="2"
            placeholder="可选，描述这批卡密的用途"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="generating" 
            @click="generateCodes"
          >
            {{ generating ? '生成中...' : '生成卡密' }}
          </el-button>
          <el-button @click="resetGenerateForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 卡密列表 -->
    <el-card class="list-card" shadow="never">
      <div slot="header" class="clearfix">
        <span>卡密列表</span>
        <div style="float: right">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索卡密或描述"
            style="width: 200px; margin-right: 10px"
            prefix-icon="el-icon-search"
            @keyup.enter.native="loadCodes"
          />
          <el-select v-model="searchForm.type" placeholder="类型" style="width: 120px; margin-right: 10px" @change="loadCodes">
            <el-option label="全部" value="" />
            <el-option label="正式卡密" value="card" />
            <el-option label="激活码" value="activation" />
            <el-option label="试用卡密" value="trial" />
          </el-select>
          <el-select v-model="searchForm.status" placeholder="状态" style="width: 120px; margin-right: 10px" @change="loadCodes">
            <el-option label="全部" value="" />
            <el-option label="可用" value="active" />
            <el-option label="已用完" value="used" />
            <el-option label="已过期" value="expired" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
          <el-button type="primary" @click="loadCodes">搜索</el-button>
        </div>
      </div>
      
      <el-table 
        :data="codes" 
        v-loading="loading"
        style="width: 100%"
        :default-sort="{prop: 'created_at', order: 'descending'}"
      >
        <el-table-column prop="code" label="卡密/激活码" width="200">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.code" placement="top">
              <span class="code-text" @click="copyCode(scope.row.code)">
                {{ scope.row.code }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="scope.row.type === 'card' ? 'primary' : scope.row.type === 'trial' ? 'warning' : 'success'"
              size="small"
            >
              {{ scope.row.type === 'card' ? '正式' : scope.row.type === 'trial' ? '试用' : '激活码' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration_days" label="有效天数" width="100" />
        
        <el-table-column label="使用情况" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.used_count }}/{{ scope.row.max_uses }}</span>
            <el-progress 
              :percentage="Math.round((scope.row.used_count / scope.row.max_uses) * 100)"
              :stroke-width="6"
              :show-text="false"
              style="margin-top: 2px"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag 
              :type="getStatusType(scope.row)"
              size="small"
            >
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="batch_id" label="批次ID" width="150" show-overflow-tooltip />
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="expires_at" label="过期时间" width="180">
          <template slot-scope="scope">
            {{ scope.row.expires_at ? formatDate(scope.row.expires_at) : '永不过期' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button 
              size="mini" 
              :type="scope.row.status === 'active' ? 'warning' : 'success'"
              @click="toggleStatus(scope.row)"
              :disabled="scope.row.used_count > 0"
            >
              {{ scope.row.status === 'active' ? '禁用' : '启用' }}
            </el-button>
            <el-button 
              size="mini" 
              type="danger" 
              @click="deleteCode(scope.row)"
              :disabled="scope.row.used_count > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </el-card>

    <!-- 生成结果对话框 -->
    <el-dialog
      title="卡密生成成功"
      :visible.sync="showGenerateResult"
      width="60%"
      :close-on-click-modal="false"
    >
      <div v-if="generateResult">
        <p>成功生成 <strong>{{ generateResult.count }}</strong> 个卡密，批次ID: <strong>{{ generateResult.batchId }}</strong></p>
        
        <el-input
          type="textarea"
          :rows="10"
          :value="generateResult.codes.join('\n')"
          readonly
          placeholder="生成的卡密列表"
        />
        
        <div style="margin-top: 10px">
          <el-button type="primary" @click="copyAllCodes">复制所有卡密</el-button>
          <el-button @click="downloadCodes">下载为文件</el-button>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showGenerateResult = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'ActivationCodes',
  data() {
    return {
      loading: false,
      generating: false,
      codes: [],
      stats: {},
      showGenerateResult: false,
      generateResult: null,
      
      // 生成表单
      generateForm: {
        count: 10,
        type: 'card',
        durationDays: 30,
        maxUses: 1,
        batchId: '',
        description: '',
        expiresAt: null
      },
      
      generateRules: {
        count: [
          { required: true, message: '请输入生成数量', trigger: 'blur' },
          { type: 'number', min: 1, max: 1000, message: '数量必须在1-1000之间', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择卡密类型', trigger: 'change' }
        ],
        durationDays: [
          { required: true, message: '请输入有效天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 3650, message: '天数必须在1-3650之间', trigger: 'blur' }
        ],
        maxUses: [
          { required: true, message: '请输入最大使用次数', trigger: 'blur' },
          { type: 'number', min: 1, max: 100, message: '次数必须在1-100之间', trigger: 'blur' }
        ]
      },
      
      // 搜索表单
      searchForm: {
        search: '',
        type: '',
        status: '',
        batchId: ''
      },
      
      // 分页
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      }
    }
  },
  
  mounted() {
    this.loadStats()
    this.loadCodes()
  },
  
  methods: {
    async loadStats() {
      try {
        const response = await axios.get('/api/admin/activation-codes/stats', {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.stats = response.data.data
        }
      } catch (error) {
        console.error('加载统计信息失败:', error)
      }
    },
    
    async loadCodes() {
      try {
        this.loading = true
        
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          ...this.searchForm
        }
        
        const response = await axios.get('/api/admin/activation-codes', {
          params,
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.codes = response.data.data.codes
          this.pagination = response.data.data.pagination
        }
      } catch (error) {
        console.error('加载卡密列表失败:', error)
        this.$message.error('加载卡密列表失败')
      } finally {
        this.loading = false
      }
    },
    
    async generateCodes() {
      try {
        await this.$refs.generateForm.validate()
        this.generating = true
        
        const response = await axios.post('/api/admin/activation-codes/generate', this.generateForm, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        if (response.data.success) {
          this.generateResult = response.data.data
          this.showGenerateResult = true
          this.$message.success('卡密生成成功')
          
          // 刷新数据
          this.loadStats()
          this.loadCodes()
          
          // 重置表单
          this.resetGenerateForm()
        }
      } catch (error) {
        console.error('生成卡密失败:', error)
        this.$message.error(error.response?.data?.message || '生成卡密失败')
      } finally {
        this.generating = false
      }
    },
    
    resetGenerateForm() {
      this.generateForm = {
        count: 10,
        type: 'card',
        durationDays: 30,
        maxUses: 1,
        batchId: '',
        description: '',
        expiresAt: null
      }
      this.$refs.generateForm?.resetFields()
    },
    
    async toggleStatus(row) {
      try {
        const newStatus = row.status === 'active' ? 'disabled' : 'active'
        
        await axios.put(`/api/admin/activation-codes/${row.id}/status`, 
          { status: newStatus },
          { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
        )
        
        this.$message.success('状态更新成功')
        this.loadCodes()
        this.loadStats()
      } catch (error) {
        console.error('更新状态失败:', error)
        this.$message.error('更新状态失败')
      }
    },
    
    async deleteCode(row) {
      try {
        await this.$confirm('确定要删除这个卡密吗？删除后无法恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await axios.delete(`/api/admin/activation-codes/${row.id}`, {
          headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
        })
        
        this.$message.success('删除成功')
        this.loadCodes()
        this.loadStats()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除失败:', error)
          this.$message.error(error.response?.data?.message || '删除失败')
        }
      }
    },
    
    copyCode(code) {
      navigator.clipboard.writeText(code).then(() => {
        this.$message.success('卡密已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    copyAllCodes() {
      const codesText = this.generateResult.codes.join('\n')
      navigator.clipboard.writeText(codesText).then(() => {
        this.$message.success('所有卡密已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },
    
    downloadCodes() {
      const codesText = this.generateResult.codes.join('\n')
      const blob = new Blob([codesText], { type: 'text/plain' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `activation_codes_${this.generateResult.batchId}_${new Date().getTime()}.txt`
      a.click()
      window.URL.revokeObjectURL(url)
    },
    
    refreshData() {
      this.loadStats()
      this.loadCodes()
    },
    
    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.loadCodes()
    },
    
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadCodes()
    },
    
    getStatusType(row) {
      if (!row.isAvailable) return 'danger'
      if (row.status === 'active') return 'success'
      if (row.status === 'used') return 'info'
      if (row.status === 'expired') return 'warning'
      return 'danger'
    },
    
    getStatusText(row) {
      if (row.isExpired) return '已过期'
      if (row.used_count >= row.max_uses) return '已用完'
      if (row.status === 'active') return '可用'
      if (row.status === 'disabled') return '已禁用'
      return row.status
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.activation-codes-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
}

.stats-row {
  margin-top: 10px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.generate-card {
  margin-bottom: 20px;
}

.list-card {
  margin-bottom: 20px;
}

.code-text {
  cursor: pointer;
  color: #409eff;
  font-family: monospace;
}

.code-text:hover {
  text-decoration: underline;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
