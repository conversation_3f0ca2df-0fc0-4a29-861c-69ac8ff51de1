-- 设备连接码功能数据库更新脚本
-- 执行时间：2025-08-06
-- 目的：添加设备连接码功能所需的表和字段

USE autojs_control;

-- 1. 为devices表添加user_id字段（如果不存在）
ALTER TABLE devices 
ADD COLUMN IF NOT EXISTS user_id INT DEFAULT NULL COMMENT '所属用户ID' AFTER last_seen,
ADD INDEX IF NOT EXISTS idx_devices_user_id (user_id);

-- 添加外键约束（如果不存在）
-- 注意：如果users表不存在或有数据完整性问题，这个约束可能会失败
-- 可以根据实际情况决定是否添加外键约束
-- ALTER TABLE devices 
-- ADD CONSTRAINT fk_devices_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- 2. 创建设备连接码表
CREATE TABLE IF NOT EXISTS device_connection_codes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(20) UNIQUE NOT NULL COMMENT '连接码',
  user_id INT NOT NULL COMMENT '用户ID',
  username VARCHAR(50) NOT NULL COMMENT '用户名',
  description VARCHAR(200) DEFAULT '' COMMENT '连接码描述',
  max_devices INT DEFAULT 1 COMMENT '最大可连接设备数',
  used_count INT DEFAULT 0 COMMENT '已使用次数',
  expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at),
  INDEX idx_is_active (is_active)
);

-- 如果users表存在，添加外键约束
-- ALTER TABLE device_connection_codes 
-- ADD CONSTRAINT fk_connection_codes_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 3. 创建设备连接记录表
CREATE TABLE IF NOT EXISTS device_connections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  device_id VARCHAR(100) NOT NULL,
  connection_code VARCHAR(20) NOT NULL COMMENT '使用的连接码',
  user_id INT NOT NULL COMMENT '分配的用户ID',
  device_name VARCHAR(100) NOT NULL,
  device_info JSON COMMENT '设备信息',
  connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_device_id (device_id),
  INDEX idx_connection_code (connection_code),
  INDEX idx_user_id (user_id)
);

-- 如果相关表存在，添加外键约束
-- ALTER TABLE device_connections 
-- ADD CONSTRAINT fk_device_connections_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- ALTER TABLE device_connections 
-- ADD CONSTRAINT fk_device_connections_code 
-- FOREIGN KEY (connection_code) REFERENCES device_connection_codes(code) ON DELETE CASCADE;

-- 4. 为现有设备设置默认用户（可选）
-- 将所有没有user_id的设备分配给admin用户（假设admin用户ID为1）
UPDATE devices 
SET user_id = 1 
WHERE user_id IS NULL 
  AND EXISTS (SELECT 1 FROM users WHERE id = 1 AND username = 'admin');

-- 5. 验证数据完整性
-- 检查devices表的user_id字段
SELECT 
    'devices表user_id字段检查' as check_name,
    COUNT(*) as total_devices,
    COUNT(user_id) as devices_with_user_id,
    COUNT(*) - COUNT(user_id) as devices_without_user_id
FROM devices;

-- 检查新创建的表
SELECT 
    'device_connection_codes表' as table_name,
    COUNT(*) as record_count
FROM device_connection_codes;

SELECT 
    'device_connections表' as table_name,
    COUNT(*) as record_count
FROM device_connections;

-- 6. 创建示例连接码（可选，仅用于测试）
-- 为admin用户创建一个测试连接码
INSERT IGNORE INTO device_connection_codes 
(code, user_id, username, description, max_devices, expires_at)
SELECT 
    'TEST1234' as code,
    u.id as user_id,
    u.username,
    '测试连接码' as description,
    10 as max_devices,
    DATE_ADD(NOW(), INTERVAL 30 DAY) as expires_at
FROM users u 
WHERE u.username = 'admin' 
LIMIT 1;

-- 显示创建的测试连接码
SELECT 
    code,
    username,
    description,
    max_devices,
    used_count,
    expires_at,
    is_active,
    created_at
FROM device_connection_codes 
WHERE code = 'TEST1234';

-- 完成提示
SELECT '设备连接码功能数据库更新完成！' AS message;

-- 使用说明
SELECT '
使用说明：
1. 登录Web管理界面，进入设备管理页面
2. 点击"连接码管理"创建新的连接码
3. 在设备脚本中输入连接码进行连接
4. 测试连接码：TEST1234（30天有效期）
' AS usage_instructions;
