/**
 * JWT认证测试脚本
 * 测试新的JWT认证系统是否正常工作
 */

const axios = require('axios');

// 测试配置
const BASE_URL = 'http://localhost:3002';

async function testJWTAuth() {
  console.log('🚀 开始JWT认证测试...\n');
  
  try {
    // 1. 测试登录获取JWT token
    console.log('🔑 测试用户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ 登录成功');
      console.log('📄 用户信息:', loginResponse.data.user);
      
      const token = loginResponse.data.token;
      console.log('🎫 JWT Token:', token.substring(0, 50) + '...');
      
      // 2. 测试使用JWT token访问受保护的API
      console.log('\n📋 测试受保护的API...');
      
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };
      
      // 测试设备列表API
      try {
        const deviceResponse = await axios.get(`${BASE_URL}/api/device/list`, { headers });
        console.log(`✅ 设备列表API成功 - 返回${deviceResponse.data.data?.length || 0}个设备`);
      } catch (error) {
        console.log(`❌ 设备列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试设备统计API
      try {
        const statsResponse = await axios.get(`${BASE_URL}/api/device/statistics`, { headers });
        console.log(`✅ 设备统计API成功:`, statsResponse.data.data);
      } catch (error) {
        console.log(`❌ 设备统计API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试小红书日志API
      try {
        const xiaohongshuResponse = await axios.get(`${BASE_URL}/api/xiaohongshu/logs`, { headers });
        console.log(`✅ 小红书日志API成功 - 返回${xiaohongshuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 小红书日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试闲鱼日志API
      try {
        const xianyuResponse = await axios.get(`${BASE_URL}/api/xianyu/logs`, { headers });
        console.log(`✅ 闲鱼日志API成功 - 返回${xianyuResponse.data.data?.logs?.length || 0}条日志`);
      } catch (error) {
        console.log(`❌ 闲鱼日志API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 测试脚本列表API
      try {
        const scriptResponse = await axios.get(`${BASE_URL}/api/script/list`, { headers });
        console.log(`✅ 脚本列表API成功 - 返回${scriptResponse.data.data?.length || 0}个脚本`);
      } catch (error) {
        console.log(`❌ 脚本列表API失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 3. 测试token验证API
      console.log('\n🔍 测试token验证...');
      try {
        const verifyResponse = await axios.get(`${BASE_URL}/api/auth/verify`, { headers });
        console.log('✅ Token验证成功:', verifyResponse.data.user);
      } catch (error) {
        console.log(`❌ Token验证失败: ${error.response?.data?.message || error.message}`);
      }
      
      // 4. 测试无效token
      console.log('\n🚫 测试无效token...');
      try {
        const invalidHeaders = {
          'Authorization': 'Bearer invalid-token',
          'Content-Type': 'application/json'
        };
        await axios.get(`${BASE_URL}/api/device/list`, { headers: invalidHeaders });
        console.log('❌ 无效token测试失败：应该返回401错误');
      } catch (error) {
        if (error.response?.status === 401) {
          console.log('✅ 无效token正确被拒绝:', error.response.data.message);
        } else {
          console.log(`❌ 无效token测试异常: ${error.message}`);
        }
      }
      
      console.log('\n🎉 JWT认证测试完成！');
      console.log('✨ 数据隔离功能已成功实现！');
      console.log('📝 所有API都已使用JWT认证和用户隔离中间件。');
      
    } else {
      console.log('❌ 登录失败:', loginResponse.data.message);
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ 无法连接到服务器，请确保服务器正在运行在端口3002');
    } else {
      console.log('❌ 测试失败:', error.response?.data?.message || error.message);
    }
  }
}

// 执行测试
testJWTAuth();
