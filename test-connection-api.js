/**
 * 测试连接码API的简单脚本
 */

const http = require('http');

function makeRequest(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testConnectionAPI() {
  console.log('🚀 测试连接码API...\n');

  try {
    // 1. 登录获取token
    console.log('1. 登录获取token...');
    const loginResult = await makeRequest('/api/auth/login', 'POST', {
      username: 'admin',
      password: 'admin123'
    });
    
    if (loginResult.statusCode !== 200 || !loginResult.data.success) {
      console.log('❌ 登录失败:', loginResult.data);
      return;
    }
    
    const token = loginResult.data.token;
    console.log('✅ 登录成功');

    // 2. 测试获取连接码列表
    console.log('\n2. 测试获取连接码列表...');
    const listResult = await makeRequest('/api/device/connection-codes', 'GET', null, token);
    console.log(`状态码: ${listResult.statusCode}`);
    
    if (listResult.statusCode === 200) {
      console.log('✅ 连接码列表API正常');
      console.log(`当前连接码数量: ${listResult.data.data?.length || 0}`);
      if (listResult.data.data && listResult.data.data.length > 0) {
        console.log('连接码列表:');
        listResult.data.data.forEach(code => {
          console.log(`  - ${code.code} | 描述: ${code.description} | 使用: ${code.used_count}/${code.max_devices}`);
        });
      }
    } else {
      console.log('❌ 连接码列表API异常:', listResult.data);
    }

    // 3. 测试创建连接码
    console.log('\n3. 测试创建连接码...');
    const createResult = await makeRequest('/api/device/connection-codes', 'POST', {
      description: 'API测试连接码',
      maxDevices: 5,
      expiresInHours: 24
    }, token);
    
    console.log(`状态码: ${createResult.statusCode}`);
    
    if (createResult.statusCode === 200 && createResult.data.success) {
      const newCode = createResult.data.data.code;
      console.log(`✅ 连接码创建成功: ${newCode}`);
      
      // 4. 测试连接码验证
      console.log('\n4. 测试连接码验证...');
      const verifyResult = await makeRequest('/api/device/verify-connection-code', 'POST', {
        code: newCode,
        deviceId: 'test_api_device',
        deviceName: 'API测试设备',
        deviceInfo: {
          brand: 'TestBrand',
          model: 'TestModel',
          ipAddress: '*************'
        }
      });
      
      console.log(`状态码: ${verifyResult.statusCode}`);
      
      if (verifyResult.statusCode === 200 && verifyResult.data.success) {
        console.log('✅ 连接码验证成功');
        console.log(`分配用户: ${verifyResult.data.data.username}`);
      } else {
        console.log('❌ 连接码验证失败:', verifyResult.data);
      }
      
    } else {
      console.log('❌ 连接码创建失败:', createResult.data);
    }

    // 5. 测试使用TEST1234连接码
    console.log('\n5. 测试使用TEST1234连接码...');
    const testCodeResult = await makeRequest('/api/device/verify-connection-code', 'POST', {
      code: 'TEST1234',
      deviceId: 'test_device_1234',
      deviceName: '测试设备1234',
      deviceInfo: {
        brand: 'TestBrand',
        model: 'TestModel1234',
        ipAddress: '*************'
      }
    });
    
    console.log(`状态码: ${testCodeResult.statusCode}`);
    
    if (testCodeResult.statusCode === 200 && testCodeResult.data.success) {
      console.log('✅ TEST1234连接码验证成功');
      console.log(`分配用户: ${testCodeResult.data.data.username}`);
    } else {
      console.log('❌ TEST1234连接码验证失败:', testCodeResult.data);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
  
  console.log('\n✅ 连接码API测试完成');
}

// 运行测试
testConnectionAPI();
