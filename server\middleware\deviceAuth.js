/**
 * 设备认证中间件 - 连接码模式
 * 用于验证设备是否已通过连接码注册并关联到用户
 */

// 日志节流机制
const logThrottleMap = new Map();
const LOG_THROTTLE_INTERVAL = 5 * 60 * 1000; // 5分钟间隔

// 节流日志函数
function throttledLog(key, message) {
  const now = Date.now();
  const lastLogTime = logThrottleMap.get(key);

  if (!lastLogTime || (now - lastLogTime) >= LOG_THROTTLE_INTERVAL) {
    console.log(message);
    logThrottleMap.set(key, now);
    return true;
  }
  return false;
}

// 设备认证中间件
const authenticateDevice = (pool) => {
  return async (req, res, next) => {
    try {
      // 优先从URL参数获取deviceId，如果没有则从请求体获取
      const deviceId = req.params.deviceId || req.body.deviceId;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      // 查询设备是否已注册并获取关联的用户信息
      const [devices] = await pool.execute(`
        SELECT d.device_id, d.device_name, d.user_id, d.status,
               dcc.username
        FROM devices d
        LEFT JOIN device_connection_codes dcc ON d.user_id = dcc.user_id
        WHERE d.device_id = ?
        LIMIT 1
      `, [deviceId]);

      if (devices.length === 0) {
        console.log(`[设备认证] 设备${deviceId}未注册`);
        return res.status(404).json({
          success: false,
          message: '设备未注册，请先使用连接码注册设备'
        });
      }

      const device = devices[0];

      // 检查设备状态
      if (device.status === 'offline') {
        console.log(`[设备认证] 设备${deviceId}已离线，返回410状态码`);
        return res.status(410).json({
          success: false,
          message: '设备已离线，请重新连接',
          code: 'DEVICE_OFFLINE'
        });
      }

      // 将设备信息添加到请求对象
      req.device = {
        deviceId: device.device_id,
        deviceName: device.device_name,
        userId: device.user_id,
        username: device.username,
        status: device.status
      };

      // 使用节流日志，避免频繁输出认证成功信息
      const authLogKey = `device_auth_${deviceId}`;
      throttledLog(authLogKey, `[设备认证] 设备${deviceId}认证成功，属于用户${device.user_id}(${device.username})`);
      next();

    } catch (error) {
      console.error('设备认证失败:', error);
      return res.status(500).json({
        success: false,
        message: '设备认证失败: ' + error.message
      });
    }
  };
};

// 设备权限验证中间件（验证设备是否属于指定用户）
const verifyDeviceOwnership = (pool) => {
  return async (req, res, next) => {
    try {
      const { deviceId } = req.params;
      const userId = req.currentUserId; // 来自JWT token认证

      if (!deviceId || !userId) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      if (!pool) {
        return res.status(500).json({
          success: false,
          message: '数据库连接不可用'
        });
      }

      // 验证设备是否属于当前用户
      const [devices] = await pool.execute(`
        SELECT device_id, device_name, user_id
        FROM devices
        WHERE device_id = ? AND user_id = ?
      `, [deviceId, userId]);

      if (devices.length === 0) {
        console.log(`[权限验证] 用户${userId}无权访问设备${deviceId}`);
        return res.status(403).json({
          success: false,
          message: '无权访问此设备'
        });
      }

      console.log(`[权限验证] 用户${userId}有权访问设备${deviceId}`);
      next();

    } catch (error) {
      console.error('设备权限验证失败:', error);
      return res.status(500).json({
        success: false,
        message: '设备权限验证失败: ' + error.message
      });
    }
  };
};

module.exports = {
  authenticateDevice,
  verifyDeviceOwnership
};
