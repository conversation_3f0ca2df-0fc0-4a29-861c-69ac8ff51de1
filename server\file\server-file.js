/**
 * 服务器文件管理模块 - 完整拆分版本
 * 包含所有文件管理相关的API和功能
 * 对应原始文件第1118-1200行和其他文件相关功能的完整内容，包含以下API：
 * - GET /api/file/list - 获取文件列表
 * - GET /api/file/transfers - 获取文件传输记录
 * - POST /api/file/upload - 文件上传
 * - DELETE /api/file/:filename - 删除文件
 * - GET /api/file/download/:filename - 文件下载
 * 以及所有相关的文件管理功能
 */

const fs = require('fs');
const path = require('path');

// 文件管理模块设置函数
async function setupServerFile(app, io, coreData, authData) {
  console.log('🔧 设置文件管理模块...');

  // 引入用户隔离中间件和工具
  const { userIsolationMiddleware } = require('../middleware/userIsolation');
  const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
  const PermissionValidator = require('../utils/PermissionValidator');

  const {
    pool,
    devices, 
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    upload,
    throttledLog
  } = coreData;

  const { authenticateToken } = authData;

  // 创建数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 文件存储
  const fileStorage = new Map(); // 存储文件信息
  const transferRecords = new Map(); // 存储传输记录

  // 初始化上传目录
  const uploadsDir = path.join(__dirname, '../uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('创建上传目录:', uploadsDir);
  }

  // 获取文件列表API (原始文件第1118行) - 已添加用户隔离
  app.get('/api/file/list', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { type, search } = req.query;
      const userId = req.currentUserId;

      // 读取用户专属目录中的文件
      const files = [];
      const userUploadsDir = path.join(uploadsDir, `user_${userId}`);

      if (fs.existsSync(userUploadsDir)) {
        const fileNames = fs.readdirSync(userUploadsDir);

        fileNames.forEach(fileName => {
          const filePath = path.join(userUploadsDir, fileName);
          const stats = fs.statSync(filePath);

          if (stats.isFile()) {
            const fileInfo = {
              name: fileName,
              size: stats.size,
              type: path.extname(fileName).toLowerCase(),
              uploadedAt: stats.birthtime,
              modifiedAt: stats.mtime,
              path: filePath,
              userId: userId
            };

            // 过滤条件
            if (type && !fileName.toLowerCase().includes(type.toLowerCase())) {
              return;
            }
            if (search && !fileName.toLowerCase().includes(search.toLowerCase())) {
              return;
            }

            files.push(fileInfo);
          }
        });
      }

      // 按修改时间排序
      files.sort((a, b) => new Date(b.modifiedAt) - new Date(a.modifiedAt));

      res.json({
        success: true,
        data: {
          files,
          total: files.length,
          uploadsDir
        }
      });

    } catch (error) {
      console.error('获取文件列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取文件列表失败: ' + error.message
      });
    }
  });

  // 获取文件传输记录API (原始文件第1141行) - 已添加用户隔离
  app.get('/api/file/transfers', authenticateToken, userIsolationMiddleware, async (req, res) => {
    try {
      const { page = 1, limit = 20, status, deviceId } = req.query;
      const userId = req.currentUserId;

      // 只获取当前用户的传输记录
      let records = Array.from(transferRecords.values()).filter(record => record.userId === userId);

      // 过滤条件
      if (status) {
        records = records.filter(record => record.status === status);
      }
      if (deviceId) {
        // 验证设备所属权
        const hasPermission = await permissionValidator.validateDeviceOwnership(deviceId, userId);
        if (hasPermission) {
          records = records.filter(record => record.deviceId === deviceId);
        } else {
          records = []; // 无权访问该设备的传输记录
        }
      }

      // 排序（最新的在前）
      records.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // 分页
      const offset = (page - 1) * limit;
      const paginatedRecords = records.slice(offset, offset + parseInt(limit));

      res.json({
        success: true,
        data: {
          records: paginatedRecords,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: records.length,
            totalPages: Math.ceil(records.length / limit)
          }
        }
      });

    } catch (error) {
      console.error('获取文件传输记录失败:', error);
      res.status(500).json({
        success: false,
        message: '获取传输记录失败: ' + error.message
      });
    }
  });

  // 文件上传API (原始文件第1168行) - 已添加用户隔离
  app.post('/api/file/upload', authenticateToken, userIsolationMiddleware, (req, res) => {
    const userId = req.currentUserId;

    // 确保用户专属目录存在
    const userUploadsDir = path.join(uploadsDir, `user_${userId}`);
    if (!fs.existsSync(userUploadsDir)) {
      fs.mkdirSync(userUploadsDir, { recursive: true });
    }

    // 配置用户专属的multer上传
    const userUpload = multer({
      storage: multer.diskStorage({
        destination: function (req, file, cb) {
          cb(null, userUploadsDir);
        },
        filename: function (req, file, cb) {
          const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
          cb(null, Date.now() + '-' + originalName);
        }
      }),
      limits: {
        fileSize: 100 * 1024 * 1024 // 100MB
      }
    });

    const uploadMultiple = userUpload.array('files', 10); // 最多10个文件

    uploadMultiple(req, res, (err) => {
      if (err) {
        console.error('文件上传错误:', err);
        return res.status(400).json({
          success: false,
          message: '文件上传失败: ' + err.message
        });
      }

      try {
        if (!req.files || req.files.length === 0) {
          return res.status(400).json({
            success: false,
            message: '没有上传文件'
          });
        }

        const uploadResults = [];

        req.files.forEach(file => {
          const fileInfo = {
            id: Date.now() + Math.random(),
            originalName: file.originalname,
            fileName: file.filename,
            size: file.size,
            mimetype: file.mimetype,
            path: file.path,
            uploadedAt: new Date(),
            userId: userId
          };

          fileStorage.set(fileInfo.id, fileInfo);

          uploadResults.push({
            id: fileInfo.id,
            originalName: file.originalname,
            fileName: file.filename,
            size: file.size,
            success: true,
            message: '上传成功'
          });

          console.log(`[用户${userId}] 文件已上传: ${file.originalname} -> ${file.filename}`);
        });

        res.json({
          success: true,
          message: `成功上传 ${req.files.length} 个文件`,
          data: {
            files: uploadResults,
            totalCount: req.files.length
          }
        });

      } catch (error) {
        console.error('处理文件上传失败:', error);
        res.status(500).json({
          success: false,
          message: '处理上传失败: ' + error.message
        });
      }
    });
  });

  // 删除文件API (原始文件第1183行) - 已添加用户隔离
  app.delete('/api/file/:filename', authenticateToken, userIsolationMiddleware, (req, res) => {
    try {
      const { filename } = req.params;
      const userId = req.currentUserId;
      const userUploadsDir = path.join(uploadsDir, `user_${userId}`);
      const filePath = path.join(userUploadsDir, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '文件不存在或无权访问'
        });
      }

      // 验证文件所属权
      let fileOwned = false;
      for (const [id, fileInfo] of fileStorage) {
        if (fileInfo.fileName === filename && fileInfo.userId === userId) {
          fileOwned = true;
          break;
        }
      }

      if (!fileOwned) {
        return res.status(403).json({
          success: false,
          message: '无权删除此文件'
        });
      }

      // 删除物理文件
      fs.unlinkSync(filePath);

      // 从存储中移除
      for (const [id, fileInfo] of fileStorage) {
        if (fileInfo.fileName === filename && fileInfo.userId === userId) {
          fileStorage.delete(id);
          break;
        }
      }

      console.log(`文件已删除: ${filename}`);

      res.json({
        success: true,
        message: '文件删除成功'
      });

    } catch (error) {
      console.error('删除文件失败:', error);
      res.status(500).json({
        success: false,
        message: '删除失败: ' + error.message
      });
    }
  });

  // 文件下载API (原始文件第1191行) - 已添加用户隔离
  app.get('/api/file/download/:filename', authenticateToken, userIsolationMiddleware, (req, res) => {
    try {
      const { filename } = req.params;
      const userId = req.currentUserId;
      const userUploadsDir = path.join(uploadsDir, `user_${userId}`);
      const filePath = path.join(userUploadsDir, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '文件不存在或无权访问'
        });
      }

      // 验证文件所属权
      let fileOwned = false;
      for (const [id, fileInfo] of fileStorage) {
        if (fileInfo.fileName === filename && fileInfo.userId === userId) {
          fileOwned = true;
          break;
        }
      }

      if (!fileOwned) {
        return res.status(403).json({
          success: false,
          message: '无权下载此文件'
        });
      }

      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // 设置响应头
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      res.setHeader('Content-Length', fileSize);
      res.setHeader('Content-Type', 'application/octet-stream');

      // 创建文件流并发送
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      console.log(`文件下载: ${filename}`);

    } catch (error) {
      console.error('文件下载失败:', error);
      res.status(500).json({
        success: false,
        message: '下载失败: ' + error.message
      });
    }
  });

  console.log('✅ 文件管理模块设置完成');

  // 返回文件管理相关函数供其他模块使用
  return {
    fileStorage,
    transferRecords,
    uploadsDir
  };
}

module.exports = { setupServerFile };
