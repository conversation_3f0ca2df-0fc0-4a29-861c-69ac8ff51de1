<template>
  <div class="uid-message-config">
    <el-form :model="config" label-width="120px">
      <!-- 功能说明 -->
      <el-form-item>
        <el-alert
          title="手动输入UID私信"
          type="info"
          :closable="false"
          show-icon
        >
          <div slot="description">
            <p>此功能支持手动输入UID列表进行私信</p>
            <p>请在下方输入框中输入要私信的UID，每行一个</p>
          </div>
        </el-alert>
      </el-form-item>

      <!-- 手动输入UID -->
      <el-form-item label="UID列表">
        <el-input
          v-model="config.uidList"
          type="textarea"
          :rows="6"
          placeholder="请输入UID，每行一个，例如：&#10;95018838961&#10;123456789&#10;987654321"
          @blur="onUidListInput"
          @change="onUidListInput"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          每行输入一个UID，支持批量输入
        </div>
      </el-form-item>

      <!-- 小红书应用选择 -->
      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <!-- 私信内容 -->
      <el-form-item label="私信内容">
        <el-input
          v-model="config.message"
          type="textarea"
          :rows="4"
          placeholder="请输入要发送的私信内容"
          maxlength="500"
          show-word-limit
          @blur="onInputChange"
          @change="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          建议私信内容简洁友好，避免过于商业化的语言
        </div>
      </el-form-item>

      <!-- 执行参数 -->
      <el-form-item label="执行参数">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="操作间隔(秒)">
              <el-input-number
                v-model="config.delay"
                :min="3"
                :max="30"
                @change="onInputChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大执行数量">
              <el-input-number
                v-model="config.maxCount"
                :min="1"
                :max="100"
                @change="onInputChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 高级选项 -->
      <el-form-item label="高级选项">
        <el-checkbox-group v-model="config.advancedOptions" @change="onInputChange">
          <el-checkbox label="enableDetailLog">启用详细日志</el-checkbox>
          <el-checkbox label="skipUsedUids">跳过已使用的UID</el-checkbox>
          <el-checkbox label="autoMarkUsed">自动标记UID为已使用</el-checkbox>
        </el-checkbox-group>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          • 启用详细日志：记录详细的执行过程<br>
          • 跳过已使用的UID：避免重复私信同一用户<br>
          • 自动标记UID为已使用：执行后自动标记UID状态
        </div>
      </el-form-item>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section" style="margin-bottom: 20px;">
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">实时状态</span>
        </el-divider>

        <div class="status-grid" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div class="status-item">
            <span class="status-label">已处理UID：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ processedUidCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">成功私信：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ successCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">失败私信：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ failedCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">已处理步骤：</span>
            <span class="status-value" style="color: #E6A23C; font-weight: bold;">{{ processedStepCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">搜索尝试次数：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ searchAttemptCount }}</span>
          </div>
        </div>

        <div class="current-status" style="margin-bottom: 15px;">
          <span class="status-label">当前状态：</span>
          <span class="status-value" style="color: #409EFF; font-weight: bold;">{{ currentStatus || '等待开始' }}</span>
        </div>
      </div>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
        >
          停止脚本
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未运行
        </span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import io from 'socket.io-client'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'UidMessageConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    deviceId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      config: {
        inputMode: 'manual', // 固定为手动输入模式
        uidList: '95018838961\n123456789\n987654321', // 手动输入的UID列表，提供默认示例
        message: '你好，很喜欢你的内容！',
        delay: 15,
        advancedOptions: ['enableDetailLog'],
        selectedApp: '' // 选择的小红书应用
      },
      currentLogId: null,
      currentTaskId: null,
      isInternalUpdate: false, // 标志位，防止循环更新

      // 实时状态变量
      processedUidCount: 0,
      successCount: 0,
      failedCount: 0,
      processedStepCount: 0,
      searchAttemptCount: 0,
      currentStatus: '等待开始',

      // Socket连接
      socket: null
    }
  },
  computed: {
    // 从Vuex获取脚本运行状态
    isScriptRunning() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('uidMessage')
      return functionState ? functionState.isScriptRunning : false
    },

    // 从Vuex获取脚本完成状态
    isScriptCompleted() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('uidMessage')
      return functionState ? functionState.isScriptCompleted : false
    },

    // 检查是否可以执行
    canExecute() {
      // 验证UID列表（手动输入模式）
      let hasValidUids = false
      if (typeof this.config.uidList === 'string') {
        const uidArray = this.config.uidList
          .split('\n')
          .map(uid => uid.trim())
          .filter(uid => uid.length > 0)
        hasValidUids = uidArray.length > 0
      } else if (Array.isArray(this.config.uidList)) {
        hasValidUids = this.config.uidList.length > 0
      }

      // 验证私信内容
      const hasValidMessage = this.config.message && 
                             this.config.message.trim().length > 0

      // 验证其他必要参数
      const hasValidDelay = this.config.delay && this.config.delay >= 3
      const hasValidMaxCount = this.config.maxCount && this.config.maxCount > 0

      console.log('[UID私信验证] 验证结果:', {
        hasValidUids,
        hasValidMessage,
        hasValidDelay,
        hasValidMaxCount,
        inputMode: this.config.inputMode,
        uidList: this.config.uidList
      })

      return hasValidUids && hasValidMessage && hasValidDelay && hasValidMaxCount
    }
  },
  watch: {
    value: {
      handler(newValue) {
        // 如果是内部更新触发的，跳过处理
        if (this.isInternalUpdate) {
          return
        }

        if (newValue && typeof newValue === 'object') {
          const processedValue = { ...newValue }

          // 确保uidList是字符串类型（用于el-input）
          if (processedValue.uidList) {
            if (Array.isArray(processedValue.uidList)) {
              // 如果是数组，转换为字符串
              processedValue.uidList = processedValue.uidList.join('\n')
            } else if (typeof processedValue.uidList !== 'string') {
              // 如果不是字符串也不是数组，转换为字符串
              processedValue.uidList = String(processedValue.uidList)
            }
          }

          this.config = { ...this.config, ...processedValue }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.loadConfig()

    // 如果有初始值，使用初始值
    if (this.value && Object.keys(this.value).length > 0) {
      const processedValue = { ...this.value }

      // 确保uidList是字符串类型（用于el-input）
      if (processedValue.uidList) {
        if (Array.isArray(processedValue.uidList)) {
          // 如果是数组，转换为字符串
          processedValue.uidList = processedValue.uidList.join('\n')
        } else if (typeof processedValue.uidList !== 'string') {
          // 如果不是字符串也不是数组，转换为字符串
          processedValue.uidList = String(processedValue.uidList)
        }
      }

      this.config = { ...this.config, ...processedValue }
    }

    // 恢复组件状态（异步）
    this.restoreComponentState().then(() => {
      console.log('[UidMessageConfig] 状态恢复完成，开始初始化Socket连接')
      // 初始化Socket连接
      this.initializeSocket()
    }).catch(error => {
      console.error('[UidMessageConfig] 状态恢复失败:', error)
      // 即使恢复失败也要初始化Socket连接
      this.initializeSocket()
    })

    // 发出初始的输入模式给父组件
    this.$emit('input-mode-change', this.config.inputMode)

    // 监听任务恢复事件
    this.$root.$on('xiaohongshu-task-restored', this.handleTaskRestored)

    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', (data) => {
      console.log('[UidMessageConfig] 收到任务开始事件:', data)
      if (data.functionType === 'uidMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[UidMessageConfig] UID私信任务开始，更新Vuex状态')

        // 更新Vuex状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'uidMessage',
          stateData: {
            isScriptRunning: true,
            isScriptCompleted: false,
            config: this.config
          }
        })

        // 保存当前执行的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[UidMessageConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[UidMessageConfig] 保存taskId:', this.currentTaskId)
        }

        console.log('[UidMessageConfig] 状态已更新:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          currentLogId: this.currentLogId,
          currentTaskId: this.currentTaskId
        })
      } else {
        console.log('[UidMessageConfig] 任务开始事件不匹配，忽略')
      }
    })

    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', (data) => {
      console.log('[UidMessageConfig] 收到任务停止事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = functionType === 'uidMessage' && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[UidMessageConfig] UID私信任务停止，原因: ${reason}`)

        // 更新Vuex状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'uidMessage',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })

        // 清空保存的ID
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[UidMessageConfig] 已清空保存的logId和taskId')

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('手动输入UID私信功能已被批量停止')
        }
      }
    })

    // 监听脚本完成事件
    this.$root.$on('xiaohongshu-script-completed', (data) => {
      console.log('[UidMessageConfig] 收到脚本完成事件:', data)
      if (data.functionType === 'uidMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[UidMessageConfig] UID私信脚本完成，状态:', data.status)

        // 根据不同的状态设置不同的Vuex状态
        if (data.status === 'stopped') {
          // 手动停止
          console.log('[UidMessageConfig] UID私信脚本被手动停止')
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'uidMessage',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: false,
              config: this.config
            }
          })
        } else if (data.status === 'success') {
          // 成功完成
          console.log('[UidMessageConfig] UID私信脚本成功完成')
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'uidMessage',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: true,
              config: this.config
            }
          })

          // 1分钟后重置完成状态
          setTimeout(() => {
            this.$store.dispatch('xiaohongshu/setFunctionState', {
              functionType: 'uidMessage',
              stateData: {
                isScriptRunning: false,
                isScriptCompleted: false,
                config: this.config
              }
            })
          }, 60000)
        } else if (data.status === 'failed') {
          // 执行失败
          console.log('[UidMessageConfig] UID私信脚本执行失败')
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'uidMessage',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: false,
              config: this.config
            }
          })
        }

        // 清空保存的ID
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[UidMessageConfig] 已清空保存的logId和taskId')
      }
    })

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      if (data.deviceId === this.deviceId) {
        console.log('[UidMessageConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })
  },

  beforeDestroy() {
    // 保存组件状态
    this.saveComponentState()

    // 断开socket连接
    if (this.socket) {
      this.socket.disconnect()
      console.log('[UidMessageConfig] Socket连接已断开')
    }

    // 清理事件监听
    this.$root.$off('xiaohongshu-task-started')
    this.$root.$off('xiaohongshu-task-stopped')
    this.$root.$off('xiaohongshu-script-completed')
    this.$root.$off('xiaohongshu-task-restored', this.handleTaskRestored)
    this.$root.$off('device-offline')
  },
  methods: {
    onInputChange() {
      console.log('=== UID私信配置变更开始 ===')
      console.log('当前配置:', JSON.stringify(this.config, null, 2))

      // 处理UID列表格式（手动输入模式）
      const processedConfig = {
        ...this.config,
        inputMode: 'manual' // 确保始终为手动输入模式
      }

      if (typeof this.config.uidList === 'string') {
        // 将文本转换为数组用于验证和后端处理
        const uidArray = this.config.uidList
          .split('\n')
          .map(uid => uid.trim())
          .filter(uid => uid.length > 0)

        console.log('手动输入模式 - 原始文本:', this.config.uidList)
        console.log('手动输入模式 - 转换后数组:', uidArray)

        // 如果UID列表为空，提供警告
        if (uidArray.length === 0) {
          console.warn('警告：UID列表为空，请输入至少一个UID')
          this.$message.warning('请输入至少一个UID')
          return // 不发送空配置
        }

        // 只在发送给后端时使用数组格式，组件内部保持字符串格式
        processedConfig.uidList = uidArray
      }

      // 验证私信内容
      if (!processedConfig.message || processedConfig.message.trim().length === 0) {
        console.warn('警告：私信内容为空')
        this.$message.warning('请输入私信内容')
        return // 不发送空配置
      }

      // 处理高级选项
      if (this.config.advancedOptions) {
        processedConfig.enableDetailLog = this.config.advancedOptions.includes('enableDetailLog')
        processedConfig.skipUsedUids = this.config.advancedOptions.includes('skipUsedUids')
        processedConfig.autoMarkUsed = this.config.advancedOptions.includes('autoMarkUsed')
      }

      console.log('最终处理后的配置:', JSON.stringify(processedConfig, null, 2))
      console.log('发送update事件...')

      // 设置内部更新标志，防止watch循环触发
      this.isInternalUpdate = true
      this.$emit('input', processedConfig)
      this.$emit('update', processedConfig)
      this.saveConfig()

      // 下一个tick后重置标志
      this.$nextTick(() => {
        this.isInternalUpdate = false
      })

      console.log('=== UID私信配置变更结束 ===')
    },

    // 专门处理UID列表输入
    onUidListInput() {
      console.log('UID列表输入变化:', this.config.uidList)
      this.onInputChange()
    },

    // 停止脚本
    stopScript() {
      console.log('[UidMessageConfig] 停止脚本')
      
      // 发送停止事件
      this.$root.$emit('xiaohongshu-stop-script', {
        functionType: 'uidMessage',
        deviceId: this.deviceId,
        logId: this.currentLogId,
        taskId: this.currentTaskId
      })

      // 更新Vuex状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'uidMessage',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      this.$message.success('已发送停止脚本命令')
    },

    // 保存配置到本地存储
    saveConfig() {
      try {
        localStorage.setItem('uidMessageConfig', JSON.stringify(this.config))
      } catch (error) {
        console.error('保存配置失败:', error)
      }
    },

    // 加载配置从本地存储
    loadConfig() {
      try {
        const savedConfig = localStorage.getItem('uidMessageConfig')
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig)

          // 确保uidList是字符串类型（用于el-input）
          if (parsedConfig.uidList) {
            if (Array.isArray(parsedConfig.uidList)) {
              // 如果是数组，转换为字符串
              parsedConfig.uidList = parsedConfig.uidList.join('\n')
            } else if (typeof parsedConfig.uidList !== 'string') {
              // 如果不是字符串也不是数组，转换为字符串
              parsedConfig.uidList = String(parsedConfig.uidList)
            }
          }

          this.config = { ...this.config, ...parsedConfig }
        }
      } catch (error) {
        console.error('加载配置失败:', error)
      }
    },

    // 保存组件状态
    async saveComponentState() {
      const { saveComponentState } = await import('@/utils/stateManager')

      const stateData = {
        config: this.config,
        currentLogId: this.currentLogId,
        currentTaskId: this.currentTaskId,
        isScriptRunning: this.isScriptRunning,
        isScriptCompleted: this.isScriptCompleted,
        // 保存实时状态数据
        realtimeData: {
          processedUidCount: this.processedUidCount,
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        }
      }

      await saveComponentState(this, 'uidMessage', stateData)
      console.log('[UidMessageConfig] 组件状态已保存，currentTaskId:', this.currentTaskId)
    },

    // 恢复组件状态
    async restoreComponentState() {
      try {
        const { restoreComponentState } = await import('@/utils/stateManager')
        const functionState = await restoreComponentState(this, 'uidMessage')

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[UidMessageConfig] 恢复组件状态:', functionState)

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复任务信息
          this.currentLogId = functionState.currentLogId || null
          this.currentTaskId = functionState.currentTaskId || null
          this.isScriptRunning = functionState.isScriptRunning || false
          this.isScriptCompleted = functionState.isScriptCompleted || false

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.processedUidCount = functionState.realtimeData.processedUidCount || 0
            this.successCount = functionState.realtimeData.successCount || 0
            this.failedCount = functionState.realtimeData.failedCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.searchAttemptCount = functionState.realtimeData.searchAttemptCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[UidMessageConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          console.log('[UidMessageConfig] 组件状态已恢复，currentTaskId:', this.currentTaskId)
        } else {
          console.log('[UidMessageConfig] 没有找到保存的状态，使用默认值')
          // 确保初始化默认值
          this.currentLogId = null
          this.currentTaskId = null
          this.isScriptRunning = false
          this.isScriptCompleted = false
        }
      } catch (error) {
        console.error('[UidMessageConfig] 恢复组件状态失败:', error)
      }
    },

    // 处理任务恢复事件
    handleTaskRestored(data) {
      console.log('[UidMessageConfig] 收到任务恢复事件:', data)
      if (data.functionType === 'uidMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        // 恢复任务状态
        this.currentLogId = data.logId
        this.currentTaskId = data.taskId
        
        // 更新Vuex状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'uidMessage',
          stateData: {
            isScriptRunning: true,
            isScriptCompleted: false,
            config: this.config
          }
        })
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [UidMessageConfig] 收到实时状态数据:', data)
      console.log('📋 [UidMessageConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [UidMessageConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [UidMessageConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [UidMessageConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.processedUidCount !== undefined) {
          this.processedUidCount = data.processedUidCount
          console.log('📊 [UidMessageConfig] 更新已处理UID数:', this.processedUidCount)
        }
        if (data.successCount !== undefined) {
          this.successCount = data.successCount
          console.log('📊 [UidMessageConfig] 更新成功私信数:', this.successCount)
        }
        if (data.failedCount !== undefined) {
          this.failedCount = data.failedCount
          console.log('📊 [UidMessageConfig] 更新失败私信数:', this.failedCount)
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
          console.log('📊 [UidMessageConfig] 更新已处理步骤数:', this.processedStepCount)
        }
        if (data.searchAttemptCount !== undefined) {
          this.searchAttemptCount = data.searchAttemptCount
          console.log('📊 [UidMessageConfig] 更新搜索尝试次数:', this.searchAttemptCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [UidMessageConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [UidMessageConfig] 实时状态已更新:', {
          processedUidCount: this.processedUidCount,
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [UidMessageConfig] 已强制更新视图')
      } else {
        console.log('❌ [UidMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.processedUidCount = 0
      this.successCount = 0
      this.failedCount = 0
      this.processedStepCount = 0
      this.searchAttemptCount = 0
      this.currentStatus = '等待开始'
      console.log('[UidMessageConfig] 实时状态已重置')
    },

    // 初始化Socket连接
    initializeSocket() {
      // 使用工具函数获取服务器地址
      const { getWebSocketUrl } = require('@/utils/serverConfig')
      // 只使用WebSocket传输，不使用长轮询
      this.socket = io(getWebSocketUrl(), {
        transports: ['websocket'], // 只使用WebSocket，不降级到polling
        upgrade: false // 禁用传输升级
      })

      this.socket.on('connect', () => {
        console.log('✅ [UidMessageConfig] Socket连接成功')
        // 获取当前用户信息
        const user = this.$store.getters['auth/user']
        const userId = user ? user.id : 'anonymous'
        this.socket.emit('web_client_connect', {
          userId: userId,
          username: user ? user.username : 'anonymous',
          clientType: 'uid_message_config'
        })
        console.log('📡 [UidMessageConfig] 已注册为用户', userId, '的客户端')
      })

      this.socket.on('disconnect', () => {
        console.log('❌ [UidMessageConfig] Socket连接断开')
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [UidMessageConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      console.log('[UidMessageConfig] Socket初始化完成')
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[UidMessageConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'uidMessage',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[UidMessageConfig] 设备离线处理完成')
    }
  }
}
</script>

<style scoped>
.uid-message-config {
  padding: 20px;
}
</style>
